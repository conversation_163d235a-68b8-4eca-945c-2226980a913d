# 项目简介
## 代码对应
```
APP.css   APP.jsx样式

APP.jsx   路由管理

index.css  全局样式

main.jsx   入口文件

components/ui/button.jsx 按钮组件

components/Chat.jsx 头部右侧聊天功能

components/CloudStorage.jsx 头部右侧云盘功能

components/KnowledgeBase.jsx 头部右侧知识库功能

components/MainContent.jsx 项目-项目管理界面

components/MinimizedChat.jsx 聊天界面最小化

components/NotificationDropdown.jsx 头部右侧通知功能

components/Sidebar.jsx 左侧一级菜单下的二级菜单

components/SystemSidebar.jsx 左侧一级菜单

components/SystemToolbar.jsx 头部右侧用户界面

pages/Addproject.jsx 项目-项目管理-项目创建界面

pages/BudgetManagement.jsx 项目-预算成本界面

pages/ChangePassword.jsx 修改密码界面

pages/CICD研发-研发管理-开发管理-CICD界面

pages/CodeReview.jsx  研发-研发管理-设计管理-方案评审界面

pages/ConfigurationBaseline.jsx 研发-研发管理-需求管理-配置基线界面

pages/DesignBaseline.jsx 研发-研发管理-设计方案-设计基线界面

pages/DesignInput.jsx 研发-研发管理-项目策划-设计输入界面

pages/DesignProposal.jsx 研发-研发管理-设计管理-方案设计界面

pages/DesignReview.jsx 研发-研发管理-设计管理-方案评审界面

pages/HRManagement.jsx 人事-人事管理-人员管理界面

pages/InputReview.jsx 研发-研发管理-项目策划-输入评审界面

pages/ProjectImport.jsx 项目-项目管理-项目输入界面

pages/Login.jsx 登录界面

pages/MergeRequests.jsx 研发-研发管理-开发管理-合并请求

pages/OrganizationStructure.jsx 人事-人事管理-组织架构界面

pages/ProjectInput.jsx 项目-项目管理-项目创建界面

pages/ProjectManagement.jsx 项目-项目管理-预算成本界面

pages/ProjectOutput.jsx 项目-项目管理-项目输出

pages/ProjectProgress.jsx 项目-项目管理-进度管理

pages/Repository.jsx 研发-研发管理-开发管理-代码库

pages/RequirementsBreakdown.jsx 研发-研发管理-需求管理-需求分解

pages/ResourceManagement.jsx 项目-项目管理-资源管理

pages/RiskManagement.jsx 项目-项目管理-风险管理

pages/UserSettings.jsx 用户设置界面

store/chatStore.js 聊天功能

store/fileStore.js 配置项目文件

store/knowledgeStore.js 配置知识库

store/navigationStore.js 设置一级导航初始二级导航

store/notificationStore.js 配置通知功能

store/userStore.js 配置用户存储

pages/Submitcode.jsx 研发-研发管理-开发管理-代码评审界面

pages/DevBaseline.jsx 研发-研发管理-开发管理-开发基线界面

pages/TestPlan.jsx 研发-研发管理-测试-测试计划界面

pages/TestCase.jsx 研发-研发管理-测试-测试用例界面

pages/TestResult.jsx 研发-研发管理-测试-测试结果界面

pages/TestBaseline.jsx 研发-研发管理-开发管理-测试基线界面

pages/IntegrationTest.jsx 研发-研发管理-集成-集成测试界面

pages/IntegrationBaseline.jsx 研发-研发管理-集成-集成基线界面

pages/ProjectClosure.jsx 项目-项目管理-项目结项界面

pages/ProjectDetail.jsx 项目-项目管理-项目详情界面

pages/ProductAcceptance.jsx 项目-项目管理-产品验收界面

pages/ProductDelivery.jsx 项目-项目管理-成果交付界面


```