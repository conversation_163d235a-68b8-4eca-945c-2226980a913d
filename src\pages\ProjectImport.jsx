import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '../components/ui/button';
import {
  MagnifyingGlassIcon,
  Cross2Icon,
  PlusIcon,
  Pencil1Icon,
  TrashIcon,
  FileTextIcon,
  EyeOpenIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { useNavigate } from 'react-router-dom';
import { projectApi, projectInputApi, fileApi, employeeApi } from '../services/projectService';

/**
 * 项目导入管理组件
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 项目输入文件的增删改查
 * 3. 文件上传、预览、下载、打包
 * 4. 审批流程管理
 */
export const ProjectImport = observer(() => {
  const navigate = useNavigate();

  // ========== 项目相关状态 ==========
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [projects, setProjects] = useState([]); // 项目列表
  const [projectSearchQuery, setProjectSearchQuery] = useState(''); // 项目搜索关键词
  const [projectLoading, setProjectLoading] = useState(false); // 项目加载状态
  const [projectError, setProjectError] = useState(null); // 项目错误信息

  // ========== 文件相关状态 ==========
  const [projectFiles, setProjectFiles] = useState([]); // 项目文件列表
  const [fileLoading, setFileLoading] = useState(false); // 文件加载状态
  const [fileSearchQuery, setFileSearchQuery] = useState(''); // 文件搜索关键词
  const [uploadFiles, setUploadFiles] = useState([]); // 待上传文件列表
  const [editUploadFiles, setEditUploadFiles] = useState([]); // 编辑时的文件列表
  const [archiveLoading, setArchiveLoading] = useState({}); // 打包下载加载状态

  // ========== 模态框状态 ==========
  const [showUploadModal, setShowUploadModal] = useState(false); // 上传文件模态框
  const [showDetailModal, setShowDetailModal] = useState(false); // 查看详情模态框
  const [showEditModal, setShowEditModal] = useState(false); // 编辑模态框
  const [showAddModal, setShowAddModal] = useState(false); // 添加模态框
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false); // 删除确认对话框

  // ========== 表单数据状态 ==========
  const [editingFile, setEditingFile] = useState(null); // 当前编辑的文件
  const [fileToDelete, setFileToDelete] = useState(null); // 待删除的文件ID
  const [formData, setFormData] = useState({ // 添加表单数据
    name: '',
    number: '',
    payDate: '',
    description: '',
    phone: ''
  });
  const [uploadFormData, setUploadFormData] = useState({ // 上传表单数据
    name: '',
    approver: '',
    description: '',
    status: 'DRAFT',
    file: null,
    uploadTime: new Date().toISOString().split('T')[0]
  });

  // ========== 审批相关状态 ==========
  const [approvers, setApprovers] = useState([]); // 审批人列表
  const [loadingApprovers, setLoadingApprovers] = useState(false); // 审批人加载状态
  const [isApproverDropdownOpen, setIsApproverDropdownOpen] = useState(false); // 审批人下拉框状态
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false); // 状态下拉框状态

  // ========== 其他状态 ==========
  const [searchTimeout, setSearchTimeout] = useState(null); // 搜索防抖定时器
  const [phoneError, setPhoneError] = useState(''); // 电话验证错误信息

  // ========== DOM引用 ==========
  const approverDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const editApproverDropdownRef = useRef(null);

  // ========== 常量定义 ==========
  const phoneRegex = /^1[3-9]\d{9}$/; // 电话号码验证正则

  // ========== 常量配置 ==========
  const statusOptions = [
    { value: 'DRAFT', label: '草稿' },
    { value: 'PENDING', label: '待审批' },
    { value: 'APPROVED', label: '已通过' },
    { value: 'REJECTED', label: '已拒绝' }
  ];



  // ========== 工具函数 ==========
  // 显示消息提示（统一的消息提示函数）
  const showMessage = (message, type = 'error') => {
    const messageDiv = document.createElement('div');
    const isError = type === 'error';
    const iconColor = isError ? 'text-red-500' : 'text-green-500';
    const textColor = isError ? 'text-red-500' : 'text-gray-700';

    messageDiv.className = 'fixed top-4 left-1/2 -translate-x-1/2 bg-white rounded px-3 py-1.5 shadow-md flex items-center z-50';
    messageDiv.innerHTML = `
      <svg class="w-4 h-4 mr-1.5 ${iconColor}" viewBox="0 0 24 24" fill="currentColor">
        ${isError
          ? '<circle cx="12" cy="12" r="10" /><path d="M12 7v6M12 15v2" stroke="white" stroke-width="2"/>'
          : '<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>'
        }
      </svg>
      <span class="${textColor}">${message}</span>
    `;
    document.body.appendChild(messageDiv);
    setTimeout(() => messageDiv.remove(), 3000);
  };

  // 电话号码验证
  const validatePhone = (phone) => {
    if (!phone) {
      setPhoneError('请输入联系电话');
      return false;
    }
    if (!phoneRegex.test(phone)) {
      setPhoneError('请输入正确的11位手机号码');
      return false;
    }
    setPhoneError('');
    return true;
  };

  // ========== 事件处理 ==========
  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      const refs = [approverDropdownRef, statusDropdownRef, editApproverDropdownRef];
      refs.forEach(ref => {
        if (ref.current && !ref.current.contains(event.target)) {
          setIsApproverDropdownOpen(false);
          setIsStatusDropdownOpen(false);
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // ========== API调用函数 ==========
  // 获取项目列表
  const fetchProjects = async () => {
    setProjectLoading(true);
    try {
      const data = await projectApi.getProjectList();
      const projectsArray = Array.isArray(data) ? data : data.data || [];
      setProjects(projectsArray);

      // 自动选择第一个项目
      if (projectsArray.length > 0 && !selectedProject) {
        setSelectedProject(projectsArray[0]);
      }
    } catch (err) {
      console.error('获取项目列表错误:', err);
      setProjectError(err.message);
      setProjects([]);
    } finally {
      setProjectLoading(false);
    }
  };

  // 获取项目文件列表
  const fetchProjectFiles = async (projectId) => {
    if (!projectId) return;
    setFileLoading(true);
    try {
      const data = await projectInputApi.getProjectInputList(projectId);
      setProjectFiles(data);
    } catch (err) {
      console.error('获取项目输入数据错误:', err);
      showMessage(err.message || '获取项目输入数据失败');
    } finally {
      setFileLoading(false);
    }
  };

  // 获取审核人列表
  const fetchApprovers = async () => {
    try {
      setLoadingApprovers(true);
      const data = await employeeApi.getEmployeeList();
      setApprovers(data);
    } catch (err) {
      console.error('获取审核人列表错误:', err);
      showMessage(err.message || '获取审核人列表失败');
    } finally {
      setLoadingApprovers(false);
    }
  };

  // ========== 生命周期钩子 ==========
  useEffect(() => {
    fetchProjects();
    fetchApprovers();
  }, []);

  // 当选择项目变化时，获取项目文件
  useEffect(() => {
    if (selectedProject) {
      fetchProjectFiles(selectedProject.id);
    } else {
      setProjectFiles([]);
    }
  }, [selectedProject]);

  // ========== 业务处理函数 ==========
  // 文件上传处理
  const handleFileUpload = async () => {
    // 表单验证
    if (!uploadFormData.name || !uploadFormData.uploadTime || !uploadFormData.approver) {
      showMessage('请填写必填项（文件名、上传时间、审核人）');
      return;
    }
    if (!selectedProject) {
      showMessage('请先选择项目');
      return;
    }
    if (uploadFiles.length === 0) {
      showMessage('请选择要上传的文件');
      return;
    }

    try {
      // 构建项目输入对象
      const projectIn = {
        id: 0,
        name: uploadFormData.name,
        number: '',
        payDate: uploadFormData.uploadTime,
        approverId: approvers.find(a => a.name === uploadFormData.approver)?.id,
        approvalComment: '',
        status: uploadFormData.status,
        description: uploadFormData.description,
        projectId: selectedProject.id,
        approverName: uploadFormData.approver,
        bucketName: 'projectin'
      };

      // 构建表单数据
      const formData = new FormData();
      formData.append('projectIn', new Blob([JSON.stringify(projectIn)], {
        type: 'application/json'
      }));
      uploadFiles.forEach(file => formData.append('files', file));

      await projectInputApi.createProjectInput(formData);

      // 重置表单状态
      resetUploadForm();
      showMessage('上传成功', 'success');
      fetchProjectFiles(selectedProject.id);
    } catch (err) {
      console.error('创建错误:', err);
      showMessage(err.message || '创建输入文件记录失败');
    }
  };

  // 重置上传表单
  const resetUploadForm = () => {
    setUploadFormData({
      name: '',
      approver: '',
      description: '',
      status: 'DRAFT',
      file: null,
      uploadTime: new Date().toISOString().split('T')[0]
    });
    setUploadFiles([]);
    setShowUploadModal(false);
  };

  // 文件操作处理
  const handleRemoveFile = (index) => {
    setUploadFiles(files => files.filter((_, i) => i !== index));
  };

  // 项目搜索处理（带防抖）
  const handleProjectSearchChange = (value) => {
    setProjectSearchQuery(value);

    // 清除之前的定时器，实现防抖
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // 延迟300ms执行搜索
    const timeoutId = setTimeout(async () => {
      setProjectLoading(true);
      try {
        const data = await projectApi.getProjectList();
        const projectsArray = Array.isArray(data) ? data : data.data || [];

        // 根据搜索关键词过滤项目
        if (value) {
          const filteredData = projectsArray.filter(project => {
            const projectName = project?.name?.toLowerCase() || '';
            const managerName = project?.managerName?.toLowerCase() || '';
            const searchValue = value.toLowerCase();
            return projectName.includes(searchValue) || managerName.includes(searchValue);
          });
          setProjects(filteredData);
        } else {
          setProjects(projectsArray);
        }
      } catch (err) {
        console.error('搜索项目错误:', err);
        setProjectError(err.message);
        setProjects([]);
      } finally {
        setProjectLoading(false);
      }
    }, 300);

    setSearchTimeout(timeoutId);
  };

  // 添加新输入处理
  const handleAdd = async () => {
    if (!validatePhone(formData.phone)) {
      return;
    }

    try {
      await projectInputApi.createProjectInput({
        ...formData,
        projectId: selectedProject.id,
        status: 'DRAFT'
      });

      // 重置表单并刷新列表
      await fetchProjectFiles(selectedProject.id);
      setShowAddModal(false);
      setFormData({ name: '', number: '', payDate: '', description: '', phone: '' });
      showMessage('添加成功', 'success');
    } catch (err) {
      console.error('添加输入错误:', err);
      showMessage(err.message || '添加输入失败');
    }
  };

  // 删除输入处理
  const handleDelete = (id) => {
    setFileToDelete(id);
    setShowDeleteConfirm(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    if (!fileToDelete) return;

    try {
      const result = await projectInputApi.deleteProjectInput(fileToDelete);
      if (result === true) {
        await fetchProjectFiles(selectedProject.id);
        setShowDeleteConfirm(false);
        setFileToDelete(null);
        showMessage('删除成功', 'success');
      } else {
        showMessage('删除失败');
      }
    } catch (err) {
      console.error('删除输入错误:', err);
      showMessage('删除输入失败');
    }
  };

  // 修改 FormFields 组件
  const FormFields = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700">名称</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">编号</label>
        <input
          type="text"
          value={formData.number}
          onChange={(e) => setFormData({ ...formData, number: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">上传日期</label>
        <div className="relative">
          <input
            type="date"
            value={formData.payDate}
            onChange={(e) => setFormData({ ...formData, payDate: e.target.value })}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 cursor-pointer"
          />
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">描述</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2"
          rows="3"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700">
          联系电话 <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          value={formData.phone}
          onChange={(e) => {
            const value = e.target.value.replace(/\D/g, '').slice(0, 11);
            setFormData({ ...formData, phone: value });
            if (value.length === 11) {
              validatePhone(value);
            }
          }}
          className={`mt-1 block w-full rounded-md border ${
            phoneError ? 'border-red-500' : 'border-gray-300'
          } px-3 py-2`}
          placeholder="请输入11位手机号码"
        />
        {phoneError && (
          <p className="mt-1 text-sm text-red-500">{phoneError}</p>
        )}
      </div>
    </div>
  );

  // 获取输入详情（支持查看和编辑两种模式）
  const fetchInputDetail = async (id, type = 'view') => {
    try {
      const data = await projectInputApi.getProjectInputDetail(id);

      if (type === 'view') {
        setEditingFile(data);
        setShowDetailModal(true);
      } else if (type === 'edit') {
        const matchedApprover = approvers.find(a => a.id === parseInt(data.approverId));
        setEditingFile({
          ...data,
          approverId: parseInt(data.approverId),
          approverName: matchedApprover?.name || '',
          status: data.status || 'DRAFT',
          approvalComment: data.approvalComment || '',
          description: data.description || ''
        });
        setShowEditModal(true);
      }
    } catch (err) {
      console.error('获取输入详情错误:', err);
      showMessage(err.message || '获取输入详情失败');
    }
  };

  // 编辑输入处理
  const handleEdit = async () => {
    if (!editingFile.name || !editingFile.approverName) {
      showMessage('请填写必填项（文件名、审核人）');
      return;
    }

    try {
      // 构建项目输入对象
      const projectIn = {
        id: editingFile.id,
        name: editingFile.name,
        number: editingFile.number || '',
        payDate: editingFile.payDate,
        approverId: approvers.find(a => a.name === editingFile.approverName)?.id,
        approvalComment: editingFile.approvalComment || '',
        status: editingFile.status,
        description: editingFile.description || '',
        projectId: selectedProject.id,
        approverName: editingFile.approverName,
        bucketName: 'projectin',
        files: editingFile.files ? editingFile.files.map(file => ({
          id: file.id,
          name: file.name,
          path: file.path || '',
          type: file.type || 0,
          size: file.size || 0,
          otherId: file.otherId || 0,
          uploadTime: file.uploadTime || new Date().toISOString(),
          uploaderId: file.uploaderId || 0,
          description: file.description || '',
          module: file.module || ''
        })) : []
      };

      // 构建表单数据
      const formData = new FormData();
      formData.append('projectIn', new Blob([JSON.stringify(projectIn)], {
        type: 'application/json'
      }));
      editUploadFiles.forEach(file => formData.append('files', file));

      await projectInputApi.updateProjectInput(editingFile.id, formData);

      // 重置状态并刷新列表
      setEditingFile(null);
      setEditUploadFiles([]);
      setShowEditModal(false);
      fetchProjectFiles(selectedProject.id);
      showMessage('更新成功', 'success');
    } catch (err) {
      console.error('更新错误:', err);
      showMessage(err.message || '更新输入文件记录失败');
    }
  };

  // 文件搜索处理
  const handleFileSearch = async () => {
    if (!selectedProject) return;
    setFileLoading(true);

    try {
      const data = await projectInputApi.searchProjectInput(selectedProject.id, fileSearchQuery);
      setProjectFiles(data);
    } catch (err) {
      console.error('搜索文件错误:', err);
      showMessage(err.message || '搜索文件失败');
    } finally {
      setFileLoading(false);
    }
  };



  // 重置搜索
  const handleReset = async () => {
    setFileSearchQuery('');
    if (!selectedProject) return;

    setFileLoading(true);
    try {
      const data = await projectInputApi.getProjectInputList(selectedProject.id);
      setProjectFiles(data);
    } catch (err) {
      console.error('重置搜索错误:', err);
      showMessage(err.message || '获取文件列表失败');
    } finally {
      setFileLoading(false);
    }
  };

  // 打包下载处理
  const handleArchiveDownload = async (fileId, fileName) => {
    try {
      setArchiveLoading(prev => ({ ...prev, [fileId]: true }));
      const blob = await projectInputApi.archiveProjectInput(fileId);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${fileName}-文件包.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showMessage('打包成功', 'success');
    } catch (error) {
      console.error('打包文件错误:', error);
      showMessage('打包文件失败');
    } finally {
      setArchiveLoading(prev => ({ ...prev, [fileId]: false }));
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex">
      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-[calc(100vh-80px)]">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="flex gap-2">

            {/* 搜索部分 */}
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="搜索项目..."
                value={projectSearchQuery}
                onChange={(e) => handleProjectSearchChange(e.target.value)}
                className="w-full pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projectLoading ? (
            <div className="p-4 text-center text-gray-500">加载中...</div>
          ) : projectError ? (
            <div className="p-4 text-center text-red-500">{projectError}</div>
          ) : projects.length === 0 ? (
            <div className="p-4 text-center text-gray-500">暂无项目数据</div>
          ) : (
            projects.map(project => {
              return (
                <div
                  key={project.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedProject?.id === project.id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => setSelectedProject(project)}
                >
                  <div className="font-medium text-gray-900">{project.name}</div>
                  <div className={`text-sm mt-1 px-2 py-0.5 inline-block rounded ${
                    project.status === 0 ? 'text-gray-600 bg-gray-100' :
                    project.status === 1 ? 'text-blue-600 bg-blue-50' :
                    project.status === 2 ? 'text-green-600 bg-green-50' : ''
                  }`}>
                    {project.status === 0 ? '未开始' :
                     project.status === 1 ? '进行中' :
                     project.status === 2 ? '已结束' : '未知状态'}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* 右侧文件列表 - 修改布局结构 */}
      <div className="flex-1">
        {/* 标题和搜索框部分 */}
        <div className="mb-6">
          {/* 标题 */}
          <h2 className="text-xl font-semibold mb-4">
            {selectedProject ? `${selectedProject.name} - 输入文件` : '项目输入文件'}
          </h2>

          {/* 搜索和操作按钮行 */}
          <div className="flex items-center gap-2">
            {/* 搜索框 */}
            {selectedProject && (
              <div className="flex-1 max-w-md">
                <input
                  type="text"
                  value={fileSearchQuery}
                  onChange={(e) => setFileSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleFileSearch()}
                  placeholder="搜索文件名称..."
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}

            {/* 所有按钮组合在一起 */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleFileSearch}
                className="flex items-center gap-1"
              >
                <MagnifyingGlassIcon className="w-4 h-4" />
                搜索
              </Button>

              <Button
                variant="outline"
                onClick={handleReset}
                disabled={!selectedProject}
              >
                重置
              </Button>

              {/* 全量打包按钮 */}
              <Button
                variant="outline"
                className="flex items-center gap-1"
                disabled={!selectedProject || projectFiles.length === 0}
                onClick={async () => {
                  try {
                    const blob = await fileApi.archiveAll(3);
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `${selectedProject.name}-文件集.zip`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    showMessage('下载成功', 'success');
                  } catch (error) {
                    console.error('全量打包文件错误:', error);
                    showMessage('全量打包文件失败');
                  }
                }}
              >
                <DownloadIcon className="w-4 h-4" />
                全量打包
              </Button>

              {/* 上传按钮 */}
              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white"
                disabled={!selectedProject}
                onClick={() => setShowUploadModal(true)}
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                新增项目输入
              </Button>
            </div>
          </div>
        </div>

        {/* 文件列表内容 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          {!selectedProject ? (
            <div className="text-center text-gray-500">请先选择一个项目</div>
          ) : fileLoading ? (
            <div className="text-center text-gray-500">加载中...</div>
          ) : projectFiles.length === 0 ? (
            <div className="text-center text-gray-500">暂无文件数据</div>
          ) : (
            <div className="grid grid-cols-3 gap-6 max-h-[calc(100vh-280px)] overflow-y-auto">
              {projectFiles.map(file => (
                <div
                  key={file.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  {/* 文件图标 */}
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                      <FileTextIcon className="w-8 h-8 text-gray-400" />
                    </div>
                  </div>

                  {/* 输入信息 */}
                  <div className="space-y-2">
                    {/* 名称和编号 */}
                    <div className="font-medium truncate" title={file.name}>
                      {file.name}
                    </div>
                    {/* <div className="text-sm text-gray-500 truncate">
                      编号: {file.number}
                    </div> */}

                    {/* 审批信息 */}
                    {file.approverName && (
                      <div className="text-sm text-gray-500">
                        审批人: {file.approverName}
                      </div>
                    )}

                    {/* 上传日期 */}
                    <div className="text-sm text-gray-500">
                      上传日期: {new Date(file.payDate).toLocaleDateString()}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex justify-between items-center pt-2 gap-2">
                      {/* 查看按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-blue-600 hover:text-blue-700"
                        onClick={async () => {
                          try {
                            const data = await projectInputApi.getProjectInputDetail(file.id);
                            setEditingFile(data);
                            setShowDetailModal(true);
                          } catch (error) {
                            console.error('查看文件错误:', error);
                            showMessage(error.message || '查看文件失败');
                          }
                        }}
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>

                      {/* 修改按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-blue-600 hover:text-blue-700"
                        onClick={() => fetchInputDetail(file.id, 'edit')}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>

                      {/* 删除按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-red-600 hover:text-red-700"
                        onClick={() => handleDelete(file.id)}
                      >
                        <TrashIcon className="w-4 h-4 mr-1" />
                      </Button>

                      {/* 打包按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-1 text-yellow-600 hover:text-yellow-700"
                        onClick={() => handleArchiveDownload(file.id, file.name)}
                        disabled={archiveLoading[file.id]}
                      >
                        {archiveLoading[file.id] ? (
                          <div className="flex items-center">
                            <svg 
                              className="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-600" 
                              xmlns="http://www.w3.org/2000/svg" 
                              fill="none" 
                              viewBox="0 0 24 24"
                            >
                              <circle 
                                className="opacity-25" 
                                cx="12" 
                                cy="12" 
                                r="10" 
                                stroke="currentColor" 
                                strokeWidth="4"
                              />
                              <path 
                                className="opacity-75" 
                                fill="currentColor" 
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              />
                            </svg>
                            打包中...
                          </div>
                        ) : (
                          <>
                            <DownloadIcon className="w-4 h-4 mr-1" />
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 修改上传文件模态框 */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">项目输入</h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setUploadFiles([]); // 清空文件列表
                  setUploadFormData({
                    name: '',
                    approver: '',
                    description: '',
                    status: 'DRAFT',
                    file: null,
                    uploadTime: new Date().toISOString().split('T')[0]
                  });
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 文件名和上传时间放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  {/* 文件名 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      输入名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={uploadFormData.name}
                      onChange={(e) => setUploadFormData({ ...uploadFormData, name: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200"
                      required
                    />
                  </div>

                  {/* 上传时间 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      创建时间 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type="date"
                        value={uploadFormData.uploadTime}
                        onChange={(e) => setUploadFormData({ ...uploadFormData, uploadTime: e.target.value })}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 cursor-pointer focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* 审核人和状态放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  {/* 审核人 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      审核人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={approverDropdownRef}>
                      <div
                        onClick={() => !loadingApprovers && setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !uploadFormData.approver ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        } ${loadingApprovers ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        <span className={uploadFormData.approver ? 'text-gray-900' : 'text-gray-400'}>
                          {loadingApprovers ? '加载中...' : (uploadFormData.approver || '请选择审核人')}
                        </span>
                        <div className="flex items-center">
                          {loadingApprovers ? (
                            <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                      
                      {isApproverDropdownOpen && !loadingApprovers && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {approvers.map(approver => (
                              <div
                                key={approver.id}
                                onClick={() => {
                                  setUploadFormData({ ...uploadFormData, approver: approver.name });
                                  setIsApproverDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  uploadFormData.approver === approver.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {approver.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 状态 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">状态</label>
                    <div className="relative" ref={statusDropdownRef}>
                      <div
                        onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-blue-500`}
                      >
                        <span className={uploadFormData.status ? 'text-gray-900' : 'text-gray-400'}>
                          {statusOptions.find(option => option.value === uploadFormData.status)?.label || '请选择状态'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isStatusDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isStatusDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {statusOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setUploadFormData({ ...uploadFormData, status: option.value });
                                  setIsStatusDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  uploadFormData.status === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">描述</label>
                  <textarea
                    value={uploadFormData.description}
                    onChange={(e) => setUploadFormData({ ...uploadFormData, description: e.target.value })}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 min-h-[30px] max-h-[60px] focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-colors duration-200"
                    rows="2"
                  />
                </div>

                {/* 上传文件模态框中添加隐藏的文件输入框 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">上传文件</label>
                  <label 
                    htmlFor="file-upload"
                    className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors duration-200"
                    onDrop={(e) => {
                      e.preventDefault();
                      const files = Array.from(e.dataTransfer.files);
                      setUploadFiles(prev => [...prev, ...files]);
                    }}
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.currentTarget.classList.add('border-blue-400');
                    }}
                    onDragLeave={(e) => {
                      e.preventDefault();
                      e.currentTarget.classList.remove('border-blue-400');
                    }}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-8 w-8 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="text-sm text-gray-600">
                        <span className="text-blue-600 hover:text-blue-500">选择文件</span>
                        <span className="pl-1">或拖拽文件到此处</span>
                      </div>
                    </div>
                  </label>
                  
                  {/* 添加隐藏的文件输入框 */}
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files?.length) {
                        // 将 FileList 转换为数组并设置到 uploadFiles
                        const newFiles = Array.from(e.target.files);
                        setUploadFiles(prev => [...prev, ...newFiles]);
                      }
                    }}
                  />
                </div>

                {/* 显示已选择的文件列表 */}
                {uploadFiles.length > 0 && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">已选择的文件</label>
                    <div className="space-y-2">
                      {uploadFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4 text-gray-400" />
                            <span className="text-sm">{file.name}</span>
                          </div>
                          <button
                            onClick={() => handleRemoveFile(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowUploadModal(false);
                  setUploadFiles([]);
                  setUploadFormData({
                    name: '',
                    approver: '',
                    description: '',
                    status: 'DRAFT',
                    file: null,
                    uploadTime: new Date().toISOString().split('T')[0]
                  });
                }}
              >
                取消
              </Button>
              <Button onClick={handleFileUpload}>
                确认上传
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* 添加新输入模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加新输入</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <FormFields />
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleAdd}>
                确认添加
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 查看输入详情模态框 */}
      {showDetailModal && editingFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看详情</h3>
              <button
                onClick={() => {
                  setShowDetailModal(false);
                  setEditingFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 名称和上传日期放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">名称</label>
                    <div className="mt-1">{editingFile.name}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">创建日期</label>
                    <div className="mt-1">{new Date(editingFile.payDate).toLocaleDateString()}</div>
                  </div>
                </div>

                {/* 审核人和审核日期放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">审核人</label>
                    <div className="mt-1">{editingFile.approverName}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">审核意见</label>
                    <div className="mt-1">{editingFile.approvalComment || '暂无'}</div>
                  </div>
                </div>

                {/* 状态和描述放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">描述</label>
                    <div className="mt-1">{editingFile.description || '暂无'}</div>
                  </div>
                </div>

                {/* 编辑弹窗中的文件部分 */}
                <div>
                  {/* 显示已有的文件列表 */}
                  {editingFile.files && editingFile.files.length > 0 && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">已有文件</label>
                      <div className="max-h-[100px] overflow-y-auto pr-2">
                        <div className="space-y-2">
                          {editingFile.files.map(file => (
                            <div key={file.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-4 h-4 text-gray-400" />
                                <span className="text-sm">{file.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {/* 预览按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-blue-600 hover:text-blue-700"
                                  onClick={async () => {
                                    try {
                                      const data = await fileApi.previewFile(file.name, 'projectin');
                                      
                                      // 处理预览URL
                                      let previewUrl = '';
                                      if (typeof data === 'string') {
                                        previewUrl = data.trim();
                                      } else if (data && typeof data === 'object') {
                                        previewUrl = data.url || data.previewUrl || data.path || data;
                                      }

                                      // 验证URL
                                      if (previewUrl) {
                                        // 如果是相对路径，添加基础URL
                                        if (previewUrl.startsWith('/')) {
                                          previewUrl = `${BASE_URL}${previewUrl}`;
                                        }
                                        // 如果不是完整URL，添加协议和域名
                                        if (!previewUrl.startsWith('http')) {
                                          previewUrl = `${BASE_URL}/${previewUrl}`;
                                        }
                                        window.open(previewUrl, '_blank');
                                      } else {
                                        throw new Error('无法获取预览URL');
                                      }
                                    } catch (error) {
                                      console.error('预览文件错误:', error);
                                      showMessage(error.message || '预览文件失败');
                                    }
                                  }}
                                >
                                  <EyeOpenIcon className="w-4 h-4" />
                                </Button>

                                {/* 修改下载按钮的点击事件处理 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-green-600 hover:text-green-700"
                                  onClick={async () => {
                                    try {
                                      const blob = await fileApi.downloadFile(file.name, 'projectin');
                                      const url = window.URL.createObjectURL(blob);
                                      const link = document.createElement('a');
                                      link.href = url;
                                      link.download = file.name;
                                      document.body.appendChild(link);
                                      link.click();
                                      document.body.removeChild(link);
                                      window.URL.revokeObjectURL(url);
                                    } catch (error) {
                                      console.error('下载文件错误:', error);
                                      showMessage('下载文件失败');
                                    }
                                  }}
                                >
                                  <DownloadIcon className="w-4 h-4" />
                                </Button>

                                {/* 删除按钮 */}
                                {/* <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700"
                                  onClick={() => handleExistingFileDelete(file.id)}
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </Button> */}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDetailModal(false);
                  setEditingFile(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 添加编辑弹窗 */}
      {showEditModal && editingFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑输入</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                

                {/* 审核人和状态放在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  {/* 文件名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    输入名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={editingFile.name}
                    onChange={(e) => setEditingFile({ ...editingFile, name: e.target.value })}
                    className={`mt-1 block w-full rounded-md border ${
                      !editingFile.name ? 'border-red-300' : 'border-gray-300'
                    } px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                  {!editingFile.name && (
                    <p className="mt-1 text-sm text-red-500">请输入名称!</p>
                  )}
                </div>
                  {/* 审核人 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      审核人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={editApproverDropdownRef}>
                      <div
                        onClick={() => !loadingApprovers && setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingFile.approverName ? 'border-red-300' : 'border-gray-300'
                        } ${loadingApprovers ? 'opacity-50 cursor-not-allowed' : ''} hover:border-blue-500`}
                      >
                        <span className={editingFile.approverName ? 'text-gray-900' : 'text-gray-400'}>
                          {loadingApprovers ? '加载中...' : (editingFile.approverName || '请选择审核人')}
                        </span>
                        <div className="flex items-center">
                          {loadingApprovers ? (
                            <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                      
                      {isApproverDropdownOpen && !loadingApprovers && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {approvers.map(approver => (
                              <div
                                key={approver.id}
                                onClick={() => {
                                  const selectedApprover = approvers.find(a => a.name === approver.name);
                                  setEditingFile({
                                    ...editingFile,
                                    approverName: approver.name,
                                    approverId: selectedApprover ? parseInt(selectedApprover.id) : 0
                                  });
                                  setIsApproverDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingFile.approverName === approver.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {approver.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {!editingFile.approverName && (
                      <p className="mt-1 text-sm text-red-500">请选择审核人!</p>
                    )}
                  </div>

                </div>


                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">描述</label>
                  <textarea
                    value={editingFile.description || ''}
                    onChange={(e) => setEditingFile({ ...editingFile, description: e.target.value })}
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 min-h-[30px] max-h-[60px]"
                    rows="2"
                  />
                </div>

                {/* 编辑弹窗中的文件部分 */}
                <div>
                  {/* 显示已有的文件列表 */}
                  {editingFile.files && editingFile.files.length > 0 && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">已有文件</label>
                      <div className="max-h-[100px] overflow-y-auto pr-2">
                        <div className="space-y-2">
                          {editingFile.files.map(file => (
                            <div key={file.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-4 h-4 text-gray-400" />
                                <span className="text-sm">{file.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {/* 预览按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-blue-600 hover:text-blue-700"
                                  onClick={async () => {
                                    try {
                                      const data = await fileApi.previewFile(file.name, 'projectin');
                                      
                                      // 处理预览URL
                                      let previewUrl = '';
                                      if (typeof data === 'string') {
                                        previewUrl = data.trim();
                                      } else if (data && typeof data === 'object') {
                                        previewUrl = data.url || data.previewUrl || data.path || data;
                                      }

                                      // 验证URL
                                      if (previewUrl) {
                                        // 如果是相对路径，添加基础URL
                                        if (previewUrl.startsWith('/')) {
                                          previewUrl = `${BASE_URL}${previewUrl}`;
                                        }
                                        // 如果不是完整URL，添加协议和域名
                                        if (!previewUrl.startsWith('http')) {
                                          previewUrl = `${BASE_URL}/${previewUrl}`;
                                        }
                                        window.open(previewUrl, '_blank');
                                      } else {
                                        throw new Error('无法获取预览URL');
                                      }
                                    } catch (error) {
                                      console.error('预览文件错误:', error);
                                      showMessage(error.message || '预览文件失败');
                                    }
                                  }}
                                >
                                  <EyeOpenIcon className="w-4 h-4" />
                                </Button>
                                {/* 修改下载按钮的点击事件处理 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-green-600 hover:text-green-700"
                                  onClick={async () => {
                                    try {
                                      const blob = await fileApi.downloadFile(file.name, 'projectin');
                                      const url = window.URL.createObjectURL(blob);
                                      const link = document.createElement('a');
                                      link.href = url;
                                      link.download = file.name;
                                      document.body.appendChild(link);
                                      link.click();
                                      document.body.removeChild(link);
                                      window.URL.revokeObjectURL(url);
                                    } catch (error) {
                                      console.error('下载文件错误:', error);
                                      showMessage('下载文件失败');
                                    }
                                  }}
                                >
                                  <DownloadIcon className="w-4 h-4" />
                                </Button>

                                {/* 删除按钮 */}
                                {/* <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600 hover:text-red-700"
                                  onClick={() => handleExistingFileDelete(file.id)}
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </Button> */}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingFile(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleEdit}>
                确认修改
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 添加删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">确认删除</h3>
              <p className="text-gray-600">
                确定要删除此任务吗？
              </p>
              <div className="mt-6 flex justify-center gap-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 bg-white text-gray-700 border rounded-lg hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});