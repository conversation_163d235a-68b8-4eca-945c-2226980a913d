import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronRightIcon,
  PersonIcon,
  CalendarIcon,
  ClockIcon,
  FileTextIcon,
  LayersIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  ChatBubbleIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  QuestionMarkCircledIcon,
  GitHubLogoIcon,
  ChevronLeftIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { testBaselineService } from '../services/testBaselineService';

const categoryOptions = [
  { value: 'user', label: '用户需求' },
  { value: 'project', label: '项目需求' },
  { value: 'functional', label: '功能需求' },
  { value: 'quality', label: '质量需求' }
];

const groupOptions = [
  { value: 'core', label: '核心功能' },
  { value: 'business', label: '业务功能' },
  { value: 'system', label: '系统功能' },
  { value: 'platform', label: '平台功能' }
];

const priorityOptions = [
  { value: 'low', label: '低' },
  { value: 'medium', label: '中' },
  { value: 'high', label: '高' }
];

const mockRequirements = [
  {
    id: 'REQ-001',
    projectId: 1,
    name: '用户登录功能',
    category: 'functional',
    group: 'core',
    priority: 'high',
    description: '实现用户登录和认证功能',
    input: '用户名、密码',
    process: '验证用户身份并生成登录令牌',
    output: '登录状态和用户信息'
  },
  {
    id: 'REQ-002',
    projectId: 2,
    name: 'APP性能优化',
    category: 'performance',
    group: 'system',
    priority: 'medium',
    description: '优化APP启动速度和响应时间',
    input: '性能指标',
    process: '分析性能瓶颈并优化',
    output: '优化后的性能报告'
  },
  {
    id: 'REQ-003',
    projectId: 3,
    name: '数据同步功能',
    category: 'functional',
    group: 'core',
    priority: 'high',
    description: '实现数据实时同步',
    input: '数据源信息',
    process: '数据抽取转换加载',
    output: '同步状态报告'
  }
];

const planStatusOptions = [
  { value: 0, label: '未完成' },
  { value: 1, label: '已完成' }
];

const ErrorMessage = ({ message, onClose }) => {
  return (
    <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
      <div className="text-red-500">
        <Cross2Icon className="w-4 h-4" />
      </div>
      <div className="text-sm text-red-800">{message}</div>
    </div>
  );
};

// 修改自定义时间选择器组件的样式
const DateTimeInput = ({ value, onChange, error, className }) => {
  return (
    <div className="relative">
      <input
        type="datetime-local"
        value={value}
        onChange={onChange}
        className={`${className} ${!value ? 'text-transparent' : ''}`}
        step="1"
      />
      {!value && (
        <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
          请选择开始时间
        </div>
      )}
    </div>
  );
};

// 修改测试级别选项
const testLevelOptions = [
  { value: 0, label: '低' },
  { value: 1, label: '中' },
  { value: 2, label: '高' }
];

// 修改测试优先级选项
const testPriorityOptions = [
  { value: 0, label: '低' },
  { value: 1, label: '中' },
  { value: 2, label: '高' }
];

// 修改测试状态选项
const testStatusOptions = [
  { value: 0, label: '已解决' },
  { value: 1, label: '未解决' },
  { value: 2, label: '不予解决' }
];

export const TestBaseline = observer(() => {
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [selectedRequirement, setSelectedRequirement] = useState(null);
  const [newRequirement, setNewRequirement] = useState({
    name: '',
    category: '',
    group: '',
    priority: '',
    description: '',
    input: '',
    process: '',
    output: ''
  });
  const [requirements, setRequirements] = useState(mockRequirements);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingPlan, setEditingPlan] = useState(null);
  const [page, setPage] = useState(0);
  const [size] = useState(10);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingRequirement, setDeletingRequirement] = useState(null);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);
  const [employees, setEmployees] = useState([
    { id: 1, name: '张三' },
    { id: 2, name: '李四' },
    { id: 3, name: '王五' }
  ]);
  const [newPlan, setNewPlan] = useState({
    projectId: '',
    projectName: '',
    planName: '',
    creator: '',
    status: '',
    description: '',
    startTime: '',
    endTime: ''
  });
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);
  const [planErrors, setPlanErrors] = useState({});
  const [testPlans, setTestPlans] = useState([
    {
      id: 1,
      name: '测试基线1',
      creatorName: '张三',
      status: 1,
      description: '这是测试基线1的描述',
      startTime: '2024-03-20T10:00:00',
      endTime: '2024-03-21T10:00:00'
    },
    {
      id: 2,
      name: '测试基线2',
      creatorName: '李四',
      status: 0,
      description: '这是测试基线2的描述',
      startTime: '2024-03-22T10:00:00',
      endTime: '2024-03-23T10:00:00'
    }
  ]);
  const [searchQuery, setSearchQuery] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewingPlan, setViewingPlan] = useState(null);
  const [searchStatus, setSearchStatus] = useState('');
  const [searchCreator, setSearchCreator] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);
  const [newTestCase, setNewTestCase] = useState({
    projectId: '',
    projectName: '',
    testName: '',
    tester: '',
    testLevel: '',
    testPriority: '',
    testStatus: '',
    description: '',
    files: []
  });
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [testCases, setTestCases] = useState([]);
  const [successMessage, setSuccessMessage] = useState('');
  const [searchLevel, setSearchLevel] = useState('');
  const [searchPriority, setSearchPriority] = useState('');

  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => {
      setErrorMessage('');
    }, 2000);
  };

  const fetchProjects = async (searchName = '') => {
    try {
      const data = await testBaselineService.getProjects(searchName);
      setProjects(data);
      
      if (data.length > 0 && !selectedProject) {
        handleProjectSelect(data[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      handleError('获取项目列表失败');
    }
  };

  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    // 模拟数据
    setTestCases([
      {
        id: 1,
        title: '测试用例1',
        creatorName: '张三',
        level: 0,
        priority: 1,
        status: 0,
        description: '这是测试用例1的描述',
        createdTime: '2024-03-20T10:00:00',
        projectFiles: []
      },
      {
        id: 2,
        title: '测试用例2',
        creatorName: '李四',
        level: 1,
        priority: 2,
        status: 1,
        description: '这是测试用例2的描述',
        createdTime: '2024-03-21T10:00:00',
        projectFiles: []
      }
    ]);
    setTotalPages(1);
    setTotalElements(2);
    setPage(0);
  };

  const handleCreatePlan = async () => {
    const newErrors = {};
    if (!newPlan.planName) newErrors.planName = '请输入计划名称';
    if (!newPlan.creator) newErrors.creator = '请选择创建人';
    if (newPlan.status === '') newErrors.status = '请选择计划状态';
    if (!newPlan.startTime) newErrors.startTime = '请选择开始时间';
    if (!newPlan.endTime) newErrors.endTime = '请选择结束时间';

    setPlanErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    try {
      const newTestPlanData = {
        name: newPlan.planName,
        creatorId: newPlan.creator,
        status: parseInt(newPlan.status),
        description: newPlan.description,
        startTime: newPlan.startTime,
        endTime: newPlan.endTime,
        projectId: selectedProject.id
      };

      await testBaselineService.createTestCase(newTestPlanData);
      fetchTestCases(selectedProject.id, page);
      setShowNewPlanModal(false);
      setNewPlan({
        projectId: '',
        projectName: '',
        planName: '',
        creator: '',
        status: '',
        description: '',
        startTime: '',
        endTime: ''
      });
      handleSuccess('创建成功');
    } catch (error) {
      handleError('创建失败');
    }
  };

  const handleUpdatePlan = async () => {
    try {
      const updatedData = {
        title: editingPlan.title,
        creatorId: editingPlan.creatorId,
        level: parseInt(editingPlan.level),
        priority: parseInt(editingPlan.priority),
        status: parseInt(editingPlan.status),
        description: editingPlan.description,
        projectId: selectedProject.id
      };

      await testBaselineService.updateTestCase(editingPlan.id, updatedData);

      // 处理新上传的文件
      for (const file of uploadedFiles) {
        await testBaselineService.uploadFile(file);
      }

      fetchTestCases(selectedProject.id, page);
      setShowEditModal(false);
      setEditingPlan(null);
      setUploadedFiles([]);
      handleSuccess('更新成功');
    } catch (error) {
      handleError('更新失败');
    }
  };

  const handleSuccess = (message) => {
    setSuccessMessage(message);
    setTimeout(() => {
      setSuccessMessage('');
    }, 2000);
  };

  const SuccessMessage = ({ message }) => {
    return (
      <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-green-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
        <div className="text-green-500">
          <CheckCircledIcon className="w-4 h-4" />
        </div>
        <div className="text-sm text-green-800">{message}</div>
      </div>
    );
  };

  const confirmDelete = async () => {
    try {
      await testBaselineService.deleteTestCase(deletingRequirement.id);
      fetchTestCases(selectedProject.id, page);
      setShowDeleteModal(false);
      setDeletingRequirement(null);
      handleSuccess('删除成功');
    } catch (error) {
      handleError('删除失败');
    }
  };

  const handleSearch = () => {
    // 模拟搜索结果
    const filteredTestCases = testCases.filter(testCase => {
      const matchTitle = !searchQuery || testCase.title.includes(searchQuery);
      const matchLevel = !searchLevel || testCase.level.toString() === searchLevel;
      const matchPriority = !searchPriority || testCase.priority.toString() === searchPriority;
      const matchStatus = !searchStatus || testCase.status.toString() === searchStatus;
      const matchCreator = !searchCreator || testCase.creatorName === employees.find(emp => emp.id.toString() === searchCreator)?.name;
      
      return matchTitle && matchLevel && matchPriority && matchStatus && matchCreator;
    });

    setTestCases(filteredTestCases);
    setTotalPages(Math.ceil(filteredTestCases.length / size));
    setTotalElements(filteredTestCases.length);
    setPage(0);
  };

  const handleReset = () => {
    setSearchQuery('');
    setSearchLevel('');
    setSearchPriority('');
    setSearchStatus('');
    setSearchCreator('');
    
    if (selectedProject) {
      handleProjectSelect(selectedProject);
    }
  };

  const fetchEmployees = async () => {
    try {
      const data = await testBaselineService.getEmployees();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表失败:', error);
      handleError('获取员工列表失败');
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  const handleFileDelete = (index) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
  };

  const openNewModal = () => {
    if (!selectedProject) {
      handleError('请先选择一个项目');
      return;
    }

    setNewTestCase({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      testName: '',
      tester: '',
      testLevel: '',
      testPriority: '',
      testStatus: '',
      description: '',
      files: []
    });
    setUploadedFiles([]);
    setShowNewPlanModal(true);
  };

  // 添加拖拽处理函数
  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  // 修改 handleCreateTestCase 函数
  const handleCreateTestCase = () => {
    // 表单验证
    const errors = {};
    if (!newTestCase.testName) errors.testName = '请输入测试名称';
    if (!newTestCase.tester) errors.tester = '请选择测试人';
    if (!newTestCase.testLevel) errors.testLevel = '请选择测试级别';
    if (!newTestCase.testPriority) errors.testPriority = '请选择测试优先级';
    if (!newTestCase.testStatus) errors.testStatus = '请选择测试状态';

    setPlanErrors(errors);
    if (Object.keys(errors).length > 0) return;

    // 模拟创建成功
    const newCase = {
      id: testCases.length + 1,
      title: newTestCase.testName,
      creatorName: employees.find(emp => emp.id.toString() === newTestCase.tester)?.name,
      level: parseInt(newTestCase.testLevel),
      priority: parseInt(newTestCase.testPriority),
      status: parseInt(newTestCase.testStatus),
      description: newTestCase.description,
      createdTime: new Date().toISOString(),
      projectFiles: uploadedFiles.map(file => ({
        id: Math.random(),
        name: file.name,
        size: file.size
      }))
    };

    setTestCases([...testCases, newCase]);
    setShowNewPlanModal(false);
    setNewTestCase({
      projectId: '',
      projectName: '',
      testName: '',
      tester: '',
      testLevel: '',
      testPriority: '',
      testStatus: '',
      description: '',
      files: []
    });
    setUploadedFiles([]);
    handleSuccess('创建成功');
  };

  const fetchTestCases = async (projectId, pageNum = 0) => {
    try {
      const params = {
        projectId: projectId || selectedProject?.id,
        page: pageNum,
        size: size,
        level: searchLevel || undefined,
        priority: searchPriority || undefined,
        status: searchStatus || undefined,
        title: searchQuery || undefined
      };

      const data = await testBaselineService.getTestCases(params);
      setTestCases(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
    } catch (error) {
      console.error('获取测试用例列表失败:', error);
      handleError('获取测试用例列表失败');
    }
  };

  // 修改分页处理相关代码
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchTestCases(selectedProject?.id, newPage);
  };

  // 修改 handleEditTestCase 函数
  const handleEditTestCase = (id) => {
    // 从当前测试用例列表中找到要编辑的用例
    const testCase = testCases.find(test => test.id === id);
    
    // 设置编辑弹窗的数据
    setEditingPlan({
      id: testCase.id,
      title: testCase.title,
      creatorId: employees.find(emp => emp.name === testCase.creatorName)?.id.toString(),
      creatorName: testCase.creatorName,
      level: testCase.level?.toString(),
      priority: testCase.priority?.toString(),
      status: testCase.status?.toString(),
      description: testCase.description || '',
      createdTime: testCase.createdTime,
      projectId: selectedProject.id,
      projectFiles: testCase.projectFiles || []
    });

    // 打开编辑弹窗
    setShowEditModal(true);
  };

  // 修改 fetchTestCaseDetail 函数
  const fetchTestCaseDetail = (id) => {
    // 从当前测试用例列表中找到要查看的用例
    const testCase = testCases.find(test => test.id === id);
    setViewingPlan(testCase); // 将数据设置到查看弹窗中
    setShowViewModal(true);
  };

  // 添加文件预览函数
  const handleFilePreview = async (file) => {
    try {
      const blob = await testBaselineService.downloadFile(file.id);
      const fileUrl = URL.createObjectURL(blob);
      
      const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
      const fileExtension = file.name.split('.').pop().toLowerCase();
      
      if (imageTypes.includes(fileExtension)) {
        window.open(fileUrl, '_blank');
      } else {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      URL.revokeObjectURL(fileUrl);
    } catch (error) {
      handleError('文件预览失败');
    }
  };

  // 添加文件删除函数
  const handleRemoveFile = (fileIndex, isExisting = false) => {
    if (isExisting) {
      // 删除已有的文件
      setEditingPlan(prev => ({
        ...prev,
        projectFiles: prev.projectFiles.filter((_, index) => index !== fileIndex)
      }));
    } else {
      // 删除新上传的文件
      setUploadedFiles(prev => prev.filter((_, index) => index !== fileIndex));
    }
  };

  // 修改删除测试基线的函数
  const handleDeleteTestCase = () => {
    const updatedTestCases = testCases.filter(testCase => testCase.id !== deletingRequirement.id);
    setTestCases(updatedTestCases);
    setShowDeleteModal(false);
    setDeletingRequirement(null);
    handleSuccess('删除成功');
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setErrorMessage('')}
        />
      )}

      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="space-y-2">
          {projects.map(project => (
            <div
              key={project.id}
              onClick={() => handleProjectSelect(project)}
              className={`p-2 rounded-lg cursor-pointer hover:bg-gray-100 ${selectedProject?.id === project.id ? 'bg-gray-100' : ''}`}
            >
              {project.name}
            </div>
          ))}
        </div>
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">测试基线</p>
            </div>
            <Button
              className="flex items-center gap-1"
              onClick={openNewModal}
            >
              <PlusIcon className="w-4 h-4" />
              添加结果
            </Button>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="搜索结果名称"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              <select
                value={searchLevel}
                onChange={(e) => setSearchLevel(e.target.value)}
                className="w-[150px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">测试级别</option>
                {testLevelOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchPriority}
                onChange={(e) => setSearchPriority(e.target.value)}
                className="w-[150px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">测试优先级</option>
                {testPriorityOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchStatus}
                onChange={(e) => setSearchStatus(e.target.value)}
                className="w-[150px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">测试状态</option>
                {testStatusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchCreator}
                onChange={(e) => setSearchCreator(e.target.value)}
                className="w-[150px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">创建人</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>{employee.name}</option>
                ))}
              </select>

              <div className="flex gap-2">
                <Button
                  onClick={handleSearch}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  搜索
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="px-4 py-2"
                >
                  重置
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">测试名</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建人</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">测试级别</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">测试优先级</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">状态</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">测试描述</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建时间</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody>
                {testCases.map(testCase => (
                  <tr key={testCase.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.title}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">{testCase.creatorName}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testLevelOptions.find(opt => opt.value === testCase.level)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testPriorityOptions.find(opt => opt.value === testCase.priority)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testStatusOptions.find(opt => opt.value === testCase.status)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testCase.description || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testCase.createdTime ? new Date(testCase.createdTime).toLocaleString() : '-'}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-3">
                        <button
                          onClick={() => fetchTestCaseDetail(testCase.id)}
                          className="text-gray-400 hover:text-blue-700"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditTestCase(testCase.id)}
                          className="text-gray-400 hover:text-blue-700"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setDeletingRequirement(testCase);
                            setShowDeleteModal(true);
                          }}
                          className="text-gray-400 hover:text-red-500"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex items-center justify-end px-4 py-3 border-t">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 mr-4">
                  共 {totalElements} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.max(0, page - 1))}
                  disabled={page === 0}
                  className="px-2 py-1 text-sm"
                >
                  上一页
                </Button>
                <div className="flex items-center">
                  <button
                    className={`px-3 py-1 text-sm rounded-lg ${page === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                    onClick={() => handlePageChange(0)}
                  >
                    1
                  </button>
                  {page > 2 && <span className="px-2 text-gray-500">...</span>}
                  {page > 1 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page - 1)}
                    >
                      {page}
                    </button>
                  )}
                  {page > 0 && page < totalPages - 1 && (
                    <button
                      className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                    >
                      {page + 1}
                    </button>
                  )}
                  {page < totalPages - 2 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page + 1)}
                    >
                      {page + 2}
                    </button>
                  )}
                  {page < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                  {totalPages > 1 && (
                    <button
                      className={`px-3 py-1 text-sm rounded-lg ${page === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                      onClick={() => handlePageChange(totalPages - 1)}
                    >
                      {totalPages}
                    </button>
                  )}
                </div>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.min(totalPages - 1, page + 1))}
                  disabled={page >= totalPages - 1}
                  className="px-2 py-1 text-sm"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看测试基线</p>
          </div>
        </div>
      )}

      {showNewPlanModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加结果</h3>
              <button
                onClick={() => setShowNewPlanModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试名 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newTestCase.testName}
                    onChange={(e) => setNewTestCase({ ...newTestCase, testName: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试人 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={newTestCase.tester}
                    onChange={(e) => setNewTestCase({ ...newTestCase, tester: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试人</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>{employee.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试级别 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={newTestCase.testLevel}
                    onChange={(e) => setNewTestCase({ ...newTestCase, testLevel: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试级别</option>
                    {testLevelOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试优先级 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={newTestCase.testPriority}
                    onChange={(e) => setNewTestCase({ ...newTestCase, testPriority: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试优先级</option>
                    {testPriorityOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试状态 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={newTestCase.testStatus}
                    onChange={(e) => setNewTestCase({ ...newTestCase, testStatus: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试状态</option>
                    {testStatusOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <textarea
                    value={newTestCase.description}
                    onChange={(e) => setNewTestCase({ ...newTestCase, description: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试描述"
                    rows={4}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <div
                    className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => document.getElementById('file-upload').click()}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-600 justify-center">
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          multiple
                          onChange={handleFileUpload}
                        />
                        <span className="text-blue-600 hover:text-blue-500">上传文件</span>
                        <p className="pl-1">或拖拽文件到这里</p>
                      </div>
                      <p className="text-xs text-gray-500">支持任意文件格式</p>
                    </div>
                  </div>
                  {/* 已上传文件列表 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">{file.name}</span>
                          <button
                            onClick={() => handleFileDelete(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewPlanModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateTestCase}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看测试基线</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <div className="text-gray-900">{selectedProject?.name}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试名
                  </label>
                  <div className="text-gray-900">{viewingPlan.title}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    创建人
                  </label>
                  <div className="text-gray-900">{viewingPlan.creatorName}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试级别
                  </label>
                  <div className="text-gray-900">
                    {testLevelOptions.find(opt => opt.value === viewingPlan.level)?.label || '-'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试优先级
                  </label>
                  <div className="text-gray-900">
                    {testPriorityOptions.find(opt => opt.value === viewingPlan.priority)?.label || '-'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试状态
                  </label>
                  <div className="text-gray-900">
                    {testStatusOptions.find(opt => opt.value === viewingPlan.status)?.label || '-'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <div className="text-gray-900">{viewingPlan.description || '-'}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    创建时间
                  </label>
                  <div className="text-gray-900">
                    {viewingPlan.createdTime ? new Date(viewingPlan.createdTime).toLocaleString() : '-'}
                  </div>
                </div>
                {/* 如果有附件，显示附件列表 */}
                {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <div className="space-y-2">
                      {viewingPlan.projectFiles.map((file, index) => (
                        <div 
                          key={index} 
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg hover:bg-gray-100"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              预览
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑测试基线</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试名 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={editingPlan.title}
                    onChange={(e) => setEditingPlan({ ...editingPlan, title: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试人 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={editingPlan.creatorId}
                    onChange={(e) => setEditingPlan({ 
                      ...editingPlan, 
                      creatorId: e.target.value,
                      creatorName: employees.find(emp => emp.id.toString() === e.target.value)?.name
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试人</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>{employee.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试级别 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={editingPlan.level}
                    onChange={(e) => setEditingPlan({ ...editingPlan, level: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试级别</option>
                    {testLevelOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试优先级 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={editingPlan.priority}
                    onChange={(e) => setEditingPlan({ ...editingPlan, priority: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试优先级</option>
                    {testPriorityOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试状态 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={editingPlan.status}
                    onChange={(e) => setEditingPlan({ ...editingPlan, status: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择测试状态</option>
                    {testStatusOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <textarea
                    value={editingPlan.description}
                    onChange={(e) => setEditingPlan({ ...editingPlan, description: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试描述"
                    rows={4}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  {/* 显示已有的文件 */}
                  {editingPlan.projectFiles && editingPlan.projectFiles.length > 0 && (
                    <div className="mb-2 space-y-2">
                      {editingPlan.projectFiles.map((file, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleRemoveFile(index, true)}
                              className="text-red-500 hover:text-red-700"
                              title="删除"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* 显示新上传的文件 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mb-2 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div 
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <button
                            onClick={() => handleRemoveFile(index)}
                            className="text-red-500 hover:text-red-700"
                            title="删除"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* 文件上传框 */}
                  <div 
                    className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 min-h-[120px] flex flex-col items-center justify-center"
                    onClick={() => document.getElementById('fileInput').click()}
                  >
                    <input
                      id="fileInput"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => {
                        const files = Array.from(e.target.files || []);
                        setUploadedFiles(prev => [...prev, ...files]);
                        e.target.value = '';
                      }}
                    />
                    <FileTextIcon className="w-6 h-6 text-gray-400 mb-2" />
                    <div className="text-gray-500">
                      点击上传文件 或 拖拽文件到此处
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                }}
              >
                取消
              </Button>
              <Button onClick={handleUpdatePlan}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">确认删除</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-600">
                确定要删除测试基线 "{deletingRequirement.title}" 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingRequirement(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {successMessage && <SuccessMessage message={successMessage} />}
    </div>
  );
});