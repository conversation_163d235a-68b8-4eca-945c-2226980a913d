import axios from 'axios';

import { fetchData } from './fetch';

// 设计输入相关接口
export const designInputApi = {
  // 获取所有设计输入文件
  getAllDesignInputs: (projectId, searchParams) => {
    const queryParams = new URLSearchParams({ projectId, ...searchParams });
    return fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/all?${queryParams}`).then(res => res.json());
  },

  // 创建设计输入
  createDesignInput: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 从项目输入创建设计输入
  createFromProjectInput: (projectId, name, uploadId, projectInputIds) => {
    const params = new URLSearchParams({ projectId, name, uploadId });
    return fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/createByProjectInput?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectInputIds)
    }).then(res => res.json());
  },

  // 更新设计输入
  updateDesignInput: (designInputId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/update/${designInputId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除设计输入
  deleteDesignInput: (designInputId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/delete/${designInputId}`).then(res => res.json()),

  // 获取单个设计输入详情
  getDesignInputDetail: (designInputId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/one/${designInputId}`).then(res => res.json()),

  // 提交评审
  submitForReview: (reviewData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewData)
    }).then(res => res.json()),

  // 上传文件
  uploadFiles: (designInputId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/upload`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjects: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),
};

// 项目输入相关接口
export const projectInputApi = {
  // 获取所有项目输入文件
  getAllProjectInputs: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-inputs/all?projectId=${projectId}`).then(res => res.json()),

  // 获取项目输入文件
  getProjectInFiles: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-ins/all/files?projectId=${projectId}`).then(res => res.json()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployees: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
};

// 文件操作相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 下载文件
  downloadFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },

  // 批量下载（打包下载）
  downloadArchive: (bucketId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/archive?bucketId=${bucketId}`),
}; 