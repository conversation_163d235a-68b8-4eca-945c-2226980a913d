import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { projectApi, clientApi, employeeApi } from '../services/projectService';

// 项目类型配置
const projectTypes = [
  { id: 0, name: '软件开发' },
  { id: 1, name: '市场调研' },
  { id: 2, name: '产品设计' },
  { id: 3, name: '服务项目' }
];

// 项目状态配置
const projectStatuses = [
  { id: 0, name: '未开始' },
  { id: 1, name: '进行中' },
  { id: 2, name: '结项' }
];

// 优先级配置
const priorityOptions = [
  { id: 'high', name: '高优先级' },
  { id: 'medium', name: '中优先级' },
  { id: 'low', name: '低优先级' }
];

// 错误提示组件
const ErrorMessage = ({ message }) => (
  <div className="text-red-500 text-sm mt-1">{message}</div>
);

/**
 * 项目添加/编辑页面组件
 * 功能：新建项目或编辑现有项目信息
 */
export const Addproject = observer(() => {
  const location = useLocation();
  const navigate = useNavigate();
  const { editMode, projectData } = location.state || {};

  // 数据状态管理
  const [managers, setManagers] = useState([]); // 员工列表
  const [loadingManagers, setLoadingManagers] = useState(false); // 员工加载状态
  const [clients, setClients] = useState([]); // 客户列表
  const [loadingClients, setLoadingClients] = useState(false); // 客户加载状态

  // 错误提示状态
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorMessages, setErrorMessages] = useState([]);

  // 表单验证错误状态
  const [errors, setErrors] = useState({
    name: false,
    type: false,
    manager: false,
    priority: false,
    clientId: false,
    status: false
  });

  // 下拉框开关状态
  const [dropdownStates, setDropdownStates] = useState({
    client: false,
    type: false,
    manager: false,
    priority: false,
    status: false
  });

  // 优先级转换工具函数
  const getPriorityText = (priority) => {
    const priorityMap = { 0: 'high', 1: 'medium', 2: 'low' };
    return priorityMap[priority] || 'medium';
  };

  const getPriorityNumber = (priority) => {
    const priorityMap = { 'high': 0, 'medium': 1, 'low': 2 };
    return priorityMap[priority] || 1;
  };

  // 统一的下拉框切换函数
  const toggleDropdown = (dropdownName) => {
    setDropdownStates(prev => ({
      ...prev,
      [dropdownName]: !prev[dropdownName]
    }));
  };

  // 关闭所有下拉框
  const closeAllDropdowns = () => {
    setDropdownStates({
      client: false,
      type: false,
      manager: false,
      priority: false,
      status: false
    });
  };

  // 表单数据初始化
  const initFormData = () => {
    if (editMode && projectData) {
      return {
        id: projectData.id,
        name: projectData.name || '',
        type: projectData.type?.toString() || '',
        description: projectData.description || '',
        createTime: projectData.createTime ? projectData.createTime.split('T')[0] : new Date().toISOString().split('T')[0],
        priority: getPriorityText(projectData.priority),
        manager: projectData.managerId?.toString() || '',
        managerName: projectData.managerName || '',
        clientId: projectData.clientId || '',
        clientName: projectData.clientName || '',
        status: projectData.status?.toString() || '0'
      };
    }
    return {
      id: null,
      name: '',
      type: '',
      description: '',
      createTime: new Date().toISOString().split('T')[0],
      priority: 'medium',
      manager: '',
      managerName: '',
      clientId: '',
      clientName: '',
      status: '0'
    };
  };

  const [formData, setFormData] = useState(initFormData);

  // 获取员工列表
  const fetchManagers = async () => {
    setLoadingManagers(true);
    try {
      const data = await employeeApi.getEmployeeList();
      setManagers(data || []);
    } catch (error) {
      console.error('获取员工列表出错:', error);
    } finally {
      setLoadingManagers(false);
    }
  };

  // 获取客户列表
  const fetchClients = async () => {
    setLoadingClients(true);
    try {
      const data = await clientApi.getClientList();
      setClients(data || []);
    } catch (error) {
      console.error('获取客户列表出错:', error);
    } finally {
      setLoadingClients(false);
    }
  };

  // 组件初始化：获取数据并设置表单
  useEffect(() => {
    const initData = async () => {
      // 并行获取员工和客户列表
      await Promise.all([fetchManagers(), fetchClients()]);

      // 处理客户信息设置
      if (editMode && projectData) {
        // 编辑模式：使用项目数据中的客户信息
        setFormData(prev => ({
          ...prev,
          clientId: projectData.clientId || '',
          clientName: projectData.clientName || ''
        }));
      } else if (location.state?.client) {
        // 新建模式：使用传入的客户信息
        setFormData(prev => ({
          ...prev,
          clientId: location.state.client.id,
          clientName: location.state.client.company
        }));
      }
    };

    initData();
  }, [editMode, projectData, location.state]);

  // 返回上一页
  const handleReturn = () => {
    window.history.back();
  };

  // 表单验证
  const validateForm = () => {
    const newErrors = {
      name: !formData.name,
      type: !formData.type,
      manager: !formData.manager,
      priority: !formData.priority,
      clientId: !formData.clientId || ['', '0', 'all'].includes(formData.clientId),
      status: !formData.status
    };

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error);
  };

  // 提交表单处理
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const requestData = {
        id: formData.id || 0,
        name: formData.name,
        type: parseInt(formData.type),
        description: formData.description || '',
        createTime: formData.createTime,
        priority: getPriorityNumber(formData.priority),
        managerId: parseInt(formData.manager),
        clientId: parseInt(formData.clientId),
        managerName: formData.managerName || '',
        status: parseInt(formData.status) || 0,
      };

      if (editMode) {
        await projectApi.updateProject(formData.id, requestData);
      } else {
        await projectApi.createProject(requestData);
      }

      navigate(-1);

    } catch (err) {
      console.error('保存项目错误:', err);
      setErrorMessages([err.message]);
      setShowErrorDialog(true);
      setTimeout(() => setShowErrorDialog(false), 2000);
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdownIds = ['client-dropdown', 'type-dropdown', 'manager-dropdown', 'priority-dropdown', 'status-dropdown'];

      dropdownIds.forEach(id => {
        const dropdown = document.getElementById(id);
        if (dropdown && !dropdown.contains(event.target)) {
          const dropdownName = id.replace('-dropdown', '');
          setDropdownStates(prev => ({ ...prev, [dropdownName]: false }));
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 统一的选择处理函数
  const handleSelectChange = (field, value, errorField = field, dropdownName = field) => {
    setFormData(prev => ({ ...prev, ...value }));
    setErrors(prev => ({ ...prev, [errorField]: false }));
    setDropdownStates(prev => ({ ...prev, [dropdownName]: false }));
  };

  // 各类型选择处理函数
  const handleManagerChange = (selectedManager) => {
    handleSelectChange('manager', {
      manager: selectedManager.id.toString(),
      managerName: selectedManager.name
    }, 'manager', 'manager');
  };

  const handleTypeChange = (selectedType) => {
    handleSelectChange('type', {
      type: selectedType.id.toString()
    }, 'type', 'type');
  };

  const handlePriorityChange = (selectedPriority) => {
    handleSelectChange('priority', {
      priority: selectedPriority.id
    }, 'priority', 'priority');
  };

  const handleStatusChange = (selectedStatus) => {
    handleSelectChange('status', {
      status: selectedStatus.id.toString()
    }, 'status', 'status');
  };

  const handleClientChange = (selectedClient) => {
    handleSelectChange('client', {
      clientId: selectedClient.id.toString(),
      clientName: selectedClient.company
    }, 'clientId', 'client');
  };

  return (
    <div className="p-6 pt-16">
      {/* 将错误提示移到页面顶部 */}
      {showErrorDialog && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
          <div className="flex items-center gap-2 text-red-500 text-sm bg-red-50 border border-red-200 rounded-md px-3 py-2 w-[400px]">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {errorMessages.map((error, index) => (
              <span key={index}>{error}</span>
            ))}
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">{editMode ? '修改项目' : '新建项目'}</h2>
          <Button variant="outline" onClick={handleReturn}>
            返回
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6">
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                {/* 客户名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    客户名称 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative" id="client-dropdown">
                    <div
                      onClick={() => !loadingClients && !editMode && toggleDropdown('client')}
                      className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                        errors.clientId ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
                      } ${loadingClients || editMode ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <span className={formData.clientId && formData.clientName ? 'text-gray-900' : 'text-gray-400'}>
                        {loadingClients ? '加载中...' : (formData.clientName || '请选择客户名称')}
                      </span>
                      <div className="flex items-center">
                        {loadingClients ? (
                          <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.client ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    </div>

                    {dropdownStates.client && !loadingClients && !editMode && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div className="py-1 max-h-60 overflow-auto">
                          {clients.map(client => (
                            <div
                              key={client.id}
                              onClick={() => handleClientChange(client)}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                formData.clientId === client.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {client.company}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {/* 调试信息: {JSON.stringify(errors)} */}
                  {errors.clientId && (
                    <ErrorMessage message="请选择客户名称" />
                  )}
                </div>

                {/* 项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }));
                      setErrors(prev => ({ ...prev, name: false }));
                    }}
                    placeholder='请输入项目名称'
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {/* 项目名称错误提示 */}
                  {errors.name && (
                    <ErrorMessage message="请输入项目名称" />
                  )}
                </div>

                {/* 项目类型 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目类型 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative" id="type-dropdown">
                    <div
                      onClick={() => toggleDropdown('type')}
                      className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                        errors.type ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
                      }`}
                    >
                      <span className={formData.type ? 'text-gray-900' : 'text-gray-400'}>
                        {formData.type ? projectTypes.find(t => t.id.toString() === formData.type)?.name : '请选择类型'}
                      </span>
                      <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.type ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>

                    {dropdownStates.type && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div className="py-1 max-h-60 overflow-auto">
                          {projectTypes.map(type => (
                            <div
                              key={type.id}
                              onClick={() => handleTypeChange(type)}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                formData.type === type.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {type.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.type && <ErrorMessage message="请选择项目类型" />}
                </div>

                {/* 项目负责人 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目负责人 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative" id="manager-dropdown">
                    <div
                      onClick={() => !loadingManagers && toggleDropdown('manager')}
                      className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                        errors.manager ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
                      } ${loadingManagers ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <span className={formData.manager ? 'text-gray-900' : 'text-gray-400'}>
                        {loadingManagers ? '加载中...' : (formData.managerName || '请选择项目负责人')}
                      </span>
                      <div className="flex items-center">
                        {loadingManagers ? (
                          <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.manager ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    </div>

                    {dropdownStates.manager && !loadingManagers && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div className="py-1 max-h-60 overflow-auto">
                          {managers.map(manager => (
                            <div
                              key={manager.id}
                              onClick={() => handleManagerChange(manager)}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                formData.manager === manager.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {manager.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.manager && <ErrorMessage message="请选择项目负责人" />}
                </div>

                {/* 优先级 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative" id="priority-dropdown">
                    <div
                      onClick={() => toggleDropdown('priority')}
                      className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                        errors.priority ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
                      }`}
                    >
                      <span className={formData.priority ? 'text-gray-900' : 'text-gray-400'}>
                        {formData.priority ? priorityOptions.find(p => p.id === formData.priority)?.name : '请选择优先级'}
                      </span>
                      <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.priority ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>

                    {dropdownStates.priority && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div className="py-1 max-h-60 overflow-auto">
                          {priorityOptions.map(priority => (
                            <div
                              key={priority.id}
                              onClick={() => handlePriorityChange(priority)}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                formData.priority === priority.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {priority.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.priority && <ErrorMessage message="请选择项目优先级" />}
                </div>

                {/* 项目状态 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目状态 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative" id="status-dropdown">
                    <div
                      onClick={() => toggleDropdown('status')}
                      className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                        errors.status ? 'border-red-500' : 'border-gray-300 hover:border-blue-500'
                      }`}
                    >
                      <span className={formData.status ? 'text-gray-900' : 'text-gray-400'}>
                        {formData.status ? projectStatuses.find(s => s.id.toString() === formData.status)?.name : '请选择状态'}
                      </span>
                      <svg className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.status ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>

                    {dropdownStates.status && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                        <div className="py-1 max-h-60 overflow-auto">
                          {projectStatuses.map(status => (
                            <div
                              key={status.id}
                              onClick={() => handleStatusChange(status)}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                formData.status === status.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {status.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.status && <ErrorMessage message="请选择项目状态" />}
                </div>

                {/* 创建时间 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    创建时间 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    value={formData.createTime}
                    onChange={(e) => setFormData(prev => ({ ...prev, createTime: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 项目描述 */}
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目描述
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* 附件上传 */}
                {/* <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目附件
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <UploadIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-2">
                      <label className="cursor-pointer text-blue-500 hover:text-blue-600">
                        <span>点击上传</span>
                        <input type="file" className="hidden" multiple />
                      </label>
                      <span className="text-gray-500"> 或拖拽文件到此处</span>
                    </div>
                  </div>
                </div> */}
              </div>
            </div>
          </div>

          <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
            <Button variant="outline" onClick={handleReturn}>
              取消
            </Button>
            <Button onClick={handleSubmit}>
              {editMode ? '保存修改' : '创建项目'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
});
