import { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  PersonIcon,
  CubeIcon,
  GearIcon,
  CircleIcon,
  ChevronRightIcon,
  CheckCircledIcon,
  Pencil1Icon,
  TrashIcon,
  Link2Icon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import {
  resourceTypeApi,
  resourceApi,
  projectApi,
  employeeResourceApi
} from '../services/ResourceManagementService';
import { getAllOrganizations } from '../services/organizationService';

/**
 * 资源管理页面
 * 主要功能：
 * 1. 资源类型管理（增删改查）
 * 2. 资源列表展示和管理
 * 3. 资源分配到项目
 * 4. 组织架构展示和部门资源管理
 * 5. 人力资源和设备资源的统一管理
 */

export const ResourceManagement = observer(() => {
  // 基础状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [taskLoading, setTaskLoading] = useState(false);

  // 资源类型相关状态
  const [resourceTypes, setResourceTypes] = useState([]);
  const [typeSearchQuery, setTypeSearchQuery] = useState('');
  const [showNewTypeModal, setShowNewTypeModal] = useState(false);
  const [newTypeForm, setNewTypeForm] = useState({
    name: '',
    description: ''
  });
  const [editingTypeId, setEditingTypeId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTypeId, setDeleteTypeId] = useState(null);

  // 资源列表相关状态
  const [resources, setResources] = useState([]);
  const [selectedResources, setSelectedResources] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);

  // 模态框状态
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [showEditResourceModal, setShowEditResourceModal] = useState(false);
  const [showDeleteResourceConfirm, setShowDeleteResourceConfirm] = useState(false);
  const [deleteResourceId, setDeleteResourceId] = useState(null);
  const [showDistributeModal, setShowDistributeModal] = useState(false);
  const [showProjectDropdown, setShowProjectDropdown] = useState(false);

  // 表单数据
  const [assignmentForm, setAssignmentForm] = useState({
    projectId: '',
    startDate: '',
    endDate: '',
    role: '',
    workload: '100%'
  });
  const [newProjectForm, setNewProjectForm] = useState({
    name: '',
    description: '',
    type: '',
    status: 0
  });
  const [editResourceForm, setEditResourceForm] = useState({
    id: '',
    name: '',
    projectName: '',
    projectId: '',
    description: '',
    type: '',
    status: 0,
    startDate: '',
    endDate: ''
  });
  const [distributeForm, setDistributeForm] = useState({
    projectId: '',
    startDate: '',
    endDate: ''
  });

  // 项目相关状态
  const [projects, setProjects] = useState([]);

  // 组织架构相关状态
  const [departments, setDepartments] = useState([]);
  const [organizations, setOrganizations] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [showDepartments, setShowDepartments] = useState(false);
  const [showOrganizations, setShowOrganizations] = useState(false);
  const [expandedOrgs, setExpandedOrgs] = useState(new Set());

  // 显示错误消息
  const showError = useCallback((message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 3000);
  }, []);

  // 分配资源到项目
  const handleDistribute = async () => {
    if (!distributeForm.projectId || !distributeForm.startDate || !distributeForm.endDate) {
      showError('请填写必要信息');
      return;
    }

    try {
      // 这里应该调用实际的API
      console.log('分配资源:', distributeForm);

      // 关闭弹窗并重置表单
      setShowDistributeModal(false);
      setDistributeForm({
        projectId: '',
        startDate: '',
        endDate: ''
      });

      showError('分配成功');
    } catch (error) {
      console.error('分配资源失败:', error);
      showError('分配资源失败');
    }
  };



  // 获取资源类型列表
  const fetchResourceTypes = useCallback(async (searchName = '') => {
    try {
      const params = {
        page: 0,
        size: 10,
        name: searchName
      };

      const data = await resourceTypeApi.getAllTypes(params);

      const formattedTypes = data.content.map(type => ({
        id: type.id.toString(),
        name: type.name,
        description: type.description,
        icon: type.name.includes('人力') ? PersonIcon :
          type.name.includes('设备') ? CubeIcon :
            type.name.includes('技术') ? GearIcon : CircleIcon
      }));

      setResourceTypes(formattedTypes);

      // 只在初始化时选择第一个类型
      if (!searchName && !selectedType && formattedTypes.length > 0) {
        const firstType = formattedTypes[0];
        handleTypeSelect(firstType.id);
      }
    } catch (error) {
      console.error('获取资源类型失败:', error);
      showError('获取资源类型失败');
      setResources([]);
      setTotalPages(1);
      setTotalRecords(0);
    }
  }, [selectedType, showError]);

  const handleTypeSearch = (value) => {
    setTypeSearchQuery(value);
    if (!value.trim()) {
      // 如果搜索框清空，恢复原始列表
      fetchResourceTypes();
      return;
    }

    const timer = setTimeout(() => {
      fetchResourceTypes(value);
    }, 300);
    return () => clearTimeout(timer);
  };

  useEffect(() => {
    fetchResourceTypes();
  }, []);

  const fetchResourcesByType = async (typeId, page = currentPage - 1) => {
    if (!typeId) {
      setResources([]);
      return;
    }

    try {
      const selectedTypeName = resourceTypes.find(t => t.id === typeId)?.name;
      let data;
      
      if (selectedTypeName?.includes('人力')) {
        data = await employeeResourceApi.getEmployeeList(0, {
          page: page,
          size: pageSize
        });
      } else {
        data = await resourceApi.getAllResources({
          type: typeId,
          page: page,
          size: pageSize
        });
      }
      
      if (selectedTypeName?.includes('人力')) {
        const formattedData = {
          content: data.content.map(employee => ({
            id: employee.id,
            name: employee.name,
            description: employee.position || '',
            status: employee.status || 0,
            type: typeId,
            projectId: employee.projectId || null,
            projectName: employee.projectName || '-',
            startDate: employee.startTime || '-',
            endDate: employee.endTime || '-'
          })),
          totalElements: data.totalElements || data.content.length,
          totalPages: data.totalPages || 1
        };
        setResources(formattedData.content);
        setTotalPages(Math.max(1, formattedData.totalPages));
        setTotalRecords(formattedData.totalElements);
      } else {
        setResources(data.content || []);
        setTotalPages(Math.max(1, data.totalPages || 1));
        setTotalRecords(data.totalElements || 0);
      }
    } catch (error) {
      console.error('获取资源失败:', error);
      setResources([]);
      setTotalPages(1);
      setTotalRecords(0);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchResourcesByType(selectedType, page - 1);
  };

  const handleTypeSelect = async (typeId) => {
    setSelectedType(typeId);
    setCurrentPage(1);
    setSearchQuery(''); // 清空搜索框
    
    // 获取选中的资源类型
    const selectedTypeName = resourceTypes.find(t => t.id === typeId)?.name;
    
    if (selectedTypeName?.includes('人力')) {
      try {
        // 获取人力资源列表
        const employeesResponse = await employeeResourceApi.getEmployeeList(0, {
          page: 0,
          size: pageSize
        });
        
        // 格式化数据
        const formattedData = employeesResponse.content.map(employee => ({
          id: employee.id,
          name: employee.name,
          description: employee.position || '',
          status: employee.status || 0,
          type: typeId,
          projectId: employee.projectId || null,
          projectName: employee.projectName || '-',
          startDate: employee.startTime || '-',
          endDate: employee.endTime || '-'
        }));
        
        setResources(formattedData);
        setTotalPages(Math.max(1, employeesResponse.totalPages || 1));
        setTotalRecords(employeesResponse.totalElements || 0);
        
        // 显示组织架构
        await fetchOrganizations();
        setShowOrganizations(true);
        setShowDepartments(false);
      } catch (error) {
        console.error('获取人力资源失败:', error);
        setResources([]);
        setTotalPages(1);
        setTotalRecords(0);
      }
    } else {
      setShowDepartments(true);
      setShowOrganizations(false);
      // 获取部门数据
      await fetchDepartments();
      await fetchResourcesByType(typeId, 0);
    }
  };

  // 修改搜索防抖逻辑
  useEffect(() => {
    const timer = setTimeout(() => {
      // 如果是人力资源，使用employees/search接口
      if (resourceTypes.find(t => t.id === selectedType)?.name.includes('人力')) {
        handleEmployeeSearch(searchQuery);
      }
      // 移除非人力资源的搜索逻辑

    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 添加人力资源搜索函数
  const handleEmployeeSearch = async (name) => {
    try {
      const params = {
        page: 0,
        size: 10,
        name: name
      };
      
      // 使用当前选中的部门ID，如果没有选中部门则使用0
      const orgId = selectedDepartment || 0;
      
      const data = await employeeResourceApi.getEmployeeList(orgId, params);
      
      // 格式化返回的数据
      const formattedData = data.content.map(employee => ({
        id: employee.id,
        name: employee.name,
        description: employee.position || '',
        status: employee.status || 0,
        type: selectedType,
        projectId: employee.projectId || null,
        projectName: employee.projectName || '-',
        startDate: employee.startTime || '-',
        endDate: employee.endTime || '-'
      }));

      setResources(formattedData);
      setTotalPages(Math.max(1, data.totalPages || 1));
      setTotalRecords(data.totalElements || 0);
    } catch (error) {
      console.error('搜索员工失败:', error);
      setErrorMessage('搜索员工失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
      setResources([]);
      setTotalPages(1);
      setTotalRecords(0);
    }
  };

  // 删除非人力资源搜索函数
  // const handleResourceSearch = async (name) => { ... };












  const handleEditType = (type) => {
    setNewTypeForm({
      name: type.name,
      description: type.description || ''
    });
    setEditingTypeId(type.id);
    setShowNewTypeModal(true);
  };

  const handleSaveResourceType = async () => {
    if (!newTypeForm.name) {
      setErrorMessage('请输入资源类型名称');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    try {
      const typeData = {
        id: editingTypeId ? parseInt(editingTypeId) : undefined,
        name: newTypeForm.name,
        description: newTypeForm.description
      };

      if (editingTypeId) {
        await resourceTypeApi.updateType(editingTypeId, typeData);
      } else {
        await resourceTypeApi.createType(typeData);
      }

      await fetchResourceTypes();

      setNewTypeForm({
        name: '',
        description: ''
      });
      setEditingTypeId(null);
      setShowNewTypeModal(false);
    } catch (error) {
      console.error(editingTypeId ? '更新资源类型失败:' : '创建资源类型失败:', error);
      setErrorMessage(editingTypeId ? '更新资源类型失败，请重试' : '创建资源类型失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleDeleteType = async (typeId) => {
    setDeleteTypeId(typeId);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    try {
      await resourceTypeApi.deleteType(deleteTypeId);
      await fetchResourceTypes();
      setShowDeleteConfirm(false);
    } catch (error) {
      console.error('删除资源类型失败:', error);
      setErrorMessage('删除资源类型失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleCloseModal = () => {
    setShowNewTypeModal(false);
    setNewTypeForm({
      name: '',
      description: ''
    });
    setEditingTypeId(null);
  };

  const handleCreateProject = async () => {
    try {
      // 验证必填字段
      if (!newProjectForm.name) {
        setErrorMessage('请输入资源名称');
        return;
      }

      // 准备请求数据
      const requestData = {
        id: 0,
        name: newProjectForm.name,
        description: newProjectForm.description || '',
        type: parseInt(selectedType),
        status: 0,
        // 如果是设备资源且选择了部门，则添加部门ID
        ...(resourceTypes.find(t => t.id === selectedType)?.name.includes('设备') && selectedDepartment 
          ? { departmentId: selectedDepartment }
          : {})
      };

      await resourceApi.createResource(requestData);

      // 关闭弹窗并刷新列表
      setShowNewProjectModal(false);
      setNewProjectForm({
        name: '',
        description: '',
        type: '',
        status: 0
      });

      // 如果选择了部门，刷新部门资源列表
      if (selectedDepartment) {
        fetchResourcesByDepartment(selectedDepartment);
      } else {
        fetchResourcesByType(selectedType, currentPage - 1);
      }
    } catch (error) {
      console.error('创建资源失败:', error);
      setErrorMessage('创建资源失败');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleEditResource = (resource) => {
    setTaskLoading(true);

    // 判断是否是人力资源
    const isHumanResource = resourceTypes.find(t => t.id === selectedType)?.name.includes('人力');
    
    // 同时获取资源详情和项目列表
    Promise.all([
      isHumanResource 
        ? employeeResourceApi.getEmployeeDetail(resource.id)
        : resourceApi.getResourceDetail(resource.id),
      projectApi.getProjectList()
    ])
      .then(([resourceData, projectsData]) => {
        setProjects(projectsData); // 设置项目列表
        setEditResourceForm({
          id: resourceData.id,
          name: resourceData.name,
          projectName: resourceData.projectName || '',
          projectId: resourceData.projectId || '',
          description: resourceData.position || resourceData.description || '',
          type: resourceData.type,
          status: resourceData.status || 0,
          startDate: resourceData.startTime || resourceData.startDate || '',
          endDate: resourceData.endTime || resourceData.endDate || ''
        });
        setShowEditResourceModal(true);
      })
      .catch(err => {
        console.error('获取数据错误:', err);
        setErrorMessage(err.message);
        setTimeout(() => setErrorMessage(''), 5000);
      })
      .finally(() => {
        setTaskLoading(false);
      });
  };

  const handleSaveResource = async () => {
    if (!editResourceForm.name) {
      setErrorMessage('请输入资源名称');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    try {
      // 判断是否是人力资源
      const isHumanResource = resourceTypes.find(t => t.id === selectedType)?.name.includes('人力');
      
      const resourceData = {
        id: editResourceForm.id,
        name: editResourceForm.name,
        projectId: editResourceForm.projectId ? parseInt(editResourceForm.projectId) : 0,
        projectName: editResourceForm.projectName || '',
        description: editResourceForm.description,
        position: editResourceForm.description, // 对于人力资源，description字段作为position使用
        type: parseInt(selectedType),
        status: parseInt(editResourceForm.status),
        startTime: editResourceForm.startDate, // 对于人力资源，startDate字段作为startTime使用
        endTime: editResourceForm.endDate // 对于人力资源，endDate字段作为endTime使用
      };

      if (isHumanResource) {
        await employeeResourceApi.updateEmployee(editResourceForm.id, resourceData);
      } else {
        await resourceApi.updateResource(editResourceForm.id, resourceData);
      }

      // 更新成功后重新获取资源列表
      if (isHumanResource && selectedDepartment) {
        const data = await employeeResourceApi.getEmployeeList(selectedDepartment, {
          page: 0,
          size: 10
        });
        setResources(data.content);
        setTotalPages(Math.max(1, data.totalPages || 1));
        setTotalRecords(data.totalElements || 0);
        setCurrentPage(1);
      } else {
        await fetchResourcesByType(selectedType);
      }

      setShowEditResourceModal(false);
      setEditResourceForm({
        id: '',
        name: '',
        projectName: '',
        projectId: '',
        description: '',
        type: '',
        status: 0,
        startDate: '',
        endDate: ''
      });

      // 显示成功提示
      setErrorMessage('更新资源成功');
      setTimeout(() => setErrorMessage(''), 3000);
    } catch (error) {
      console.error('更新资源失败:', error);
      setErrorMessage('更新资源失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleDeleteResource = async (resourceId) => {
    try {
      // 判断是否是人力资源
      const isHumanResource = resourceTypes.find(t => t.id === selectedType)?.name.includes('人力');
      
      let result;
      if (isHumanResource) {
        result = await employeeResourceApi.deleteEmployee(resourceId);
      } else {
        result = await resourceApi.deleteResource(resourceId);
      }

      if (result === true) {
        // 删除成功后刷新列表
        await fetchResourcesByType(selectedType);
        setShowDeleteResourceConfirm(false);
        setDeleteResourceId(null);
        
        // 显示成功提示
        setErrorMessage('删除资源成功');
        setTimeout(() => setErrorMessage(''), 3000);
      } else {
        setErrorMessage('删除资源失败');
        setTimeout(() => setErrorMessage(''), 3000);
      }
    } catch (error) {
      console.error('删除资源失败:', error);
      setErrorMessage('删除资源失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 点击删除按钮时的处理函数
  const handleDeleteClick = (resourceId) => {
    setDeleteResourceId(resourceId);
    setShowDeleteResourceConfirm(true);
  };

  const fetchProjects = async (searchName = '') => {
    try {
      setProjects(null); // 设置为null表示加载中状态
      
      const params = {
        name: searchName
      };

      console.log('请求项目列表参数:', params);
      
      // 使用projectApi接口代替直接fetch调用
      const data = await projectApi.getProjectList(params);
      
      // 检查返回的数据是否有效
      if (data) {
        // 确保项目数据格式统一，包含id和name字段
        const formattedProjects = Array.isArray(data) 
          ? data.map(project => ({
              id: project.id || '',
              name: project.name || ''
            })).filter(project => project.id && project.name) // 过滤掉无效项目
          : [];
        
        console.log('获取项目列表成功, 项目数量:', formattedProjects.length);
        if (formattedProjects.length === 0 && data.length > 0) {
          console.warn('项目数据格式不符合预期，原始数据:', data);
        }
        
        setProjects(formattedProjects);
        return formattedProjects; // 返回处理后的项目列表
      } else {
        console.error('获取项目列表返回格式异常:', data);
        setProjects([]);
        showError('获取项目列表返回的数据格式不正确');
        return [];
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setProjects([]);
      showError('获取项目列表失败，请重试');
      return [];
    }
  };

  const handleAssign = async () => {
    console.log('提交表单数据:', assignmentForm);
    
    if (!assignmentForm.projectId) {
      setErrorMessage('请选择项目');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }
    if (!assignmentForm.startDate) {
      setErrorMessage('请选择开始时间');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }
    if (!assignmentForm.endDate) {
      setErrorMessage('请选择结束时间');
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    try {
      // 获取当前选择的资源类型
      const selectedTypeName = resourceTypes.find(t => t.id === selectedType)?.name;
      
      // 构建参数对象
      const params = {
        projectId: assignmentForm.projectId,
        startDate: assignmentForm.startDate,
        endDate: assignmentForm.endDate
      };

      console.log('提交的参数:', params);

      if (selectedTypeName?.includes('人力')) {
        // 人力资源：添加employeeIds参数
        params.employeeIds = selectedResources.join(',');
        // 使用employeeResourceApi接口
        await employeeResourceApi.distributeEmployee(params);
      } else {
        // 其他资源：添加resourceId参数
        params.resourceId = selectedResources.join(',');
        // 使用resourceApi接口
        await resourceApi.distributeResource(params);
      }

      // 分配成功后刷新资源列表
      if (selectedDepartment) {
        handleDepartmentSelect(selectedDepartment);
      } else {
        fetchResourcesByType(selectedType, currentPage - 1);
      }

      // 重置表单和状态
      setShowAssignModal(false);
      setAssignmentForm({
        projectId: '',
        startDate: '',
        endDate: '',
        role: '',
        workload: '100%'
      });
      setSelectedResources([]);
      
      // 显示成功提示
      setErrorMessage('分配资源成功');
      setTimeout(() => setErrorMessage(''), 3000);
    } catch (error) {
      console.error('分配资源失败:', error);
      setErrorMessage('分配资源失败，请重试');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };



  // 添加获取资源名称的函数
  const getProjectName = (projectId) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : '-';
  };

  // 添加获取资源最后分配时间的函数
  const getResourceLastEndDate = (resourceId) => {
    const resource = resources.find(r => r.id === resourceId);
    return resource?.endDate || null;
  };

  // 获取部门列表
  const fetchDepartments = async () => {
    try {
      // 使用organizationService接口
      const data = await getAllOrganizations();
      
      // 确保每个部门对象都有唯一标识
      const formattedDepartments = data.map((dept, index) => ({
        ...dept,
        id: dept.id || index.toString() // 如果后端返回的数据没有id，使用索引作为id
      }));
      setDepartments(formattedDepartments);
    } catch (error) {
      console.error('获取部门数据失败:', error);
      setErrorMessage('获取部门数据失败');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 修改处理部门选择的函数
  const handleDepartmentSelect = async (orgId) => {
    setSelectedDepartment(orgId);
    
    // 获取部门下的资源列表
    if (resourceTypes.find(t => t.id === selectedType)?.name.includes('人力')) {
      try {
        const params = {
          page: 0,
          size: pageSize
        };
        
        // 使用employeeResourceApi接口替代直接fetch调用
        const data = await employeeResourceApi.getEmployeeList(orgId, params);
        
        // 格式化返回的数据
        const formattedData = data.content.map(employee => ({
          id: employee.id,
          name: employee.name,
          description: employee.position || '',
          status: employee.status || 0,
          type: selectedType,
          projectId: employee.projectId || null,
          projectName: employee.projectName || '-',
          startDate: employee.startTime || '-',
          endDate: employee.endTime || '-'
        }));
        
        setResources(formattedData);
        setTotalPages(Math.max(1, data.totalPages || 1));
        setTotalRecords(data.totalElements || 0);
        setCurrentPage(1);
      } catch (error) {
        console.error('获取部门资源失败:', error);
        setErrorMessage('获取部门资源失败');
        setTimeout(() => setErrorMessage(''), 3000);
      }
    } else {
      // 其他资源类型保持原有的获取逻辑
      await fetchResourcesByDepartment(orgId);
    }
  };

  // 修改根据部门获取资源的函数
  const fetchResourcesByDepartment = async (deptId) => {
    try {
      // 使用resourceApi接口替代直接fetch调用
      const data = await resourceApi.getResourcesByDepartment(deptId);
      
      // 如果返回的是数组，需要转换格式以匹配现有的状态结构
      const formattedData = {
        content: Array.isArray(data) ? data : data.content || [],
        totalElements: Array.isArray(data) ? data.length : data.totalElements || 0,
        totalPages: Array.isArray(data) ? 1 : data.totalPages || 1
      };

      setResources(formattedData.content);
      setTotalPages(Math.max(1, formattedData.totalPages));
      setTotalRecords(formattedData.totalElements);
      setCurrentPage(1);
    } catch (error) {
      console.error('获取部门资源失败:', error);
      setErrorMessage('获取部门资源失败');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 修改获取组织架构数据的函数
  const fetchOrganizations = async () => {
    try {
      // 使用统一的organizationService接口，带缓存功能
      const data = await getAllOrganizations();
      
      // 直接使用返回的数据结构
      if (data && data.children) {
        setOrganizations(data.children);
      } else {
        setOrganizations([]);
      }
    } catch (error) {
      console.error('获取组织架构数据失败:', error);
      setErrorMessage('获取组织架构数据失败');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 添加切换展开/折叠的处理函数
  const toggleExpand = (orgId) => {
    const newExpanded = new Set(expandedOrgs);
    if (newExpanded.has(orgId)) {
      newExpanded.delete(orgId);
    } else {
      newExpanded.add(orgId);
    }
    setExpandedOrgs(newExpanded);
  };




  // 添加新的 useEffect 来监听 selectedType 的变化
  useEffect(() => {
    if (selectedType && resourceTypes.length > 0) {
      const selectedTypeName = resourceTypes.find(t => t.id === selectedType)?.name;
      
      if (selectedTypeName?.includes('人力')) {
        // 如果是人力资源，获取组织架构数据
        const fetchData = async () => {
          try {
            await fetchOrganizations();
            setShowOrganizations(true);
            setShowDepartments(false);
            
            // 获取人力资源列表
            const response = await employeeResourceApi.getEmployeeList(0, {
              page: 0,
              size: 10
            });
            
            // 确保返回的数据是数组
            const employeesData = response.content || [];
            
            // 格式化数据
            const formattedData = employeesData.map(employee => ({
              id: employee.id,
              name: employee.name,
              description: employee.position || '',
              status: employee.status || 0,
              startDate: employee.startTime || '-',
              endDate: employee.endTime || '-',
              type: selectedType,
              projectId: employee.projectId || null,
              projectName: employee.projectName || '-'
            }));
            
            setResources(formattedData);
            setTotalPages(Math.max(1, response.totalPages || 1));
            setTotalRecords(response.totalElements || 0);
          } catch (error) {
            console.error('获取人力资源数据失败:', error);
            setErrorMessage('获取人力资源数据失败');
            setTimeout(() => setErrorMessage(''), 3000);
            // 设置空数据
            setResources([]);
            setTotalPages(1);
            setTotalRecords(0);
          }
        };
        
        fetchData();
      } else {
        // 其他资源类型使用原有接口
        fetchResourcesByType(selectedType, 0);
      }
    }
  }, [selectedType, resourceTypes]);

  // 修改初始化的 useEffect
  useEffect(() => {
    const initializeData = async () => {
      try {
        const params = {
          page: 0,
          size: 10
        };
        
        const data = await resourceTypeApi.getAllTypes(params);
        
        if (data.content && data.content.length > 0) {
          const formattedTypes = data.content.map(type => ({
            id: type.id.toString(),
            name: type.name,
            description: type.description,
            icon: type.name.includes('人力') ? PersonIcon :
              type.name.includes('设备') ? CubeIcon :
                type.name.includes('技术') ? GearIcon : CircleIcon
          }));
          
          setResourceTypes(formattedTypes);
          
          // 选择第一个类型并加载其资源
          const firstType = formattedTypes[0];
          await handleTypeSelect(firstType.id);
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
        setErrorMessage('加载数据失败，请刷新页面重试');
      }
    };
    
    initializeData();
  }, []);



  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50">
      {/* 全局消息提示 */}
      {errorMessage && (
        <div className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] flex items-center px-4 py-2 rounded-lg shadow-lg ${
          errorMessage.includes('成功') ? 'bg-green-50 border border-green-100' : 'bg-white border border-red-100'
        }`}>
          {errorMessage.includes('成功') ? (
            <CheckCircledIcon className="w-4 h-4 text-green-500 mr-2" />
          ) : (
            <Cross2Icon className="w-4 h-4 text-red-500 mr-2" />
          )}
          <span className={`text-sm ${errorMessage.includes('成功') ? 'text-green-600' : 'text-gray-600'}`}>
            {errorMessage}
          </span>
        </div>
      )}

      {/* 页面标题 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">项目资源分配</h1>
          <p className="text-gray-500">选择项目并分配所需资源</p>
        </div>
      </div>

      {/* 主要内容区域：左右分栏布局 */}
      <div className="flex gap-6 h-[calc(100vh-180px)]">
        {/* 左侧资源类型管理面板 */}
        <div className="w-64 bg-white rounded-lg shadow-sm p-4 overflow-y-auto">
          <h3 className="font-medium text-gray-700 mb-4">资源类型</h3>

          {/* 资源类型搜索和新建 */}
          <div className="flex items-center gap-2 mb-4">
            <div className="relative flex-1">
              <input
                type="text"
                value={typeSearchQuery}
                onChange={(e) => handleTypeSearch(e.target.value)}
                placeholder="搜索资源类型..."
                className="w-full pl-8 pr-2 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
            </div>
            <Button
              size="icon"
              variant="ghost"
              className="shrink-0"
              onClick={() => setShowNewTypeModal(true)}
            >
              <PlusIcon className="w-4 h-4" />
            </Button>
          </div>

          <div className="space-y-2">

            {resourceTypes.map(type => (
              <div key={type.id}>
                <div
                  className={`group flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors
                    ${selectedType === type.id ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50'}`}
                >
                  <div
                    className="flex items-center gap-2 flex-1"
                    onClick={() => handleTypeSelect(type.id)}
                  >
                    <type.icon className="w-4 h-4" />
                    <span>{type.name}</span>
                  </div>
                  <div className="flex items-center gap-1 transition-opacity">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 text-gray-500 hover:text-blue-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditType(type);
                      }}
                    >
                      <Pencil1Icon className="w-4 h-4" />
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 text-gray-500 hover:text-red-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteType(type.id);
                      }}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                {/* 显示组织架构树形结构 */}
                {showOrganizations && 
                 selectedType === type.id && 
                 type.name.includes('人力') && (
                  <div className="ml-8 mt-2">
                    {Array.isArray(organizations) && organizations.map((org) => (
                      <div key={org.id}>
                        {/* 显示部门名称 */}
                        <div 
                          className={`flex items-center gap-2 py-2 text-sm cursor-pointer transition-colors
                            ${selectedDepartment === org.id
                              ? 'bg-blue-50 text-blue-600' 
                              : 'text-gray-600 hover:bg-gray-50'
                            }`}
                        >
                          {/* 添加展开/折叠箭头 */}
                          {org.children && org.children.length > 0 && (
                            <ChevronRightIcon 
                              className={`w-4 h-4 transition-transform ${expandedOrgs.has(org.id) ? 'transform rotate-90' : ''}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleExpand(org.id);
                              }}
                            />
                          )}
                          <span 
                            className="flex-1"
                            onClick={() => handleDepartmentSelect(org.id)}
                          >
                            {org.name}
                          </span>
                        </div>
                        
                        {/* 显示子部门，添加展开/折叠控制 */}
                        {org.children && org.children.length > 0 && expandedOrgs.has(org.id) && (
                          <div className="ml-4">
                            {org.children.map((child) => (
                              <div key={child.id}>
                                <div 
                                  className={`flex items-center gap-2 py-2 text-sm cursor-pointer transition-colors
                                    ${selectedDepartment === child.id
                                      ? 'bg-blue-50 text-blue-600' 
                                      : 'text-gray-600 hover:bg-gray-50'
                                    }`}
                                >
                                  {/* 子部门的展开/折叠箭头 */}
                                  {child.children && child.children.length > 0 && (
                                    <ChevronRightIcon 
                                      className={`w-4 h-4 transition-transform ${expandedOrgs.has(child.id) ? 'transform rotate-90' : ''}`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        toggleExpand(child.id);
                                      }}
                                    />
                                  )}
                                  <span 
                                    className="flex-1"
                                    onClick={() => handleDepartmentSelect(child.id)}
                                  >
                                    {child.name}
                                  </span>
                                </div>
                                
                                {/* 显示第三级部门 */}
                                {child.children && child.children.length > 0 && expandedOrgs.has(child.id) && (
                                  <div className="ml-4">
                                    {child.children.map((subChild) => (
                                      <div 
                                        key={subChild.id}
                                        className={`flex items-center gap-2 py-2 text-sm cursor-pointer transition-colors
                                          ${selectedDepartment === subChild.id
                                            ? 'bg-blue-50 text-blue-600' 
                                            : 'text-gray-600 hover:bg-gray-50'
                                          }`}
                                        onClick={() => handleDepartmentSelect(subChild.id)}
                                      >
                                        <span className="flex-1">{subChild.name}</span>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
                
                {/* 部门显示逻辑 */}
                {showDepartments && 
                 selectedType === type.id && 
                 type.name.includes('设备') && (
                  <div className="ml-8 mt-2">
                    {departments.map((dept) => (
                      <div 
                        key={dept.id}
                        className={`flex items-center gap-2 py-2 text-sm cursor-pointer transition-colors
                          ${selectedDepartment === dept.id 
                            ? 'bg-blue-50 text-blue-600' 
                            : 'text-gray-600 hover:bg-gray-50'
                          }`}
                        onClick={() => handleDepartmentSelect(dept.id)}
                      >
                        <span className="px-3">{dept.name}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 右侧资源列表管理区域 */}
        <div className="flex-1 bg-white rounded-lg shadow-sm p-4 overflow-hidden flex flex-col">
          {/* 资源操作工具栏 */}
          <div className="flex gap-4 mb-4">
            <div className="flex items-center space-x-2 mb-4">
              {/* 资源搜索框 */}
              <div className="relative flex-1">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                  }}
                  className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="搜索资源..."
                />
                <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
              </div>
              {/* 新建资源按钮（仅非人力资源显示） */}
              {!resourceTypes.find(t => t.id === selectedType)?.name.includes('人力') && (
                <Button
                  onClick={() => setShowNewProjectModal(true)}
                  className="shrink-0"
                >
                  <PlusIcon className="w-4 h-4 mr-2" />
                  新建资源
                </Button>
              )}
              {/* 批量分配按钮 */}
              <Button
                onClick={() => setShowAssignModal(true)}
                className="shrink-0"
                disabled={selectedResources.length === 0}
              >
                <Link2Icon className="w-4 h-4 mr-2" />
                分配 {selectedResources.length > 0 && `(${selectedResources.length})`}
              </Button>
            </div>
          </div>

          {/* 资源列表 */}
          <div className="flex-1 flex flex-col min-h-0">
            {/* 表头 */}
            <div className="grid grid-cols-7 gap-4 px-4 py-3 bg-gray-50 border-b font-medium text-sm text-gray-600">
              <div className="flex items-center gap-3">
                {/* 添加全选复选框 */}
                <input
                  type="checkbox"
                  checked={
                    resources.filter(r => r.status === 0).length > 0 && 
                    resources.filter(r => r.status === 0)
                      .every(r => selectedResources.includes(r.id))
                  }
                  onChange={(e) => {
                    if (e.target.checked) {
                      const unallocatedResourceIds = resources
                        .filter(r => r.status === 0)
                        .map(r => r.id);
                      setSelectedResources([...selectedResources, ...unallocatedResourceIds]);
                    } else {
                      const unallocatedResourceIds = resources
                        .filter(r => r.status === 0)
                        .map(r => r.id);
                      setSelectedResources(selectedResources.filter(id => !unallocatedResourceIds.includes(id)));
                    }
                  }}
                  className="w-4 h-4 rounded border-gray-300 text-blue-600"
                />
                资源名称
              </div>
              <div>状态</div>
              <div>分配项目</div>
              <div>描述</div>
              <div>开始时间</div>
              <div>结束时间</div>
              <div className="text-right">操作</div>
            </div>

            {/* 列表内容 */}
            <div className="flex-1 overflow-y-auto">
              <div className="divide-y">
                {Array.isArray(resources) && resources.map(resource => (
                  <div
                    key={resource.id}
                    className="grid grid-cols-7 gap-4 px-4 py-4 hover:bg-gray-50 items-center"
                  >
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={selectedResources.includes(resource.id)}
                        disabled={resource.status === 1} // 已分配的资源禁用选择
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedResources([...selectedResources, resource.id]);
                          } else {
                            setSelectedResources(selectedResources.filter(id => id !== resource.id));
                          }
                        }}
                        className={`w-4 h-4 rounded border-gray-300 ${
                          resource.status === 1 
                            ? 'text-gray-300 cursor-not-allowed' 
                            : 'text-blue-600'
                        }`}
                      />
                      <span className="font-medium">{resource.name}</span>
                    </div>
                    <div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${resource.status === 1 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                        {resource.status === 1 ? '已分配' : '未分配'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {resourceTypes.find(t => t.id === selectedType)?.name.includes('人力') 
                        ? (resource.projectName || '-')
                        : getProjectName(resource.projectId)}
                    </div>
                    <div className="text-sm text-gray-600 truncate">
                      {resource.description || '-'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {resource.startDate || '-'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {resource.endDate || '-'}
                    </div>
                    {/* 操作列 */}
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        onClick={() => handleEditResource(resource)}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>
                      {!resourceTypes.find(t => t.id === selectedType)?.name.includes('人力') && (
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8"
                          onClick={() => handleDeleteClick(resource.id)}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 分页器 */}
            <div className="mt-4 py-2 flex justify-end items-center text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <span>
                  共 {totalRecords} 条记录
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 hover:bg-gray-100"
                >
                  上一页
                </Button>
                
                {/* 添加页码显示逻辑 */}
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 min-w-[32px] ${
                      pageNum === currentPage 
                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                        : 'hover:bg-gray-100'
                    }`}
                  >
                    {pageNum}
                  </Button>
                ))}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 hover:bg-gray-100"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 新建项目弹窗 */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建资源</h3>
              <button
                onClick={() => setShowNewProjectModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    资源类型 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={resourceTypes.find(t => t.id === selectedType)?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    资源名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newProjectForm.name}
                    onChange={(e) => setNewProjectForm({
                      ...newProjectForm,
                      name: e.target.value
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入资源名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    资源描述
                  </label>
                  <textarea
                    value={newProjectForm.description}
                    onChange={(e) => setNewProjectForm({
                      ...newProjectForm,
                      description: e.target.value
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    placeholder="请输入资源描述"
                  />
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewProjectModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateProject}>
                确认创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 资源分配弹窗 */}
      {showAssignModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">分配资源</h3>
              <button
                onClick={() => setShowAssignModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择项目 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={assignmentForm.projectId}
                    onChange={(e) => setAssignmentForm(prev => ({
                      ...prev,
                      projectId: e.target.value
                    }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择项目</option>
                    {Array.isArray(projects) && projects.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={assignmentForm.startDate}
                      min={selectedResources.length === 1 ?
                        getResourceLastEndDate(selectedResources[0]) || new Date().toISOString().split('T')[0] :
                        new Date().toISOString().split('T')[0]}
                      onChange={(e) => {
                        const newStartDate = e.target.value;
                        setAssignmentForm(prev => ({
                          ...prev,
                          startDate: newStartDate,
                          // 如果结束时间早于新的开始时间，更新结束时间
                          endDate: prev.endDate && new Date(prev.endDate) < new Date(newStartDate) ?
                            newStartDate : prev.endDate
                        }));
                      }}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结束时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={assignmentForm.endDate}
                      min={assignmentForm.startDate ||
                        (selectedResources.length === 1 ?
                          getResourceLastEndDate(selectedResources[0]) || new Date().toISOString().split('T')[0] :
                          new Date().toISOString().split('T')[0])}
                      onChange={(e) => setAssignmentForm(prev => ({
                        ...prev,
                        endDate: e.target.value
                      }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  已选择 {selectedResources.length} 个资源
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowAssignModal(false)}
              >
                取消
              </Button>
              <Button
                onClick={handleAssign}
              >
                确认分配
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* 添加新建资源类型弹窗 */}
      {showNewTypeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">
                {editingTypeId ? '编辑资源类型' : '新建资源类型'}
              </h3>
              <button
                onClick={handleCloseModal}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    类型名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newTypeForm.name}
                    onChange={(e) => setNewTypeForm({
                      ...newTypeForm,
                      name: e.target.value
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入资源类型名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    类型描述
                  </label>
                  <textarea
                    value={newTypeForm.description}
                    onChange={(e) => setNewTypeForm({
                      ...newTypeForm,
                      description: e.target.value
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    placeholder="请输入资源类型描述"
                  />
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCloseModal}
              >
                取消
              </Button>
              <Button onClick={handleSaveResourceType}>
                {editingTypeId ? '确认更新' : '确认创建'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[320px]">
            <div className="p-4">
              <h3 className="text-base font-medium mb-2">确认删除</h3>
              <p className="text-sm text-gray-500">确定要删除此资源吗？</p>
            </div>
            <div className="flex border-t">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-3 text-gray-500 hover:bg-gray-50 text-sm font-medium border-r"
              >
                取消
              </button>
              <button
                onClick={confirmDelete}
                className="flex-1 px-4 py-3 text-red-500 hover:bg-red-50 text-sm font-medium"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 删除资源确认对话框 */}
      {showDeleteResourceConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[320px]">
            <div className="p-4">
              <h3 className="text-base font-medium mb-2">确认删除</h3>
              <p className="text-sm text-gray-500">确定要删除此资源吗？</p>
            </div>
            <div className="flex border-t">
              <button
                onClick={() => {
                  setShowDeleteResourceConfirm(false);
                  setDeleteResourceId(null);
                }}
                className="flex-1 px-4 py-3 text-gray-500 hover:bg-gray-50 text-sm font-medium border-r"
              >
                取消
              </button>
              <button
                onClick={() => handleDeleteResource(deleteResourceId)}
                className="flex-1 px-4 py-3 text-red-500 hover:bg-red-50 text-sm font-medium"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑资源弹窗 */}
      {showEditResourceModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            {taskLoading ? (
              <div className="p-6 text-center">
                <div className="text-gray-500">加载资源数据中...</div>
              </div>
            ) : (
              <>
                <div className="p-6 border-b flex justify-between items-center">
                  <h3 className="text-xl font-semibold">编辑资源</h3>
                  <button
                    onClick={() => setShowEditResourceModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg"
                  >
                    <Cross2Icon className="w-4 h-4" />
                  </button>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        资源类型 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={resourceTypes.find(t => t.id === selectedType)?.name || ''}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    {/* 修改这里：将资源名称和分配项目放在同一行 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          资源名称 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          value={editResourceForm.name}
                          onChange={(e) => setEditResourceForm({
                            ...editResourceForm,
                            name: e.target.value
                          })}
                          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="请输入资源名称"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          分配项目
                        </label>
                        <div className="relative project-dropdown-container">
                          <div
                            onClick={() => editResourceForm.status === 0 ? null : setShowProjectDropdown(!showProjectDropdown)}
                            className={`w-full px-3 py-2 border rounded-lg flex items-center justify-between ${
                              editResourceForm.status === 0 ? 'bg-gray-50 cursor-not-allowed' : 
                              !editResourceForm.projectId ? 'border-gray-300 cursor-pointer' : 'border-gray-300 hover:border-blue-500 cursor-pointer'
                            }`}
                          >
                            <span className={editResourceForm.projectId ? 'text-gray-900' : 'text-gray-400'}>
                              {editResourceForm.projectId ? (editResourceForm.projectName || projects.find(p => p.id.toString() === editResourceForm.projectId)?.name || '未知项目') : '请选择项目'}
                            </span>
                            {editResourceForm.status !== 0 && (
                              <svg 
                                className={`h-5 w-5 text-gray-400 transform transition-transform ${showProjectDropdown ? 'rotate-180' : ''}`} 
                                viewBox="0 0 20 20" 
                                fill="currentColor"
                              >
                                <path 
                                  fillRule="evenodd" 
                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                                  clipRule="evenodd" 
                                />
                              </svg>
                            )}
                          </div>
                          
                          {showProjectDropdown && editResourceForm.status !== 0 && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                              <div className="py-1 max-h-60 overflow-auto">
                                <div
                                  onClick={() => {
                                    setEditResourceForm({
                                      ...editResourceForm,
                                      projectId: '',
                                      projectName: ''
                                    });
                                    setShowProjectDropdown(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    !editResourceForm.projectId ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  请选择项目
                                </div>
                                {projects.map(project => (
                                  <div
                                    key={project.id}
                                    onClick={() => {
                                      setEditResourceForm({
                                        ...editResourceForm,
                                        projectId: project.id.toString(),
                                        projectName: project.name
                                      });
                                      setShowProjectDropdown(false);
                                    }}
                                    className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                      editResourceForm.projectId === project.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                    }`}
                                  >
                                    {project.name}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        资源描述
                      </label>
                      <textarea
                        value={editResourceForm.description}
                        onChange={(e) => setEditResourceForm({
                          ...editResourceForm,
                          description: e.target.value
                        })}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        rows={2}
                        placeholder="请输入资源描述"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        资源状态
                      </label>
                      <input
                        type="text"
                        value={editResourceForm.status === 1 ? '已分配' : '未分配'}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          开始时间
                        </label>
                        <input
                          type="date"
                          value={editResourceForm.startDate}
                          disabled={!editResourceForm.startDate}
                          onChange={(e) => setEditResourceForm({
                            ...editResourceForm,
                            startDate: e.target.value
                          })}
                          className={`w-full px-3 py-2 border rounded-lg ${!editResourceForm.startDate
                              ? 'bg-gray-50 text-gray-500 cursor-not-allowed'
                              : 'focus:outline-none focus:ring-2 focus:ring-blue-500'
                            }`}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          结束时间
                        </label>
                        <input
                          type="date"
                          value={editResourceForm.endDate}
                          disabled={!editResourceForm.endDate}
                          onChange={(e) => setEditResourceForm({
                            ...editResourceForm,
                            endDate: e.target.value
                          })}
                          className={`w-full px-3 py-2 border rounded-lg ${!editResourceForm.endDate
                              ? 'bg-gray-50 text-gray-500 cursor-not-allowed'
                              : 'focus:outline-none focus:ring-2 focus:ring-blue-500'
                            }`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowEditResourceModal(false)}
                  >
                    取消
                  </Button>
                  <Button onClick={handleSaveResource}>
                    确认更新
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 分配弹窗 */}
      {showDistributeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">分配资源</h3>
              <button
                onClick={() => setShowDistributeModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={distributeForm.projectId}
                    onChange={(e) => setDistributeForm({
                      ...distributeForm,
                      projectId: e.target.value
                    })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择项目</option>
                    {projects.map(project => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={distributeForm.startDate}
                      onChange={(e) => setDistributeForm({
                        ...distributeForm,
                        startDate: e.target.value
                      })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结束时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={distributeForm.endDate}
                      onChange={(e) => setDistributeForm({
                        ...distributeForm,
                        endDate: e.target.value
                      })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowDistributeModal(false)}
              >
                取消
              </Button>
              <Button 
                onClick={handleDistribute}
                disabled={!distributeForm.projectId || !distributeForm.startDate || !distributeForm.endDate}
              >
                确认分配
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 如果选择的资源已被分配，显示提示信息 */}
      {selectedResources.length === 1 && 
       resources.find(r => r.id === selectedResources[0])?.status === 1 && 
       getResourceLastEndDate(selectedResources[0]) && (
        <div className="text-sm text-orange-600">
          该资源已被分配，只能从 {getResourceLastEndDate(selectedResources[0])} 之后选择时间
        </div>
      )}
    </div>
  );
});
