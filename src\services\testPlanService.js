import axios from 'axios';

import { fetchData } from './fetch';


// 测试计划相关接口
export const testPlanApi = {
  // 获取项目列表
  getProjectList: (searchName = '') => {
    const params = new URLSearchParams({ name: searchName });
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${params}`).then(res => res.json());
  },

  // 获取测试计划列表
  getTestPlanList: (projectId, page, size, name, status, creatorId) => {
    const params = new URLSearchParams({
      projectId,
      page,
      size,
      name,
      status,
      creatorId
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/all?${params}`).then(res => res.json());
  },

  // 获取单个测试计划详情
  getTestPlanDetail: (planId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/one/${planId}`).then(res => res.json()),

  // 创建测试计划
  createTestPlan: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新测试计划
  updateTestPlan: (planId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/update/${planId}`, {
      method: 'PUT',
      body: formData
    }).then(res => res.json()),

  // 删除测试计划
  deleteTestPlan: (planId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/delete/${planId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 文件预览
  getFilePreviewUrl: (fileName) => {
    const params = new URLSearchParams({
      fileName,
      bucketName: 'testplan'
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 文件下载
  getFileDownloadUrl: (fileName) => {
    const params = new URLSearchParams({
      fileName,
      bucketName: 'testplan'
    });
    return `${fetchData["PROJECT_URL"]}/api/file/download?${params}`;
  },

  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
}; 