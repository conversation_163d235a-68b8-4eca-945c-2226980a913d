import React from 'react';
import { observer } from 'mobx-react-lite';
import { chatStore } from '../store/chatStore';
import { ChatBubbleIcon, Cross2Icon } from '@radix-ui/react-icons';

export const MinimizedChat = observer(() => {
  const { isOpen, isMinimized, currentConversation } = chatStore;

  if (!isOpen || !isMinimized) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg">
      <div 
        className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50"
        onClick={() => chatStore.minimizeChat()}
      >
        <ChatBubbleIcon className="w-5 h-5 text-blue-500" />
        <span className="font-medium">
          {currentConversation ? currentConversation.name : '聊天'}
        </span>
        <button
          onClick={(e) => {
            e.stopPropagation();
            chatStore.toggleChat();
          }}
          className="ml-2 p-1 hover:bg-gray-100 rounded"
        >
          <Cross2Icon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
});