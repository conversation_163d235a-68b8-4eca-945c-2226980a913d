import React, { useState, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useNavigate } from 'react-router-dom';
import { navigationStore } from '../store/navigationStore';
import { userStore } from '../store/userStore';
import { ChangePasswordModal } from './ChangePasswordModal';
import { 
  PersonIcon,
  LayersIcon,
  CodeIcon,
  MixerVerticalIcon,
  DashboardIcon,
  BackpackIcon,
  BoxIcon,
  CheckCircledIcon,
  BarChartIcon,
  Link2Icon,
  GearIcon,
  LockClosedIcon,
  ExitIcon
} from '@radix-ui/react-icons';

const systems = [
  { icon: PersonIcon, text: '人事管理' },
  { icon: LayersIcon, text: '项目管理' },
  { icon: CodeIcon, text: '研发管理' },
  // { icon: MixerVerticalIcon, text: '生产管理' },
  // { icon: DashboardIcon, text: '财务管理' },
  // { icon: BackpackIcon, text: '采购管理' },
  // { icon: BoxIcon, text: '仓储管理' },
  // { icon: CheckCircledIcon, text: '质量管理' },
  // { icon: BarChartIcon, text: '销售管理' },
  // { icon: Link2Icon, text: '供应链管理' }
];

export const SystemSidebar = observer(() => {
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const { currentSystem, setCurrentSystem, setCurrentSubPage } = navigationStore;
  const menuRef = useRef(null);

  const handleMenuClick = (e, page) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (page === 'logout') {
      userStore.logout();
    } else if (page === 'password') {
      setShowPasswordModal(true);
    } else {
      userStore.setCurrentPage(page);
    }
    setShowUserMenu(false);
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSystemClick = (system) => {
    setCurrentSystem(system);
    
    // 根据点击的系统导航到对应的默认页面
    switch(system) {
      case '人事管理':
        navigate('/hr-management/personnel');
        setCurrentSubPage('人员管理');
        break;
      case '项目管理':
        navigate('/project-management/create');
        setCurrentSubPage('项目创建');
        break;
      case '研发管理':
        navigate('/dev-management/design-input');
        setCurrentSubPage('设计输入');
        break;
      // ... 其他系统的处理
    }
  };

  return (
    <div className="w-20 bg-[#1e293b] h-screen flex flex-col items-center py-4 overflow-y-auto scrollbar-none relative">
      <div 
        ref={menuRef}
        className="w-8 h-8 bg-blue-500 rounded-full mb-8 flex-shrink-0 flex items-center justify-center cursor-pointer relative"
        onClick={(e) => {
          e.stopPropagation();
          setShowUserMenu(!showUserMenu);
        }}
      >
        <PersonIcon className="w-5 h-5 text-white" />
        {showUserMenu && (
          <div className="fixed top-4 left-20 w-48 bg-white rounded-lg shadow-lg py-2 border border-gray-100 z-[9999]">
            <button
              className="w-full px-4 py-2 text-left flex items-center gap-2 hover:bg-gray-50"
              onClick={(e) => handleMenuClick(e, 'password')}
            >
              <LockClosedIcon className="w-4 h-4" />
              <span>修改密码</span>
            </button>
            <button
              className="w-full px-4 py-2 text-left flex items-center gap-2 hover:bg-gray-50"
              onClick={(e) => handleMenuClick(e, 'settings')}
            >
              <GearIcon className="w-4 h-4" />
              <span>用户设置</span>
            </button>
            <div className="h-px bg-gray-100 my-2"></div>
            <button
              className="w-full px-4 py-2 text-left flex items-center gap-2 hover:bg-gray-50 text-red-600"
              onClick={(e) => handleMenuClick(e, 'logout')}
            >
              <ExitIcon className="w-4 h-4" />
              <span>退出</span>
            </button>
          </div>
        )}
      </div>
      <nav className="flex-1 w-full">
        {systems.map((item, index) => (
          <div
            key={index}
            className={`w-full flex flex-col items-center p-2 cursor-pointer ${
              item.text === currentSystem
                ? 'text-white bg-blue-500' 
                : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
            onClick={() => handleSystemClick(item.text)}
          >
            <item.icon className="w-5 h-5 mb-1" />
            <span className="text-xs text-center leading-tight">{item.text.slice(0, 2)}</span>
          </div>
        ))}
      </nav>
      <ChangePasswordModal 
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
      />
    </div>
  );
});