/**
 * 集成基线管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和选择
 * 2. 基线列表的查看、搜索和分页
 * 3. 基线的创建、编辑、删除操作
 * 4. 基线详情查看
 * 5. 文件上传、预览和下载
 */

import { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  TrashIcon,
  Pencil1Icon,
  EyeOpenIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { integrationBaselineApi, fileApi, projectApi } from '../services/integrationBaselineService';

// 初始化新基线表单数据
const initNewBaseline = {
  name: '',
  description: '',
  attachments: []
};

/**
 * 集成基线管理组件
 */
export const IntegrationBaseline = observer(() => {
  // 基础数据状态
  const [baselines, setBaselines] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  // 弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);

  // 表单数据
  const [selectedBaseline, setSelectedBaseline] = useState(null);
  const [newBaseline, setNewBaseline] = useState(initNewBaseline);
  const [editingBaseline, setEditingBaseline] = useState(null);
  const [editingFiles, setEditingFiles] = useState([]);

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(10);

  // 错误和消息状态
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [formErrors, setFormErrors] = useState({});

  // 删除确认状态
  const [deletingItem, setDeletingItem] = useState(null);

  // 查看基线详情
  const handleViewDetail = async (baseline) => {
    setIsLoading(true);
    try {
      const response = await integrationBaselineApi.getBaselineDetail(baseline.id);
      if (response.data) {
        setSelectedBaseline(response.data);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('获取基线详情失败:', error);
      setError('获取基线详情失败，请重试');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // 创建基线
  const handleCreateBaseline = async () => {
    // 表单验证
    const errors = {};
    if (!newBaseline.name) errors.name = '请输入基线名称';
    if (!selectedProject) errors.project = '请选择项目';

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setIsCreating(true);
    setError('');

    try {
      const formData = new FormData();
      const integrationLine = {
        projectId: selectedProject.id,
        name: newBaseline.name,
        description: newBaseline.description || '',
        codeAddress: ''
      };

      formData.append('integrationLine', new Blob([JSON.stringify(integrationLine)], {
        type: 'application/json'
      }), 'integrationLine.json');

      newBaseline.attachments.forEach(file => {
        formData.append('files', file);
      });

      const response = await integrationBaselineApi.createBaseline(formData);
      if (response.data) {
        fetchBaselines(selectedProject.id);
        setShowNewModal(false);
        setNewBaseline(initNewBaseline);
        setFormErrors({});
        setSuccessMessage('基线创建成功');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('创建基线失败:', error);
      setError(error.response?.data?.message || '创建失败，请重试');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsCreating(false);
    }
  };

  // 删除基线
  const handleDeleteBaseline = (baseline) => {
    setDeletingItem(baseline);
    setShowDeleteConfirmModal(true);
  };

  // 编辑基线
  const handleEditBaseline = async (baseline) => {
    setIsLoading(true);
    try {
      const response = await integrationBaselineApi.getBaselineDetail(baseline.id);
      if (response.data) {
        setEditingBaseline(response.data);
        setShowEditModal(true);
      }
    } catch (error) {
      console.error('获取基线详情失败:', error);
      setError('获取基线详情失败，请重试');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理编辑文件变化
  const handleEditFileChange = (e) => {
    const files = Array.from(e.target.files);
    setEditingFiles([...editingFiles, ...files]);
  };

  // 更新基线
  const handleUpdateBaseline = async () => {
    if (!editingBaseline.name) {
      setError('请输入基线名称');
      return;
    }

    setIsUpdating(true);
    setError('');

    try {
      const formData = new FormData();
      const integrationLine = {
        id: editingBaseline.id,
        projectId: selectedProject.id,
        name: editingBaseline.name,
        createdTime: editingBaseline.createdTime,
        description: editingBaseline.description || '',
        projectFiles: editingBaseline.projectFiles || [],
        fileIds: editingBaseline.projectFiles ? editingBaseline.projectFiles.map(file => file.id) : []
      };

      formData.append('integrationLine', new Blob([JSON.stringify(integrationLine)], {
        type: 'application/json'
      }), 'integrationLine.json');

      editingFiles.forEach(file => {
        formData.append('files', file);
      });

      const response = await integrationBaselineApi.updateBaseline(editingBaseline.id, formData);
      if (response.data) {
        fetchBaselines(selectedProject.id);
        setShowEditModal(false);
        setEditingBaseline(null);
        setEditingFiles([]);
        setSuccessMessage('基线更新成功');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('更新基线失败:', error);
      setError(error.response?.data?.message || '更新失败，请重试');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsUpdating(false);
    }
  };

  // 处理文件变化
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setNewBaseline({
      ...newBaseline,
      attachments: [...newBaseline.attachments, ...files]
    });
  };

  // 移除文件
  const handleRemoveFile = (index) => {
    setNewBaseline({
      ...newBaseline,
      attachments: newBaseline.attachments.filter((_, i) => i !== index)
    });
  };

  // 打开新建弹窗
  const handleOpenNewModal = () => {
    setShowNewModal(true);
  };

  // 获取基线列表
  const fetchBaselines = useCallback(async (projectId) => {
    setIsLoading(true);
    try {
      const response = await integrationBaselineApi.getBaselineList(projectId, currentPage, pageSize, searchQuery);
      if (response.data) {
        setBaselines(response.data.content || []);
        setTotalPages(response.data.totalPages || 0);
        setTotalRecords(response.data.totalElements || 0);
      }
    } catch (error) {
      console.error('获取基线列表失败:', error);
      setError('获取基线列表失败，请重试');
      setBaselines([]);
      setTotalPages(0);
      setTotalRecords(0);
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize, searchQuery]);

  // 获取项目列表
  const fetchProjects = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await projectApi.getProjectList();
      if (response.data) {
        setProjects(response.data);
        if (response.data.length > 0 && !selectedProject) {
          setSelectedProject(response.data[0]);
        }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setError('获取项目列表失败，请重试');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsLoading(false);
    }
  }, [selectedProject]);

  // 选择项目
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    fetchBaselines(project.id);
  };

  // 预览文件
  const handlePreviewFile = async (file) => {
    try {
      const response = await fileApi.previewFile(file.name, 'integrationline');
      if (response.data) {
        window.open(response.data, '_blank');
      }
    } catch (error) {
      console.error('获取文件预览失败:', error);
      setError('获取文件预览失败，请重试');
      setTimeout(() => setError(''), 3000);
    }
  };

  // 下载文件
  const handleDownloadFile = async (file) => {
    try {
      const response = await fileApi.downloadFile(file.name, 'integrationline');
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setSuccessMessage('下载成功');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('下载文件失败:', error);
      setError('下载文件失败，请重试');
      setTimeout(() => setError(''), 3000);
    }
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    try {
      await integrationBaselineApi.deleteBaseline(deletingItem.id);
      fetchBaselines(selectedProject.id);
      setShowDeleteConfirmModal(false);
      setDeletingItem(null);
      setSuccessMessage('删除成功');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('删除失败:', error);
      setError(error.response?.data?.message || '删除失败，请重试');
      setTimeout(() => setError(''), 3000);
    }
  };

  // 搜索基线
  const handleSearch = async () => {
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  };

  // 重置搜索
  const handleReset = async () => {
    setSearchQuery('');
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  };

  // 分页处理
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // 组件初始化时获取项目列表
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // 当项目或分页变化时获取基线列表
  useEffect(() => {
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  }, [currentPage, selectedProject, fetchBaselines]);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 错误消息提示 */}
      {error && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
          {error}
        </div>
      )}

      {/* 成功消息提示 */}
      {successMessage && (
        <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50">
          {successMessage}
        </div>
      )}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''}`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  project.status === 0 ? 'bg-gray-100 text-gray-600' :
                  project.status === 1 ? 'bg-blue-100 text-blue-600' :
                  project.status === 2 ? 'bg-green-100 text-green-600' : ''
                }`}>
                  {project.status === 0 ? '未开始' :
                   project.status === 1 ? '进行中' :
                   project.status === 2 ? '已结束' : '未知状态'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">集成基线管理</p>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索基线..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleSearch}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  style={{ backgroundColor: '#007bff', color: 'white' }}
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={handleOpenNewModal}
                >
                  <PlusIcon className="w-4 h-4" />
                  创建基线
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <div className="divide-y">
              <div className="p-4 bg-gray-50">
                <div className="grid grid-cols-4 gap-4">
                  <div className="text-sm font-medium text-gray-500">基线名称</div>
                  <div className="text-sm font-medium text-gray-500">创建时间</div>
                  <div className="text-sm font-medium text-gray-500">描述</div>
                  <div className="text-sm font-medium text-gray-500">操作</div>
                </div>
              </div>
              {baselines.map(baseline => (
                <div key={baseline.id} className="p-4 hover:bg-gray-50">
                  <div className="grid grid-cols-4 gap-4">
                    <div className="text-sm">{baseline.name}</div>
                    <div className="text-sm">{baseline.createdTime}</div>
                    <div className="text-sm truncate">{baseline.description}</div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => handleViewDetail(baseline)}
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleEditBaseline(baseline)}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="text-red-600"
                        onClick={() => handleDeleteBaseline(baseline)}
                      >
                        <TrashIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-end mt-4">
            <div className="flex items-center gap-2">
              <span>共 {totalRecords} 条记录</span>
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                上一页
              </Button>
              {Array.from({ length: totalPages }, (_, index) => (
                <Button
                  key={index}
                  onClick={() => handlePageChange(index)}
                  className={`px-3 py-1 border rounded ${currentPage === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                >
                  {index + 1}
                </Button>
              ))}
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages - 1}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看基线</p>
          </div>
        </div>
      )}

      {showDetailModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">集成基线详情</h3>
              <button
                onClick={() => {
                  setShowDetailModal(false);
                  setSelectedBaseline(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>

            {isLoading ? (
              <div className="p-6 text-center text-gray-500">
                加载中...
              </div>
            ) : selectedBaseline && (
              <>
                <div className="p-6">
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">
                        基线名称
                      </label>
                      <div className="text-base">{selectedBaseline.name}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">
                        描述
                      </label>
                      <div className="text-base">{selectedBaseline.description || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">
                        创建时间
                      </label>
                      <div className="text-base">{selectedBaseline.createdTime || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">
                        修改时间
                      </label>
                      <div className="text-base">{selectedBaseline.updatedTime || '-'}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-2">
                        附件
                      </label>
                      <div className="space-y-2">
                        {selectedBaseline.projectFiles && selectedBaseline.projectFiles.length > 0 ? (
                          selectedBaseline.projectFiles.map(file => (
                            <div
                              key={file.id}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-3">
                                <FileTextIcon className="w-5 h-5 text-gray-400" />
                                <div>
                                  <div className="font-medium">{file.name}</div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handlePreviewFile(file)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <EyeOpenIcon className="w-4 h-4 mr-1" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadFile(file)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7 10 12 15 17 10" />
                                    <line x1="12" y1="15" x2="12" y2="3" />
                                  </svg>
                                </Button>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="text-gray-500 text-center py-4">暂无附件</div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-6 border-t bg-gray-50 flex justify-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowDetailModal(false);
                      setSelectedBaseline(null);
                    }}
                  >
                    关闭
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* 创建基线弹窗 */}
      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">创建设计基线</h3>
              <button
                onClick={() => {
                  setShowNewModal(false);
                  setError('');
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="基线名称"
                    value={newBaseline.name}
                    onChange={(e) => {
                      setNewBaseline({ ...newBaseline, name: e.target.value });
                      if (formErrors.name) {
                        setFormErrors({ ...formErrors, name: '' });
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      formErrors.name ? 'border-red-500' : ''
                    }`}
                    placeholder="请输入基线名称"
                  />
                  {formErrors.name && (
                    <div className="flex items-center gap-1 mt-1">
                      <div className="bg-red-50 text-red-500 rounded-md px-2 py-1 text-xs flex items-center">
                        <span>{formErrors.name}</span>
                      </div>
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线描述
                  </label>
                  <textarea
                    value={newBaseline.description}
                    onChange={(e) => setNewBaseline({ ...newBaseline, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线描述..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    附件
                  </label>
                  <div className="rounded-lg p-2">
                    <div 
                      className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors"
                      onClick={() => document.getElementById('file-upload').click()}
                    >
                      <div className="flex flex-col items-center gap-1">
                        <PlusIcon className="w-6 h-6 text-gray-400" />
                        <span className="text-blue-600">点击上传文件</span>
                        <span className="text-gray-500 text-sm">或拖拽文件到这里</span>
                      </div>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        className="hidden"
                        onChange={handleFileChange}
                      />
                    </div>
                    <div className="space-y-2 mt-4">
                      {newBaseline.attachments.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4" />
                            <span className="text-sm">{file.name}</span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600"
                            onClick={() => handleRemoveFile(index)}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowNewModal(false)}>
                取消
              </Button>
              <Button 
                onClick={handleCreateBaseline} 
                disabled={isCreating}
              >
                {isCreating ? '创建中...' : '创建基线'}
              </Button>
            </div>
          </div>
        </div>
      )}



      {showEditModal && editingBaseline && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑设计基线</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingBaseline(null);
                  setEditingFiles([]);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="基线名称"
                    value={editingBaseline.name}
                    onChange={(e) => {
                      const errorMessage = e.target.parentNode.querySelector('.error-message');
                      if (errorMessage) {
                        errorMessage.remove();
                        e.target.classList.remove('border-red-500');
                      }
                      setEditingBaseline({ ...editingBaseline, name: e.target.value });
                    }}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线名称"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    版本描述
                  </label>
                  <textarea
                    value={editingBaseline.description}
                    onChange={(e) => setEditingBaseline({ 
                      ...editingBaseline, 
                      description: e.target.value 
                    })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入版本描述..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    附件
                  </label>
                  <div className=" rounded-lg p-4">
                    {editingBaseline.projectFiles && editingBaseline.projectFiles.length > 0 && (
                      <div className="mb-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">已上传文件</div>
                        <div className="space-y-2">
                          {editingBaseline.projectFiles.map((file) => (
                            <div
                              key={file.id}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-3">
                                <FileTextIcon className="w-5 h-5 text-gray-400" />
                                <div className="font-medium">{file.name}</div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handlePreviewFile(file)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <EyeOpenIcon className="w-4 h-4 mr-1" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadFile(file)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7 10 12 15 17 10" />
                                    <line x1="12" y1="15" x2="12" y2="3" />
                                  </svg>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-600"
                                  onClick={() => {
                                    setEditingBaseline({
                                      ...editingBaseline,
                                      projectFiles: editingBaseline.projectFiles.filter(f => f.id !== file.id)
                                    });
                                  }}
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div 
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
                      onClick={() => document.getElementById('edit-file-upload').click()}
                    >
                      <div className="flex flex-col items-center gap-2">
                        <PlusIcon className="w-8 h-8 text-gray-400" />
                        <span className="text-blue-600">点击上传文件</span>
                        <input
                          id="edit-file-upload"
                          type="file"
                          multiple
                          className="hidden"
                          onChange={handleEditFileChange}
                        />
                      </div>
                    </div>

                    {editingFiles.length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">待上传文件</div>
                        <div className="space-y-2">
                          {editingFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-3">
                                <FileTextIcon className="w-5 h-5 text-gray-400" />
                                <div className="font-medium">{file.name}</div>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600"
                                onClick={() => {
                                  const newFiles = [...editingFiles];
                                  newFiles.splice(index, 1);
                                  setEditingFiles(newFiles);
                                }}
                              >
                                <TrashIcon className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingBaseline(null);
                  setEditingFiles([]);
                }}
              >
                取消
              </Button>
              <Button 
                onClick={handleUpdateBaseline}
                disabled={isUpdating}
              >
                {isUpdating ? '保存中...' : '保存修改'}
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* 删除确认弹窗 */}
      {showDeleteConfirmModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-2">确定要删除吗？</h3>
              <p className="text-gray-500 text-sm">删除后将无法恢复此基线。</p>
            </div>
            <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteConfirmModal(false);
                  setDeletingItem(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
              >
                确定删除
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});