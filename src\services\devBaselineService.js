// 开发基线接口管理
import { userStore } from '../store/userStore';

import {fetchData} from './fetch'

// 获取项目列表
export const fetchProjectList = async (username, searchQuery = '') => {
  try {
    // 首先获取用户数据
    const userResponse = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/users?username=${username}`, {
      headers: {
        'Private-Token': userStore.getUserData()?.accessToken || '',
      }
    });
    const userData = await userResponse.json();
    const user = userData[0];

    if (user) {
      // 获取项目列表
      const url = searchQuery 
        ? `${fetchData["GITLAB_URL"]}/api/v4/projects?search=${encodeURIComponent(searchQuery)}`
        : `${fetchData["GITLAB_URL"]}/api/v4/users/${user.id}/projects`;

      const projectsResponse = await fetch(url, {
        headers: {
          'Private-Token': userStore.getUserData()?.accessToken || '',
        }
      });
      return await projectsResponse.json();
    }
    return [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
    throw error;
  }
};

// 获取员工列表
export const fetchEmployeeList = async () => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/employees/list`);
    return await response.json();
  } catch (error) {
    console.error('获取员工列表失败:', error);
    throw error;
  }
};

// 获取基线列表
export const fetchBaselineList = async (projectId, page = 0, size = 10, name = '', createdBy = '') => {
  try {
    const url = new URL(`${fetchData["BASE_URL"]}/api/develop-lines/page`);
    const params = { projectId, page, size };
    if (name) params.name = name;
    if (createdBy) params.createdBy = createdBy;
    
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('获取基线列表失败');
    }

    return await response.json();
  } catch (error) {
    console.error('获取基线列表失败:', error);
    throw error;
  }
};

// 获取基线详情
export const fetchBaselineDetail = async (id) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/develop-lines/one/${id}`, {
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('获取基线详情失败');
    }

    return await response.json();
  } catch (error) {
    console.error('获取基线详情失败:', error);
    throw error;
  }
};

// 创建基线
export const createBaseline = async (formData) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/develop-lines/create`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error('创建基线失败');
    }

    return await response.json();
  } catch (error) {
    console.error('创建基线失败:', error);
    throw error;
  }
};

// 更新基线
export const updateBaseline = async (id, formData) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/develop-lines/update/${id}`, {
      method: 'PUT',
      body: formData
    });

    if (!response.ok) {
      throw new Error('更新基线失败');
    }

    return await response.json();
  } catch (error) {
    console.error('更新基线失败:', error);
    throw error;
  }
};

// 删除基线
export const deleteBaseline = async (id) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/develop-lines/delete/${id}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error('删除基线失败');
    }

    return await response.json();
  } catch (error) {
    console.error('删除基线失败:', error);
    throw error;
  }
};

// 获取文件预览
export const getFilePreview = async (fileId) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/files/preview/${fileId}`, {
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('获取文件预览失败');
    }

    return await response.json();
  } catch (error) {
    console.error('获取文件预览失败:', error);
    throw error;
  }
};

// 搜索基线
export const searchBaselines = async (projectId, page = 0, size = 10, name = '', createdBy = '') => {
  try {
    const url = new URL(`${fetchData["BASE_URL"]}/api/develop-lines/page`);
    const params = { projectId, page, size };
    
    if (name) params.name = name;
    if (createdBy) params.createdBy = createdBy;
    
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('搜索失败');
    }

    return await response.json();
  } catch (error) {
    console.error('搜索失败:', error);
    throw error;
  }
};

// 文件下载
export const downloadFile = async (fileName, bucketName) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/file/download?fileName=${encodeURIComponent(fileName)}&bucketName=${bucketName}`);
    if (!response.ok) {
      throw new Error('下载文件失败');
    }
    return response.blob();
  } catch (error) {
    console.error('文件下载失败:', error);
    throw error;
  }
};

// 文件预览
export const previewFile = async (fileName, bucketName) => {
  try {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/file/preview?fileName=${encodeURIComponent(fileName)}&bucketName=${bucketName}`);
    if (!response.ok) {
      throw new Error('获取文件预览失败');
    }
    return response.text();
  } catch (error) {
    console.error('获取文件预览失败:', error);
    throw error;
  }
}; 