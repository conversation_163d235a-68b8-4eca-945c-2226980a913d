import axios from 'axios';

import { fetchData } from './fetch';

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (name) => {
    const params = name ? `?name=${encodeURIComponent(name)}` : '';
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list${params}`).then(res => res.json());
  },

  // 获取单个项目详情
  getProjectDetail: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/one/${projectId}`).then(res => res.json()),

  // 创建项目
  createProject: (projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 更新项目信息
  updateProject: (projectId, projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/update/${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 删除项目
  deleteProject: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/delete/${projectId}`, {
      method: 'GET'
    }).then(res => res.json()),
};

// 任务相关接口
export const taskApi = {
  // 获取项目下的所有任务
  getTasksByProject: (projectId, name) => {
    const params = name ? `?name=${encodeURIComponent(name)}` : '';
    return fetch(`${fetchData["STAFF_URL"]}/api/tasks/by-project/${projectId}${params}`).then(res => res.json());
  },

  // 获取单个任务详情
  getTaskDetail: (taskId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/tasks/one/${taskId}`).then(res => res.json()),

  // 创建任务
  createTask: (taskData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/tasks/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(taskData)
    }).then(res => res.json()),

  // 更新任务信息
  updateTask: (taskId, taskData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/tasks/update/${taskId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(taskData)
    }).then(res => res.json()),

  // 删除任务
  deleteTask: (taskId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/tasks/delete/${taskId}`, {
      method: 'GET'
    }).then(res => res.json()),
};

// 员工相关接口（项目中用到的）
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),

  // 搜索员工（混合搜索）
  searchEmployeesMixed: (keyword) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/search-mixed?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),
}; 