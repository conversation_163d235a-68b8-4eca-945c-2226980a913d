import { MinusOutlined } from '@ant-design/icons';

const Dialog = ({ isMinimized, onMinimize, onRestore, children, ...props }) => {
  return (
    <div className={`dialog-container ${isMinimized ? 'minimized' : ''}`}>
      <div className="dialog-header">
        <button 
          onClick={isMinimized ? onRestore : onMinimize}
          aria-label={isMinimized ? "还原窗口" : "最小化窗口"}
          title={isMinimized ? "还原窗口" : "最小化窗口"}
          className="minimize-button"
        >
          <MinusOutlined />
        </button>
      </div>
      {!isMinimized && (
        <div className="dialog-content">
          {children}
        </div>
      )}
      <style jsx>{`
        .dialog-container {
          transition: all 0.3s ease;
          border: 1px solid #ddd;
          border-radius: 4px;
          background: white;
        }
        
        .dialog-container.minimized {
          width: 200px;
          height: 40px;
          position: fixed;
          right: 20px;
          bottom: 20px;
          overflow: hidden;
        }
        
        .dialog-header {
          padding: 8px;
          border-bottom: 1px solid #ddd;
        }
        
        .minimize-button {
          border: none;
          background: none;
          cursor: pointer;
          padding: 4px 8px;
        }
        
        .dialog-container.minimized .dialog-header {
          cursor: pointer;
        }
      `}</style>
    </div>
  );
};

export default Dialog; 