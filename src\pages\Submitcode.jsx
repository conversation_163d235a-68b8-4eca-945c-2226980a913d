import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { 
  MagnifyingGlassIcon,
  GitHubLogoIcon,
  CommitIcon,
  FileTextIcon,
  ChevronRightIcon,
  ClockIcon,
  PersonIcon,
  CodeIcon,
  PlusIcon,
  Cross2Icon,
  ChatBubbleIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  QuestionMarkCircledIcon,
  TrashIcon,
  ArchiveIcon,
  EyeOpenIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import * as submitcodeService from '../services/submitcodeService';
import { userStore } from '../store/userStore';


const mockSubmissions = [
  {
    id: 'SUB-001',
    projectId: 1,
    title: '用户认证功能实现',
    description: '完成了用户登录、注册和认证功能的开发',
    files: [
      {
        name: 'auth.js',
        path: '/src/services/auth.js',
        size: '2.3 KB',
        status: 'modified'
      },
      {
        name: 'login.jsx',
        path: '/src/pages/login.jsx',
        size: '4.1 KB',
        status: 'added'
      }
    ],
    status: 'pending', // pending, reviewing, approved, rejected
    submitter: '张三',
    submitTime: '2024-02-20 14:30',
    reviewer: '李四',
    comments: []
  }
];

// 添加模拟的文件数据
const mockFiles = {
  'main': [
    { name: 'README.md', type: 'file', path: '/README.md', size: '2.1 KB' },
    { name: 'src', type: 'directory', path: '/src' },
    { name: 'package.json', type: 'file', path: '/package.json', size: '1.2 KB' }
  ],
  'develop': [
    { name: 'README.md', type: 'file', path: '/README.md', size: '2.3 KB' },
    { name: 'src', type: 'directory', path: '/src' },
    { name: 'docs', type: 'directory', path: '/docs' }
  ]
};

const defaultSshKey = {
  title: 'seadee-key',
  key: userStore.getUserData()?.sshKey || ''
};


export const Submitcode = observer(() => {
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [selectedMR, setSelectedMR] = useState(null);
  const [newMR, setNewMR] = useState({
    title: '',
    sourceBranch: 'main',
    targetBranch: '',
    description: '',
    reviewers: []
  });
  const [mergeRequests, setMergeRequests] = useState(mockSubmissions);
  const [newComment, setNewComment] = useState('');
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [projects, setProjects] = useState([]);
  const [branches, setBranches] = useState([]);
  const [error, setError] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [mrToDelete, setMrToDelete] = useState(null);
  const [submissions, setSubmissions] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [submitForm, setSubmitForm] = useState({
    title: '',
    description: '',
    files: []
  });
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [files, setFiles] = useState([]);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [reviewComment, setReviewComment] = useState('');
  const [currentPath, setCurrentPath] = useState('');
  const [showReviewDetailModal, setShowReviewDetailModal] = useState(false);
  const [reviewDetail, setReviewDetail] = useState(null);

  useEffect(() => {
    const fetchUserAndProjects = async () => {
      try {
        const userData = await submitcodeService.getUserBySSHKey(defaultSshKey.key);
        const user = userData;
        
        if (user) {
          const projectsData = await submitcodeService.getUserProjects(user.id);
          
          const formattedProjects = projectsData.map(project => ({
            id: project.id,
            name: project.name
          }));
          
          setProjects(formattedProjects);

          if (formattedProjects.length > 0) {
            const firstProject = formattedProjects[0];
            setSelectedProject(firstProject);
            // 先获取分支，然后使用默认分支
            const branches = await submitcodeService.getProjectBranches(firstProject.id);
            const defaultBranch = branches.find(b => b.default) || branches[0];
            setBranches(branches);
            setSelectedBranch(defaultBranch);
            
            // 使用默认分支获取文件和评审数据
            await fetchFiles(firstProject.id, defaultBranch.name);
            await fetchReviews(firstProject.id, defaultBranch.name);
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    };

    fetchUserAndProjects();
  }, []);

  const fetchBranches = async (projectId) => {
    try {
      const branchesData = await submitcodeService.getProjectBranches(projectId);
      console.log('获取到分支数据:', branchesData);
      setBranches(branchesData);
      
      if (branchesData.length > 0) {
        const defaultBranch = branchesData.find(b => b.default) || branchesData[0];
        setSelectedBranch(defaultBranch);
        // 在设置完分支后使用默认分支获取评审数据
        console.log('开始获取评审数据，使用分支:', defaultBranch.name);
        await fetchReviews(projectId, defaultBranch.name);
        await fetchFiles(projectId, defaultBranch.name);
      }
    } catch (error) {
      console.error('获取分支失败:', error);
      const mockBranches = [
        { name: 'main', default: true },
        { name: 'develop', default: false },
        { name: 'feature/user-auth', default: false }
      ];
      setBranches(mockBranches);
      setSelectedBranch(mockBranches[0]);
      setFiles(mockFiles['main'] || []);
    }
  };

  const fetchFiles = async (projectId, branch, path = '') => {
    try {
      const filesData = await submitcodeService.getProjectFiles(projectId, branch, path);
      setFiles(filesData);
      setCurrentPath(path);
    } catch (error) {
      console.error('获取文件列表失败:', error);
      setFiles(mockFiles[branch] || []);
    }
  };

  const fetchReviews = async (projectId, branchName) => {
    try {
      console.log('开始获取评审数据，参数:', { projectId, branchName });
      const reviewsData = await submitcodeService.getProjectReviews(projectId, branchName);
      console.log('API返回的原始数据:', reviewsData);
      
      if (!Array.isArray(reviewsData)) {
        console.log('评审数据不是数组，类型:', typeof reviewsData);
        console.log('原始数据:', reviewsData);
        setSubmissions([]);
        return;
      }
      
      // 格式化数据
      const formattedData = reviewsData.map(review => ({
        projectName: review.projectName || selectedProject?.name || '',
        branchName: review.branchName || selectedBranch?.name || '',
        fileName: review.fileName || review.file?.name || '',
        advice: review.advice || review.description || ''
      }));
      
      console.log('格式化后的评审数据:', formattedData);
      setSubmissions(formattedData);
      
    } catch (error) {
      console.error('获取评审数据失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack
      });
      showError('获取评审数据失败');
      setSubmissions([]);
    }
  };

  const handleProjectSelect = (project) => {
    console.log('选择项目:', project);
    setSelectedProject(project);
    // 获取分支信息，会自动获取文件和评审数据
    fetchBranches(project.id);
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles([...selectedFiles, ...files]);
    setSubmitForm({
      ...submitForm,
      files: [...submitForm.files, ...files]
    });
  };

  const handleRemoveFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    setSubmitForm({
      ...submitForm,
      files: newFiles
    });
  };

  const handleSubmitCode = () => {
    if (!submitForm.title || submitForm.files.length === 0) {
      alert('请填写标题并选择要提交的文件');
      return;
    }

    const newSubmission = {
      id: `SUB-${Date.now()}`,
      projectId: selectedProject.id,
      title: submitForm.title,
      description: submitForm.description,
      files: submitForm.files.map(file => ({
        name: file.name,
        path: `/src/${file.name}`,
        size: `${(file.size / 1024).toFixed(1)} KB`,
        status: 'added'
      })),
      status: 'pending',
      submitter: '当前用户',
      submitTime: new Date().toLocaleString(),
      reviewer: '',
      comments: []
    };

    setSubmissions([newSubmission, ...submissions]);
    setSubmitForm({
      title: '',
      description: '',
      files: []
    });
    setSelectedFiles([]);
    setShowNewModal(false);
  };

  const showError = (message, isSuccess = false) => {
    setError({
      message,
      type: isSuccess ? 'success' : 'error'
    });
    setTimeout(() => {
      setError(null);
    }, 3000);
  };


  const handleFileReview = async (file) => {
    try {
      const fileContent = await submitcodeService.getFileContent(selectedProject.id, file.path, 'main');
      const fileId = file.id || String(Date.now());

      let existingReview = null;
      try {
        existingReview = await submitcodeService.getFileReview(selectedProject.id, selectedBranch.name, fileName);
      } catch (error) {
      }

      setSelectedFile({
        ...file,
        id: fileId,
        content: fileContent,
        existingReview: existingReview?.description || '',
        reviewId: existingReview?.id
      });
      setReviewComment('');
      setShowReviewModal(true);
    } catch (error) {
      console.error('获取文件内容或评审信息失败:', error);
      showError('获取文件内容或评审信息失败');
    }
  };

  const handleSubmitReview = async () => {
    try {
      if (!selectedProject || !selectedFile || !reviewComment) {
        throw new Error('请填写完整的评审信息');
      }

      const reviewData = {
        projectId: selectedProject.id,
        projectName: selectedProject.name,
        branchName: selectedBranch.name,
        fileName: selectedFile.name,
        advice: reviewComment
      };

      await submitcodeService.createFileReview(reviewData);
      
      await fetchReviews(selectedProject.id, selectedBranch.name);

      setShowReviewModal(false);
      setReviewComment('');
      showError('评审提交成功', true);
    } catch (error) {
      console.error('提交评审失败:', error);
      showError(error.message);
    }
  };

  // 添加处理文件夹点击的函数
  const handleFileClick = (file) => {
    if (file.type === 'tree') {
      // 如果是文件夹，获取其内容
      fetchFiles(selectedProject.id, selectedBranch.name, file.path);
    } else {
      // 如果是文件，获取该文件的评审数据
      fetchFileReviews(selectedProject.id, selectedBranch.name, file.name);
    }
  };

  const fetchFileReviews = async (projectId, branchName, fileName) => {
    try {
      console.log('获取文件评审数据，参数:', { projectId, branchName, fileName });
      const reviewsData = await submitcodeService.getProjectReviews(projectId, branchName, fileName);
      console.log('API返回的原始数据:', reviewsData);
      
      if (!Array.isArray(reviewsData)) {
        console.log('评审数据不是数组，类型:', typeof reviewsData);
        console.log('原始数据:', reviewsData);
        setSubmissions([]);
        return;
      }
      
      // 格式化数据
      const formattedData = reviewsData.map(review => ({
        projectName: review.projectName || selectedProject?.name || '',
        branchName: review.branchName || selectedBranch?.name || '',
        fileName: review.fileName || fileName || '',
        advice: review.advice || review.description || ''
      }));
      
      console.log('格式化后的评审数据:', formattedData);
      setSubmissions(formattedData);
      
    } catch (error) {
      console.error('获取文件评审数据失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack
      });
      showError('获取评审数据失败');
      setSubmissions([]);
    }
  };

  const handleBackToParent = () => {
    if (!currentPath) return;
    
    const parentPath = currentPath.split('/').slice(0, -1).join('/');
    // 使用当前选中的分支
    fetchFiles(selectedProject.id, selectedBranch.name, parentPath);
  };

  const handleViewReview = async (submission) => {
    try {
      const response = await submitcodeService.getFileReview(
        selectedProject.id,
        submission.branchName,
        submission.fileName
      );
      
      setReviewDetail(response);
      setShowReviewDetailModal(true);
    } catch (error) {
      console.error('获取评审详情失败:', error);
      showError('获取评审详情失败');
    }
  };

  return (
    <>
      {/* Error Message */}
      {error && (
        <div className="fixed top-0 left-0 right-0 z-50 flex justify-center">
          <div className={`px-4 py-2 rounded-b-lg shadow-lg flex items-center gap-2 ${
            error.type === 'success' ? 'bg-green-50 text-green-600' : 'bg-red-50 text-red-600'
          }`}>
            {error.type === 'success' ? (
              <CheckCircledIcon className="w-4 h-4" />
            ) : (
              <Cross2Icon className="w-4 h-4" />
            )}
            <span>{error.message}</span>
          </div>
        </div>
      )}

      <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
        {/* Project List Sidebar */}
        <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          </div>
          <div className="flex-1 overflow-y-auto">
            {projects
              .filter(project => 
                project.name.toLowerCase().includes(projectSearchQuery.toLowerCase())
              )
              .map(project => (
                <div
                  key={project.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                  }`}
                  onClick={() => handleProjectSelect(project)}
                >
                  <div className="flex items-center gap-2">
                    <GitHubLogoIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="font-medium">{project.name}</div>
                      <div className="text-sm text-gray-500">代码仓库</div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Content Area */}
        {selectedProject ? (
          <div className="flex-1 flex flex-col h-full">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">代码评审</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-hidden flex">
              {/* 文件列表 */}
              <div className="w-1/2 overflow-hidden flex flex-col h-[1200px]">
                <div className="p-4 border-b flex justify-between items-center">
                  <h3 className="font-medium">文件列表</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      文件分支: {selectedBranch?.name}
                    </span>
                    {currentPath && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleBackToParent}
                        className="flex items-center gap-1"
                      >
                        <ChevronRightIcon className="w-4 h-4 rotate-180" />
                        返回上级
                      </Button>
                    )}
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto">
                  {files.map((file, index) => (
                    <div 
                      key={index} 
                      className="p-4 hover:bg-gray-50 flex items-center justify-between border-b cursor-pointer"
                      onClick={() => handleFileClick(file)}
                    >
                      <div className="flex items-center gap-2">
                        {file.type === 'tree' ? (
                          <ArchiveIcon className="w-4 h-4 text-gray-400" />
                        ) : (
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                        )}
                        <span>{file.name}</span>
                        {file.size && (
                          <span className="text-sm text-gray-500">
                            {(file.size / 1024).toFixed(1)} KB
                          </span>
                        )}
                      </div>
                      {file.type !== 'tree' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation(); // 防止触发父元素的点击事件
                            handleFileReview(file);
                          }}
                        >
                          评审
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              {/* 背景间隔 */}
              <div className="w-8 bg-gray-50"></div>
              {/* 评审列表 */}
              <div className="w-1/2 overflow-hidden flex flex-col h-[1200px]">
                <div className="p-4 border-b">
                  <h3 className="font-medium mb-4">评审列表</h3>
                  <div className="grid grid-cols-5 gap-4 text-sm font-medium text-gray-500">
                    <div>项目名称</div>
                    <div>分支</div>
                    <div>文件名称</div>
                    <div>评审建议</div>
                    <div>操作</div>
                  </div>
                </div>
                <div className="flex-1 overflow-y-auto">
                  {console.log('当前评审列表数据:', submissions)}
                  {submissions && submissions.length > 0 ? (
                    submissions.map((submission, index) => (
                      <div 
                        key={index} 
                        className="p-4 hover:bg-gray-50 border-b cursor-pointer"
                      >
                        <div className="grid grid-cols-5 gap-4 text-sm">
                          <div className="truncate">{submission.projectName}</div>
                          <div className="truncate">{submission.branchName}</div>
                          <div className="truncate">{submission.fileName}</div>
                          <div className="truncate max-w-[6em]" title={submission.advice || '暂无评审'}>
                            {submission.advice || '暂无评审'}
                          </div>
                          <div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewReview(submission);
                              }}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      暂无评审数据
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <GitHubLogoIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目提交代码</p>
            </div>
          </div>
        )}

        {/* New MR Modal */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">提交代码</h3>
                <button
                  onClick={() => setShowNewModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      标题 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={submitForm.title}
                      onChange={(e) => setSubmitForm({ ...submitForm, title: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入提交标题"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描述
                    </label>
                    <textarea
                      value={submitForm.description}
                      onChange={(e) => setSubmitForm({ ...submitForm, description: e.target.value })}
                      rows={4}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请描述此次提交的内容..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文件 <span className="text-red-500">*</span>
                    </label>
                    <div className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-sm font-medium">选择文件</span>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => document.getElementById('file-upload').click()}
                        >
                          <PlusIcon className="w-4 h-4 mr-1" />
                          添加文件
                        </Button>
                      </div>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        className="hidden"
                        onChange={handleFileSelect}
                      />
                      <div className="space-y-2">
                        {selectedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded"
                          >
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-4 h-4" />
                              <span className="text-sm">{file.name}</span>
                              <span className="text-xs text-gray-500">
                                {(file.size / 1024).toFixed(1)} KB
                              </span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600"
                              onClick={() => handleRemoveFile(index)}
                            >
                              <TrashIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNewModal(false)}
                >
                  取消
                </Button>
                <Button onClick={handleSubmitCode}>
                  提交
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Review Modal */}
        {showReviewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] flex flex-col">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">文件评审 - {selectedFile?.name}</h3>
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto p-6">
                <div className="bg-gray-50 p-4 rounded-lg mb-4 h-[300px] overflow-y-auto">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    {selectedFile?.content || '加载文件内容中...'}
                  </pre>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    评审意见
                  </label>
                  <textarea
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入评审意见..."
                  />
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowReviewModal(false)}
                >
                  取消
                </Button>
                <Button onClick={handleSubmitReview}>
                  提交评审
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* View Review Modal */}
        {showReviewDetailModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[80vh] flex flex-col">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">查看评审详情</h3>
                <button
                  onClick={() => setShowReviewDetailModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <div className="text-sm text-gray-900  p-3 rounded-lg">
                      {reviewDetail?.projectName || '-'}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      分支
                    </label>
                    <div className="text-sm text-gray-900  p-3 rounded-lg">
                      {reviewDetail?.branchName || '-'}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      文件名称
                    </label>
                    <div className="text-sm text-gray-900  p-3 rounded-lg">
                      {reviewDetail?.fileName || '-'}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审建议
                    </label>
                    <div className="text-sm text-gray-900 bg-gray-50 p-3 rounded-lg whitespace-pre-wrap">
                      {reviewDetail?.advice || '-'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setShowReviewDetailModal(false)}
                >
                  关闭
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
});