/**
 * 代码仓库管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和管理（创建、删除项目）
 * 2. 分支管理（切换分支、创建新分支）
 * 3. 文件浏览（查看文件列表、文件内容、目录导航）
 * 4. 文件操作（上传文件、创建文件夹）
 * 5. 代码克隆（SSH/HTTP URL复制）
 *
 * 简化说明：
 * - 去除了复杂的搜索功能
 * - 去除了SSH Key管理功能
 * - 简化了错误处理逻辑
 * - 优化了状态管理结构
 * - 添加了详细的中文注释
 */

import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  GitHubLogoIcon,
  FileTextIcon,
  ChevronRightIcon,
  DownloadIcon,
  PlusIcon,
  Cross2Icon,
  ExclamationTriangleIcon,
  BoxIcon,
  CopyIcon,
  CheckIcon,
  TrashIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { repositoryService } from '../services/repositoryService';

export const Repository = observer(() => {
  // 基础状态管理
  const [projects, setProjects] = useState([]); // 项目列表
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [userData, setUserData] = useState(null); // 用户数据

  // 分支相关状态
  const [branches, setBranches] = useState([]); // 分支列表
  const [selectedBranch, setSelectedBranch] = useState('main'); // 当前选中分支
  const [showNewBranchModal, setShowNewBranchModal] = useState(false); // 新建分支弹窗
  const [newBranch, setNewBranch] = useState({ name: '', source: 'main' }); // 新分支信息

  // 文件相关状态
  const [projectFiles, setProjectFiles] = useState(null); // 项目文件列表
  const [selectedFile, setSelectedFile] = useState(null); // 当前选中文件
  const [fileContent, setFileContent] = useState(null); // 文件内容
  const [currentPath, setCurrentPath] = useState(''); // 当前路径

  // 项目管理状态
  const [showNewProjectModal, setShowNewProjectModal] = useState(false); // 新建项目弹窗
  const [newProject, setNewProject] = useState({ name: '', description: '', visibility: 'private' }); // 新项目信息
  const [showDeleteModal, setShowDeleteModal] = useState(false); // 删除项目弹窗
  const [projectToDelete, setProjectToDelete] = useState(null); // 待删除项目
  const [deleteError, setDeleteError] = useState(null); // 删除错误信息

  // 文件上传状态
  const [showUploadModal, setShowUploadModal] = useState(false); // 上传文件弹窗
  const [uploadFiles, setUploadFiles] = useState([]); // 待上传文件列表
  const [uploadError, setUploadError] = useState(null); // 上传错误信息
  const [uploadCommitMessage, setUploadCommitMessage] = useState(''); // 上传提交信息

  // 文件夹创建状态
  const [showNewFolderModal, setShowNewFolderModal] = useState(false); // 新建文件夹弹窗
  const [newFolder, setNewFolder] = useState({ name: '', commitMessage: '' }); // 新文件夹信息

  // 克隆相关状态
  const [showCloneDropdown, setShowCloneDropdown] = useState(false); // 克隆下拉菜单
  const [cloneUrls, setCloneUrls] = useState({ ssh: '', http: '' }); // 克隆URL
  const [copiedType, setCopiedType] = useState(null); // 复制状态



  // 克隆下拉菜单引用
  const cloneDropdownRef = React.useRef(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (cloneDropdownRef.current && !cloneDropdownRef.current.contains(event.target)) {
        setShowCloneDropdown(false);
      }
    };

    if (showCloneDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showCloneDropdown]);

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 获取用户数据
        const user = await repositoryService.getUserInfo();
        setUserData(user);

        if (user) {
          // 获取项目列表
          const projectsData = await repositoryService.getUserProjects(user.id);
          const formattedProjects = projectsData.map(project => ({
            id: project.id,
            name: project.name
          }));

          setProjects(formattedProjects);

          // 自动选择第一个项目
          if (formattedProjects.length > 0) {
            handleSelectProject(formattedProjects[0]);
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    };

    initializeData();
  }, []);

  // 选择项目处理函数
  const handleSelectProject = async (project) => {
    setCurrentPath(''); // 重置当前路径
    setSelectedProject(project);

    try {
      // 并行获取项目相关数据
      const [projectDetails, branchesData, filesData] = await Promise.all([
        repositoryService.getProjectDetails(project.id),
        repositoryService.getProjectBranches(project.id),
        repositoryService.getProjectFiles(project.id)
      ]);

      // 设置分支数据
      setBranches(branchesData);
      setSelectedBranch('main');

      // 更新项目详细信息
      setSelectedProject({
        ...project,
        name: projectDetails.name,
        path: projectDetails.path,
        defaultBranch: projectDetails.default_branch,
        forksCount: projectDetails.forks_count,
        createdAt: new Date(projectDetails.created_at).toLocaleString('zh-CN'),
        lastActivityAt: new Date(projectDetails.last_activity_at).toLocaleString('zh-CN')
      });

      // 设置文件列表
      setProjectFiles(filesData);

      // 获取克隆URL（非关键数据，单独处理）
      try {
        const urls = await repositoryService.getCloneUrls(project.id);
        setCloneUrls({
          ssh: urls.ssh_url_to_repo,
          http: urls.http_url_to_repo
        });
      } catch (error) {
        console.error('获取克隆URL失败:', error);
      }

    } catch (error) {
      console.error('获取项目详情失败:', error);
      setProjectFiles(null);
    }
  };

  // 创建新分支处理函数
  const handleCreateBranch = async () => {
    if (!newBranch.name.trim()) {
      alert('请输入分支名称');
      return;
    }

    try {
      await repositoryService.createBranch(selectedProject.id, newBranch.name, newBranch.source);

      // 重新获取分支列表
      const branchesData = await repositoryService.getProjectBranches(selectedProject.id);
      setBranches(branchesData);

      // 关闭弹窗并重置表单
      setShowNewBranchModal(false);
      setNewBranch({ name: '', source: 'main' });
    } catch (error) {
      console.error('创建分支失败:', error);
      alert('创建分支失败，请重试');
    }
  };



  // 分支切换处理函数
  const handleBranchChange = async (e) => {
    const newBranch = e.target.value;
    setSelectedBranch(newBranch);

    try {
      // 获取新分支下的文件列表
      const filesData = await repositoryService.getProjectFiles(selectedProject.id, newBranch, currentPath);
      setProjectFiles(filesData);

      // 清除当前选中的文件和文件内容
      setSelectedFile(null);
      setFileContent(null);
    } catch (error) {
      console.error('获取分支文件列表失败:', error);
    }
  };

  // 文件/文件夹点击处理函数
  const handleFileClick = async (file) => {
    try {
      if (file.type === 'tree') {
        // 点击文件夹，进入文件夹
        setCurrentPath(file.path);
        const folderContents = await repositoryService.getProjectFiles(selectedProject.id, selectedBranch, file.path);
        setProjectFiles(folderContents);
        setSelectedFile(null);
        setFileContent(null);
      } else {
        // 点击文件，显示文件内容
        const content = await repositoryService.getFileContent(selectedProject.id, file.path, selectedBranch);
        setSelectedFile(file);
        setFileContent(content);
      }
    } catch (error) {
      console.error('获取内容失败:', error);
    }
  };

  // 返回上级目录处理函数
  const handleBack = async () => {
    try {
      const parentPath = currentPath.split('/').slice(0, -1).join('/');
      setCurrentPath(parentPath);

      const contents = await repositoryService.getProjectFiles(selectedProject.id, selectedBranch, parentPath);
      setProjectFiles(contents);
      setSelectedFile(null);
      setFileContent(null);
    } catch (error) {
      console.error('获取上级目录失败:', error);
    }
  };

  // 创建新项目处理函数
  const handleCreateProject = async () => {
    if (!newProject.name.trim()) {
      alert('请输入项目名称');
      return;
    }

    try {
      const newProjectData = await repositoryService.createProject({
        name: newProject.name,
        description: newProject.description,
        visibility: newProject.visibility
      });

      // 重新获取项目列表
      if (userData) {
        const projectsData = await repositoryService.getUserProjects(userData.id);
        const formattedProjects = projectsData.map(project => ({
          id: project.id,
          name: project.name
        }));

        setProjects(formattedProjects);

        // 自动选择新创建的项目
        const createdProject = formattedProjects.find(p => p.id === newProjectData.id);
        if (createdProject) {
          handleSelectProject(createdProject);
        }
      }

      // 关闭弹窗并重置表单
      setShowNewProjectModal(false);
      setNewProject({ name: '', description: '', visibility: 'private' });
    } catch (error) {
      console.error('创建项目失败:', error);
      alert('创建项目失败，请重试');
    }
  };

  // 文件选择处理函数
  const handleFileSelect = (files) => {
    const newFiles = Array.from(files).map(file => ({
      id: Date.now() + Math.random(),
      file: file
    }));
    setUploadFiles([...uploadFiles, ...newFiles]);
  };

  // 删除选中文件处理函数
  const handleFileDelete = (fileId) => {
    setUploadFiles(uploadFiles.filter(f => f.id !== fileId));
  };

  // 处理文件上传
  const handleFileUpload = async () => {
    if (uploadFiles.length === 0 || !uploadCommitMessage.trim()) {
      alert('请选择文件并输入提交信息');
      return;
    }

    try {
      for (const fileObj of uploadFiles) {
        const reader = new FileReader();
        await new Promise((resolve, reject) => {
          reader.onload = async () => {
            try {
              const content = reader.result.split(',')[1];
              const filePath = (currentPath ? currentPath + '/' : '') + fileObj.file.name;

              await repositoryService.uploadFile(
                selectedProject.id,
                filePath,
                content,
                selectedBranch,
                uploadCommitMessage
              );
              resolve();
            } catch (error) {
              reject(error);
            }
          };
          reader.onerror = reject;
          reader.readAsDataURL(fileObj.file);
        });
      }

      // 刷新文件列表
      const filesData = await repositoryService.getProjectFiles(selectedProject.id, selectedBranch, currentPath);
      setProjectFiles(filesData);

      // 关闭弹窗并重置表单
      setShowUploadModal(false);
      setUploadCommitMessage('');
      setUploadFiles([]);
    } catch (error) {
      console.error('文件上传失败:', error);
      setUploadError(error.message === 'A file with this name already exists' ? '文件已存在' : error.message || '上传失败，请重试');
    }
  };

  // 创建文件夹处理函数
  const handleCreateFolder = async () => {
    if (!newFolder.name.trim() || !newFolder.commitMessage.trim()) {
      alert('请填写完整信息');
      return;
    }

    try {
      const path = currentPath
        ? `${currentPath}/${newFolder.name}/.gitkeep`
        : `${newFolder.name}/.gitkeep`;

      await repositoryService.createFolder(
        selectedProject.id,
        path,
        selectedBranch,
        newFolder.commitMessage
      );

      // 刷新文件列表
      const filesData = await repositoryService.getProjectFiles(selectedProject.id, selectedBranch, currentPath);
      setProjectFiles(filesData);

      // 关闭弹窗并重置表单
      setShowNewFolderModal(false);
      setNewFolder({ name: '', commitMessage: '' });
    } catch (error) {
      console.error('创建目录失败:', error);
      alert('创建目录失败，请重试');
    }
  };

  // 复制克隆URL处理函数
  const handleCopyCloneUrl = (type) => {
    const url = type === 'ssh' ? cloneUrls.ssh : cloneUrls.http;
    navigator.clipboard.writeText(url).then(() => {
      setCopiedType(type);
      setTimeout(() => setCopiedType(null), 2000);
    });
  };



  // 删除项目处理函数
  const handleDeleteProject = (project, e) => {
    e.stopPropagation(); // 阻止事件冒泡
    setProjectToDelete(project);
    setShowDeleteModal(true);
  };

  // 确认删除项目处理函数
  const confirmDeleteProject = async () => {
    if (!projectToDelete) return;

    try {
      await repositoryService.deleteProject(projectToDelete.id);

      // 从项目列表中移除已删除的项目
      setProjects(projects.filter(p => p.id !== projectToDelete.id));

      // 如果删除的是当前选中的项目，清除选中状态
      if (selectedProject?.id === projectToDelete.id) {
        setSelectedProject(null);
        setProjectFiles(null);
        setSelectedFile(null);
        setFileContent(null);
      }

      // 关闭弹窗并重置状态
      setShowDeleteModal(false);
      setProjectToDelete(null);
      setDeleteError(null);
    } catch (error) {
      console.error('删除项目失败:', error);
      setDeleteError(error.message || '删除项目失败，请重试');
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex">

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-[1100px]">
        {/* 项目列表头部 */}
        <div className="p-4 border-b flex justify-between items-center">
          <h2 className="text-lg font-semibold">项目列表</h2>
          <div className="flex items-center gap-2">
            {/* 新建项目按钮 */}
            <Button
              variant="ghost"
              size="icon"
              className="w-8 h-8"
              onClick={() => setShowNewProjectModal(true)}
            >
              <PlusIcon className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* 项目列表内容 */}
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${
                selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
              }`}
              onClick={() => handleSelectProject(project)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GitHubLogoIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="font-medium">{project.name}</div>
                    <div className="text-sm text-gray-500">代码仓库</div>
                  </div>
                </div>
                {/* 删除项目按钮 */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 text-red-500 hover:bg-red-50 hover:text-red-600"
                  onClick={(e) => handleDeleteProject(project, e)}
                >
                  <TrashIcon className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧项目详情区域 */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col">
          {/* 项目基本信息 */}
          <div className="bg-white p-6 rounded-lg shadow-sm mb-4">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">{selectedProject.name}</h1>
            </div>

            {/* 项目信息网格 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-gray-500 mb-1">项目路径</div>
                <div>{selectedProject.path}</div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">当前分支</div>
                <div className="flex items-center gap-2">
                  {/* 分支选择下拉框 */}
                  <select
                    value={selectedBranch}
                    onChange={handleBranchChange}
                    className="w-48 px-3 py-1 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {branches.map(branch => (
                      <option key={branch.name} value={branch.name}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                  {/* 新建分支按钮 */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowNewBranchModal(true)}
                    className="flex items-center gap-1 bg-blue-600 text-white hover:bg-blue-700"
                  >
                    <PlusIcon className="w-4 h-4" />
                    新建分支
                  </Button>
                </div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">创建时间</div>
                <div>{selectedProject.createdAt}</div>
              </div>
              <div>
                <div className="text-gray-500 mb-1">上次操作时间</div>
                <div>{selectedProject.lastActivityAt}</div>
              </div>
            </div>
          </div>

          {/* 文件列表区域 */}
          {projectFiles && (
            <div className="bg-white p-6 rounded-lg shadow-sm mb-4">
              {/* 文件操作工具栏 */}
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-4">
                  <h2 className="text-lg font-semibold">文件列表</h2>
                  <div className="flex gap-2">
                    {/* 上传文件按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowUploadModal(true)}
                      className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
                    >
                      <PlusIcon className="w-4 h-4" />
                      上传文件
                    </Button>
                    {/* 新建目录按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowNewFolderModal(true)}
                      className="flex items-center gap-2"
                    >
                      <BoxIcon className="w-4 h-4" />
                      新建目录
                    </Button>
                    {/* 克隆下拉菜单 */}
                    <div className="relative" ref={cloneDropdownRef}>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowCloneDropdown(!showCloneDropdown)}
                        className="flex items-center gap-2"
                      >
                        <DownloadIcon className="w-4 h-4" />
                        克隆
                      </Button>
                      {showCloneDropdown && (
                        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border p-4 z-50">
                          <div className="space-y-4">
                            {/* SSH克隆URL */}
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-2">SSH克隆URL</div>
                              <div className="flex items-center gap-2">
                                <input
                                  type="text"
                                  value={cloneUrls.ssh}
                                  readOnly
                                  className="flex-1 px-3 py-2 bg-gray-50 border rounded-lg text-sm"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCopyCloneUrl('ssh')}
                                  className="flex items-center gap-1"
                                >
                                  {copiedType === 'ssh' ? <CheckIcon className="w-4 h-4 text-green-500" /> : <CopyIcon className="w-4 h-4" />}
                                  {copiedType === 'ssh' ? '已复制' : '复制'}
                                </Button>
                              </div>
                            </div>
                            {/* HTTP克隆URL */}
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-2">HTTP克隆URL</div>
                              <div className="flex items-center gap-2">
                                <input
                                  type="text"
                                  value={cloneUrls.http}
                                  readOnly
                                  className="flex-1 px-3 py-2 bg-gray-50 border rounded-lg text-sm"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleCopyCloneUrl('http')}
                                  className="flex items-center gap-1"
                                >
                                  {copiedType === 'http' ? <CheckIcon className="w-4 h-4 text-green-500" /> : <CopyIcon className="w-4 h-4" />}
                                  {copiedType === 'http' ? '已复制' : '复制'}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* 返回上级按钮 */}
                {currentPath && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBack}
                    className="flex items-center gap-2"
                  >
                    <ChevronRightIcon className="w-4 h-4 rotate-180" />
                    返回上级
                  </Button>
                )}
              </div>

              {/* 当前路径显示 */}
              <div className="text-sm text-gray-500 mb-4">
                当前路径: {currentPath || '/'}
              </div>

              {/* 文件列表 */}
              <div className="divide-y flex-1 overflow-y-auto">
                {projectFiles.map((file) => (
                  <div
                    key={file.id}
                    className="py-2 flex items-center justify-between hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleFileClick(file)}
                  >
                    <div className="flex items-center gap-2">
                      {file.type === 'tree' ? (
                        <BoxIcon className="w-4 h-4 text-gray-400" />
                      ) : (
                        <FileTextIcon className="w-4 h-4 text-gray-400" />
                      )}
                      <span className="text-sm">{file.name}</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {file.type === 'tree' ? '文件夹' : '文件'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 文件内容显示区域 */}
          {selectedFile && fileContent && (
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">{selectedFile.name}</h2>
              </div>
              {/* 文件内容预览 */}
              <div className="bg-gray-50 p-4 rounded-lg h-[340px] overflow-y-auto">
                <pre className="text-sm font-mono whitespace-pre-wrap">{fileContent}</pre>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* 空状态 - 未选择项目时显示 */
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <GitHubLogoIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看详情</p>
          </div>
        </div>
      )}

      {/* 新建分支弹窗 */}
      {showNewBranchModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建分支</h3>
              <button
                onClick={() => setShowNewBranchModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            {/* 弹窗内容 */}
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    分支名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newBranch.name}
                    onChange={(e) => setNewBranch({ ...newBranch, name: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入分支名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">源分支</label>
                  <select
                    value={newBranch.source}
                    onChange={(e) => setNewBranch({ ...newBranch, source: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {branches.map(branch => (
                      <option key={branch.name} value={branch.name}>
                        {branch.name} {branch.isDefault && '(默认)'}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            {/* 弹窗底部按钮 */}
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowNewBranchModal(false)}>取消</Button>
              <Button onClick={handleCreateBranch}>创建分支</Button>
            </div>
          </div>
        </div>
      )}

      {/* 新建项目弹窗 */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建项目</h3>
              <button
                onClick={() => setShowNewProjectModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            {/* 弹窗内容 */}
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newProject.name}
                    onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入项目名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
                  <textarea
                    value={newProject.description}
                    onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入项目描述"
                    rows={3}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">可见性</label>
                  <select
                    value={newProject.visibility}
                    onChange={(e) => setNewProject({ ...newProject, visibility: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="private">私有</option>
                    <option value="internal">内部</option>
                    <option value="public">公开</option>
                  </select>
                </div>
              </div>
            </div>
            {/* 弹窗底部按钮 */}
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowNewProjectModal(false)}>取消</Button>
              <Button onClick={handleCreateProject}>创建项目</Button>
            </div>
          </div>
        </div>
      )}

      {/* 文件上传弹窗 */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          {/* 错误提示 */}
          {uploadError && (
            <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3 shadow-lg z-50 w-[500px]">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-red-800">上传失败</h4>
                <p className="text-sm text-red-700 mt-1">{uploadError}</p>
              </div>
              <button onClick={() => setUploadError(null)} className="p-1 hover:bg-red-100 rounded-full flex-shrink-0">
                <Cross2Icon className="w-4 h-4 text-red-500" />
              </button>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">上传文件</h3>
              <button
                onClick={() => {
                  setShowUploadModal(false);
                  setUploadError(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            {/* 弹窗内容 */}
            <div className="p-6">
              <div className="space-y-4">
                {/* 文件选择区域 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择文件 <span className="text-red-500">*</span>
                  </label>
                  <div
                    className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors border-gray-300 hover:border-blue-500"
                    onClick={() => document.getElementById('file-upload').click()}
                  >
                    <input
                      id="file-upload"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => handleFileSelect(e.target.files)}
                    />
                    <div className="flex flex-col items-center gap-2">
                      <PlusIcon className="w-8 h-8 text-gray-400" />
                      <div className="text-sm text-gray-600">
                        <span>点击选择文件上传</span>
                      </div>
                    </div>
                  </div>

                  {/* 已选择文件列表 */}
                  {uploadFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {uploadFiles.map((fileObj) => (
                        <div key={fileObj.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-600">{fileObj.file.name}</span>
                          </div>
                          <button
                            onClick={() => handleFileDelete(fileObj.id)}
                            className="p-1 hover:bg-gray-200 rounded-full"
                          >
                            <Cross2Icon className="w-4 h-4 text-gray-500" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 提交信息 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提交信息 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={uploadCommitMessage}
                    onChange={(e) => setUploadCommitMessage(e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入提交信息"
                  />
                </div>

                {/* 当前分支显示 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">当前分支</label>
                  <input
                    type="text"
                    value={selectedBranch}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
              </div>
            </div>
            {/* 弹窗底部按钮 */}
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowUploadModal(false);
                  setUploadError(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleFileUpload}>上传</Button>
            </div>
          </div>
        </div>
      )}

      {/* 新建文件夹弹窗 */}
      {showNewFolderModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建目录</h3>
              <button
                onClick={() => setShowNewFolderModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            {/* 弹窗内容 */}
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    目录名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newFolder.name}
                    onChange={(e) => setNewFolder({ ...newFolder, name: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入目录名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提交信息 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newFolder.commitMessage}
                    onChange={(e) => setNewFolder({ ...newFolder, commitMessage: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入提交信息"
                  />
                </div>
              </div>
            </div>
            {/* 弹窗底部按钮 */}
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowNewFolderModal(false)}>取消</Button>
              <Button onClick={handleCreateFolder}>创建</Button>
            </div>
          </div>
        </div>
      )}



      {/* Delete Project Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">删除项目</h3>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
                  <ExclamationTriangleIcon className="w-6 h-6 text-red-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-800">警告：此操作不可逆</h4>
                    <p className="text-sm text-red-700 mt-1">
                      删除项目将永久移除所有代码、提交历史、议题和相关数据。此操作无法撤销。
                    </p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={projectToDelete?.name}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                
                {deleteError && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-700">{deleteError}</p>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setProjectToDelete(null);
                  setDeleteError(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDeleteProject}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                删除项目
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});