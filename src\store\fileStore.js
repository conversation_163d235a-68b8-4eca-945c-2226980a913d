import { makeAutoObservable } from 'mobx';

class FileStore {
  isOpen = false;
  newSharedFiles = 2;
  currentTab = 'my-files';
  currentFolder = null;
  searchQuery = '';

  files = {
    'my-files': [
      {
        id: '1',
        type: 'folder',
        name: '项目文档',
        size: null,
        modifiedTime: '2024-01-20 14:30',
        items: [
          {
            id: '1-1',
            type: 'file',
            name: '项目计划书.docx',
            size: '2.5MB',
            modifiedTime: '2024-01-20 14:30'
          }
        ]
      },
      {
        id: '2',
        type: 'file',
        name: '会议纪要.docx',
        size: '1.2MB',
        modifiedTime: '2024-01-19 10:15'
      },
      {
        id: '3',
        type: 'file',
        name: '产品设计稿.psd',
        size: '25MB',
        modifiedTime: '2024-01-18 16:45'
      }
    ],
    'shared-by-me': [
      {
        id: '4',
        type: 'file',
        name: '团队工作安排.xlsx',
        size: '0.8MB',
        modifiedTime: '2024-01-17 09:20',
        sharedWith: ['张三', '李四']
      }
    ],
    'shared-with-me': [
      {
        id: '5',
        type: 'file',
        name: '市场调研报告.pdf',
        size: '3.5MB',
        modifiedTime: '2024-01-16 11:30',
        sharedBy: '王经理',
        isNew: true
      },
      {
        id: '6',
        type: 'file',
        name: '产品规划.xlsx',
        size: '1.8MB',
        modifiedTime: '2024-01-15 15:20',
        sharedBy: '李总',
        isNew: true
      }
    ],
    'transfers': []
  };

  constructor() {
    makeAutoObservable(this);
  }

  setCurrentTab(tab) {
    this.currentTab = tab;
    this.currentFolder = null;
  }

  setCurrentFolder(folder) {
    this.currentFolder = folder;
  }

  toggleModal() {
    this.isOpen = !this.isOpen;
  }

  setSearchQuery(query) {
    this.searchQuery = query;
  }

  getCurrentFiles() {
    let files = this.currentFolder 
      ? this.currentFolder.items 
      : this.files[this.currentTab];

    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      files = files.filter(file => 
        file.name.toLowerCase().includes(query)
      );
    }

    return files;
  }

  createFolder(name) {
    const newFolder = {
      id: Date.now().toString(),
      type: 'folder',
      name,
      size: null,
      modifiedTime: new Date().toLocaleString(),
      items: []
    };

    if (this.currentFolder) {
      this.currentFolder.items.push(newFolder);
    } else {
      this.files['my-files'].push(newFolder);
    }
  }

  deleteFile(fileId) {
    const files = this.currentFolder ? this.currentFolder.items : this.files[this.currentTab];
    const index = files.findIndex(f => f.id === fileId);
    if (index !== -1) {
      files.splice(index, 1);
    }
  }

  markAllFilesRead() {
    this.newSharedFiles = 0;
    this.files['shared-with-me'].forEach(file => {
      file.isNew = false;
    });
  }
}

export const fileStore = new FileStore();