import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { projectClosure<PERSON><PERSON> } from '../services/projectClosureService';
import {
    MagnifyingGlassIcon,
    PlusIcon,
    Cross2Icon,
    ChevronRightIcon,
    PersonIcon,
    CalendarIcon,
    ClockIcon,
    FileTextIcon,
    LayersIcon,
    Pencil1Icon,
    TrashIcon,
    EyeOpenIcon,
    DownloadIcon,
    ChatBubbleIcon,
    CheckCircledIcon,
    CrossCircledIcon,
    QuestionMarkCircledIcon,
    GitHubLogoIcon,
    ChevronLeftIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';

// 添加日期时间格式化函数
const formatDateTime = (dateStr) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '';
    return date.toISOString().slice(0, 19).replace('T', ' ');
};

const categoryOptions = [
    { value: 'user', label: '用户需求' },
    { value: 'project', label: '项目需求' },
    { value: 'functional', label: '功能需求' },
    { value: 'quality', label: '质量需求' }
];

const groupOptions = [
    { value: 'core', label: '核心功能' },
    { value: 'business', label: '业务功能' },
    { value: 'system', label: '系统功能' },
    { value: 'platform', label: '平台功能' }
];

const priorityOptions = [
    { value: 'low', label: '低' },
    { value: 'medium', label: '中' },
    { value: 'high', label: '高' }
];

const mockRequirements = [
    {
        id: 'REQ-001',
        projectId: 1,
        name: '用户登录功能',
        category: 'functional',
        group: 'core',
        priority: 'high',
        description: '实现用户登录和认证功能',
        input: '用户名、密码',
        process: '验证用户身份并生成登录令牌',
        output: '登录状态和用户信息'
    },
    {
        id: 'REQ-002',
        projectId: 2,
        name: 'APP性能优化',
        category: 'performance',
        group: 'system',
        priority: 'medium',
        description: '优化APP启动速度和响应时间',
        input: '性能指标',
        process: '分析性能瓶颈并优化',
        output: '优化后的性能报告'
    },
    {
        id: 'REQ-003',
        projectId: 3,
        name: '数据同步功能',
        category: 'functional',
        group: 'core',
        priority: 'high',
        description: '实现数据实时同步',
        input: '数据源信息',
        process: '数据抽取转换加载',
        output: '同步状态报告'
    }
];

const planStatusOptions = [
    { value: 0, label: '未完成' },
    { value: 1, label: '已完成' }
];

const ErrorMessage = ({ message, onClose }) => {
    return (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
            <div className="text-red-500">
                <Cross2Icon className="w-4 h-4" />
            </div>
            <div className="text-sm text-red-800">{message}</div>
        </div>
    );
};

// 在 ErrorMessage 组件定义后添加 SuccessMessage 组件
const SuccessMessage = ({ message }) => {
    return (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-green-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
            <div className="text-green-500">
                <CheckCircledIcon className="w-4 h-4" />
            </div>
            <div className="text-sm text-green-800">{message}</div>
        </div>
    );
};

// 修改自定义时间选择器组件的样式
const DateTimeInput = ({ value, onChange, error, className }) => {
    return (
        <div className="relative">
            <input
                type="datetime-local"
                value={value}
                onChange={onChange}
                className={`${className} ${!value ? 'text-transparent' : ''}`}
                step="1"
            />
            {!value && (
                <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                    请选择开始时间
                </div>
            )}
        </div>
    );
};

// 添加批准人标签组件
const ApproverTag = ({ approver, onRemove }) => (
    <div className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1">
        <span className="text-sm">{approver.name}</span>
        <button
            type="button"
            onClick={(e) => {
                e.stopPropagation();
                onRemove(approver.id);
            }}
            className="text-gray-400 hover:text-red-500"
        >
            <Cross2Icon className="w-4 h-4" />
        </button>
    </div>
);

// 将 EditModal 移到组件外部
const EditModal = React.memo(({
    editForm,
    setEditForm,
    showEditModal,
    setShowEditModal,
    employees,
    selectedApprovers,
    setSelectedApprovers,
    editShowApproverSelect,
    setEditShowApproverSelect,
    handleUpdateClosure,
    handleEditRemoveApprover,
    handleEditFileUpload,
    handleEditFileDelete,
    handlePreviewFile
}) => {
    // 添加负责人选择框的状态
    const [showManagerSelect, setShowManagerSelect] = useState(false);
    // 添加结项发起人选择框的状态
    const [showApplicantSelect, setShowApplicantSelect] = useState(false);
    // 添加结项状态选择框的状态
    const [showStatusSelect, setShowStatusSelect] = useState(false);
    // 添加负责人选择框的引用
    const managerSelectRef = useRef(null);
    // 添加结项发起人选择框的引用
    const applicantSelectRef = useRef(null);
    // 添加结项状态选择框的引用
    const statusSelectRef = useRef(null);

    // 添加点击外部关闭下拉框的效果
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (managerSelectRef.current && !managerSelectRef.current.contains(event.target)) {
                setShowManagerSelect(false);
            }
            if (applicantSelectRef.current && !applicantSelectRef.current.contains(event.target)) {
                setShowApplicantSelect(false);
            }
            if (statusSelectRef.current && !statusSelectRef.current.contains(event.target)) {
                setShowStatusSelect(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-[800px] max-h-[90vh] flex flex-col">
                <div className="p-4 border-b flex items-center justify-between">
                    <h1 className="text-lg font-medium">修改项目结项</h1>
                    <button
                        onClick={() => setShowEditModal(false)}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <Cross2Icon className="w-4 h-4" />
                    </button>
                </div>

                <div className="p-6 overflow-y-auto">
                    <div className="space-y-4">
                        {/* 项目名称和结项名称在同一行 */}
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    项目名称
                                </label>
                                <input
                                    type="text"
                                    value={editForm.projectName}
                                    disabled
                                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    结项名称
                                </label>
                                <input
                                    type="text"
                                    value={editForm.name}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        setEditForm(prev => ({
                                            ...prev,
                                            name: value
                                        }));
                                    }}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                />
                            </div>
                        </div>

                        {/* 负责人和结项发起人在同一行 */}
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    负责人
                                </label>
                                <div className="relative" ref={managerSelectRef}>
                                    <div
                                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                                        onClick={() => setShowManagerSelect(!showManagerSelect)}
                                    >
                                        <span className={editForm.manager ? "text-gray-700" : "text-gray-400"}>
                                            {editForm.manager || "请选择负责人"}
                                        </span>
                                        <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                    </div>
                                    {showManagerSelect && (
                                        <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                                            <div className="flex flex-col p-1">
                                                {employees.map((employee) => (
                                                    <div
                                                        key={employee.id}
                                                        className="flex items-center gap-2 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded"
                                                        onClick={() => {
                                                            setEditForm(prev => ({
                                                                ...prev,
                                                                manager: employee.name
                                                            }));
                                                            setShowManagerSelect(false);
                                                        }}
                                                    >
                                                        <span className="text-sm text-gray-700">{employee.name}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    结项发起人
                                </label>
                                <div className="relative" ref={applicantSelectRef}>
                                    <div
                                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                                        onClick={() => setShowApplicantSelect(!showApplicantSelect)}
                                    >
                                        <span className={editForm.applicant ? "text-gray-700" : "text-gray-400"}>
                                            {editForm.applicant || "请选择发起人"}
                                        </span>
                                        <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                    </div>
                                    {showApplicantSelect && (
                                        <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                                            <div className="flex flex-col p-1">
                                                {employees.map((employee) => (
                                                    <div
                                                        key={employee.id}
                                                        className="flex items-center gap-2 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded"
                                                        onClick={() => {
                                                            setEditForm(prev => ({
                                                                ...prev,
                                                                applicant: employee.name
                                                            }));
                                                            setShowApplicantSelect(false);
                                                        }}
                                                    >
                                                        <span className="text-sm text-gray-700">{employee.name}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    结项状态
                                </label>
                                <select
                                    value={editForm.status}
                                    onChange={(e) => {
                                        const newStatus = e.target.value;
                                        setEditForm(prev => ({
                                            ...prev,
                                            status: newStatus,
                                            point: newStatus === '1' ? (prev.originalPoint || prev.point || '') : ''
                                        }));
                                    }}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                >
                                    <option value="0">待结项</option>
                                    <option value="1">已结项</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    批准人 <span className="text-red-500">*</span>
                                </label>
                                <div className="relative">
                                    <div
                                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                                        onClick={() => setEditShowApproverSelect(!editShowApproverSelect)}
                                    >
                                        {selectedApprovers.length > 0 ? (
                                            <div className="flex flex-wrap gap-2">
                                                {selectedApprovers.map(approver => (
                                                    <ApproverTag
                                                        key={approver.id}
                                                        approver={approver}
                                                        onRemove={handleEditRemoveApprover}
                                                    />
                                                ))}
                                            </div>
                                        ) : (
                                            <span className="text-gray-400">请选择批准人</span>
                                        )}
                                    </div>

                                    {editShowApproverSelect && (
                                        <div
                                            className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <div className="flex flex-wrap p-2 gap-2">
                                                {employees.map((employee) => (
                                                    <div
                                                        key={employee.id}
                                                        className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                                        onClick={() => {
                                                            const approver = {
                                                                id: employee.id,
                                                                name: employee.name
                                                            };
                                                            if (!selectedApprovers.find(r => r.id === employee.id)) {
                                                                setSelectedApprovers([...selectedApprovers, approver]);
                                                            }
                                                        }}
                                                    >
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedApprovers.some(r => r.id === employee.id)}
                                                            onChange={() => { }}
                                                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                        />
                                                        <div className={`flex-1 px-2 py-1 rounded ${selectedApprovers.some(r => r.id === employee.id) ? 'bg-blue-50' : ''}`}>
                                                            <span className="text-sm">{employee.name}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* 申请时间和预计结束时间在同一行 */}
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    申请时间
                                </label>
                                <input
                                    type="datetime-local"
                                    value={editForm.applyTime}
                                    onChange={(e) => setEditForm(prev => ({
                                        ...prev,
                                        applyTime: e.target.value
                                    }))}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                    step="1"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    预计结束时间
                                </label>
                                <input
                                    type="datetime-local"
                                    value={editForm.expectedEndTime}
                                    onChange={(e) => setEditForm(prev => ({
                                        ...prev,
                                        expectedEndTime: e.target.value
                                    }))}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                    step="1"
                                />
                            </div>
                        </div>

                        {/* 实际结束时间和结项状态在同一行 */}
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    实际结束时间
                                </label>
                                <input
                                    type="datetime-local"
                                    value={editForm.actualEndTime}
                                    onChange={(e) => setEditForm(prev => ({
                                        ...prev,
                                        actualEndTime: e.target.value
                                    }))}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                    step="1"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    结项评分
                                </label>
                                <input
                                    type="number"
                                    min="0"
                                    max="100"
                                    value={editForm.point}
                                    onChange={(e) => {
                                        const value = e.target.value;
                                        if (value === '' || (Number(value) >= 0 && Number(value) <= 100)) {
                                            setEditForm(prev => ({
                                                ...prev,
                                                point: value
                                            }));
                                        }
                                    }}
                                    disabled={editForm.status !== '1'}
                                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${editForm.status === '1' ? 'bg-white border-gray-300' : 'bg-gray-100 border-gray-200 cursor-not-allowed'
                                        }`}
                                    placeholder={editForm.status === '1' ? "请输入结项评分" : "仅已结项状态可评分"}
                                />
                            </div>

                        </div>

                        {/* 结项分析 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                结项分析
                            </label>
                            <textarea
                                value={editForm.analysis}
                                onChange={(e) => setEditForm(prev => ({
                                    ...prev,
                                    analysis: e.target.value
                                }))}
                                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-18 resize-none"
                            />
                        </div>

                        {/* 附件 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                附件
                            </label>

                            {/* 已上传的文件列表 */}
                            {editForm.files.filter(file => file.id).length > 0 && (
                                <div className="mb-4 space-y-2">
                                    <div className="text-sm text-gray-500 mb-2">已上传文件：</div>
                                    {editForm.files.filter(file => file.id).map((file, index) => (
                                        <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <FileTextIcon className="w-4 h-4 mr-2" />
                                                {file.name}
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <button
                                                    onClick={() => handlePreviewFile(file)}
                                                    className="text-blue-600 hover:text-blue-800"
                                                    title="预览"
                                                >
                                                    <EyeOpenIcon className="w-4 h-4" />
                                                </button>
                                                <button
                                                    onClick={() => handleEditFileDelete(editForm.files.findIndex(f => f.id === file.id))}
                                                    className="text-red-500 hover:text-red-700"
                                                    title="删除"
                                                >
                                                    <TrashIcon className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {/* 文件上传框 */}
                            <label
                                htmlFor="edit-file-upload"
                                className="flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                                onDragOver={(e) => e.preventDefault()}
                                onDrop={(e) => {
                                    e.preventDefault();
                                    const files = Array.from(e.dataTransfer.files);
                                    setEditForm(prev => ({
                                        ...prev,
                                        files: [...prev.files, ...files]
                                    }));
                                }}
                            >
                                <div className="space-y-1 text-center">
                                    <svg
                                        className="mx-auto h-8 w-8 text-gray-400"
                                        stroke="currentColor"
                                        fill="none"
                                        viewBox="0 0 48 48"
                                        aria-hidden="true"
                                    >
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                            strokeWidth={2}
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                    <div className="flex text-sm text-gray-600 justify-center">
                                        <span className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                                            点击上传文件
                                        </span>
                                        <input
                                            id="edit-file-upload"
                                            name="edit-file-upload"
                                            type="file"
                                            className="sr-only"
                                            multiple
                                            onChange={handleEditFileUpload}
                                        />
                                        <p className="pl-1">或拖拽文件到这里</p>
                                    </div>
                                    <p className="text-xs text-gray-500">支持任意文件格式</p>
                                </div>
                            </label>

                            {/* 新上传的文件列表 */}
                            {editForm.files.filter(file => !file.id).length > 0 && (
                                <div className="mt-4 space-y-2">
                                    <div className="text-sm text-gray-500 mb-2">待上传文件：</div>
                                    {editForm.files.filter(file => !file.id).map((file, index) => (
                                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <FileTextIcon className="w-4 h-4 mr-2" />
                                                {file.name}
                                            </div>
                                            <button
                                                onClick={() => handleEditFileDelete(editForm.files.findIndex(f => f === file))}
                                                className="text-red-500 hover:text-red-700"
                                                title="删除"
                                            >
                                                <TrashIcon className="w-4 h-4" />
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                    <Button
                        variant="outline"
                        onClick={() => setShowEditModal(false)}
                    >
                        取消
                    </Button>
                    <Button onClick={handleUpdateClosure}>
                        保存
                    </Button>
                </div>
            </div>
        </div>
    );
});

// 添加确认弹窗组件
const ConfirmDialog = ({ isOpen, title, message, onConfirm, onCancel }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-sm w-full">
                <h3 className="text-lg font-medium mb-2">{title}</h3>
                <p className="text-gray-600 mb-4">{message}</p>
                <div className="flex justify-end gap-2">
                    <button
                        onClick={onCancel}
                        className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        取消
                    </button>
                    <button
                        onClick={onConfirm}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                    >
                        确认
                    </button>
                </div>
            </div>
        </div>
    );
};

export const ProjectClosure = observer(() => {
    const [projects, setProjects] = useState([]);
    const [selectedProject, setSelectedProject] = useState(null);
    const [projectSearchQuery, setProjectSearchQuery] = useState('');
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [showNewModal, setShowNewModal] = useState(false);
    const [selectedRequirement, setSelectedRequirement] = useState(null);
    const [newRequirement, setNewRequirement] = useState({
        name: '',
        category: '',
        group: '',
        priority: '',
        description: '',
        input: '',
        process: '',
        output: ''
    });
    const [requirements, setRequirements] = useState(mockRequirements);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingPlan, setEditingPlan] = useState(null);
    const [page, setPage] = useState(0);
    const [size] = useState(10);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [deletingRequirement, setDeletingRequirement] = useState(null);
    const [totalPages, setTotalPages] = useState(1);
    const [totalElements, setTotalElements] = useState(0);
    const [employees, setEmployees] = useState([
        { id: 1, name: '张三' },
        { id: 2, name: '李四' },
        { id: 3, name: '王五' }
    ]);
    const [newPlan, setNewPlan] = useState({
        projectId: '',
        projectName: '',
        planName: '',
        creator: '',
        status: '',
        description: '',
        startTime: '',
        endTime: ''
    });
    const [showNewPlanModal, setShowNewPlanModal] = useState(false);
    const [planErrors, setPlanErrors] = useState({});
    const [testPlans, setTestPlans] = useState([
        {
            id: 1,
            name: '测试用例1',
            creatorName: '张三',
            status: 1,
            description: '这是测试用例1的描述',
            startTime: '2024-03-20T10:00:00',
            endTime: '2024-03-21T10:00:00'
        },
        {
            id: 2,
            name: '测试用例2',
            creatorName: '李四',
            status: 0,
            description: '这是测试用例2的描述',
            startTime: '2024-03-22T10:00:00',
            endTime: '2024-03-23T10:00:00'
        }
    ]);
    const [searchQuery, setSearchQuery] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [showViewModal, setShowViewModal] = useState(false);
    const [viewingPlan, setViewingPlan] = useState(null);
    const [searchStatus, setSearchStatus] = useState('');
    const [searchCreator, setSearchCreator] = useState('');
    const [currentPage, setCurrentPage] = useState(0);
    const [pageSize] = useState(10);
    const [newTestCase, setNewTestCase] = useState({
        projectId: '',
        projectName: '',
        caseName: '',
        creator: '',
        module: '',
        precondition: '',
        purpose: '',
        inputData: '',
        steps: '',
        expectedResult: '',
        files: []
    });
    const [uploadedFiles, setUploadedFiles] = useState([]);
    const [testCases, setTestCases] = useState([]);
    const [successMessage, setSuccessMessage] = useState('');
    const [showTemplateUploadModal, setShowTemplateUploadModal] = useState(false);
    const [templateFile, setTemplateFile] = useState(null);
    const [templateFileName, setTemplateFileName] = useState('');
    const [fieldErrors, setFieldErrors] = useState({
        name: '',
        creator: '',
        module: ''
    });
    const [existingFiles, setExistingFiles] = useState([]);
    const [showClosureModal, setShowClosureModal] = useState(false);
    const [showProjectSelect, setShowProjectSelect] = useState(false);
    const [closureForm, setClosureForm] = useState({
        projectId: '',      // 用于传给接口
        projectName: '',    // 用于显示选择的项目名称
        name: '',          // 新增：结项名称
        manager: '',
        applyTime: '',
        expectedEndTime: '',
        actualEndTime: '',
        analysis: '',
        applicant: '',
        approver: ''
    });
    const [closureErrors, setClosureErrors] = useState({});
    const [closureFiles, setClosureFiles] = useState([]);
    const [showApproverSelect, setShowApproverSelect] = useState(false);
    const [selectedApprovers, setSelectedApprovers] = useState([]);
    const [selectedClosure, setSelectedClosure] = useState(null);
    const [initialized, setInitialized] = useState(false);
    const [editForm, setEditForm] = useState({
        projectId: '',
        projectName: '',
        name: '',
        manager: '',
        applyTime: '',
        expectedEndTime: '',
        actualEndTime: '',
        applicant: '',
        approvers: [],
        status: '',
        point: '',
        analysis: '',
        files: []
    });
    const [editShowApproverSelect, setEditShowApproverSelect] = useState(false);
    const approverSelectRef = useRef(null);
    const projectSelectRef = useRef(null);
    const [showManagerSelect, setShowManagerSelect] = useState(false);
    const [showApplicantSelect, setShowApplicantSelect] = useState(false);
    const managerSelectRef = useRef(null);
    const applicantSelectRef = useRef(null);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [itemToDelete, setItemToDelete] = useState(null);

    const handleError = (message) => {
        setErrorMessage(message);
        setTimeout(() => {
            setErrorMessage('');
        }, 2000);
    };

    // 修改初始化函数
    const initializeData = async () => {
        if (initialized) return;

        try {
            // 构建查询参数
            const params = {
                page: 0,
                size: 10
            };

            // 并行获取项目列表和结项列表
            const [projectsData, closuresData] = await Promise.all([
                projectClosureApi.getProjectList(params),
                projectClosureApi.getClosureList(params)
            ]);

            setProjects(projectsData.content || []);
            setTestCases(closuresData || []);

            // 如果有结项数据，自动选中第一条记录
            if (closuresData && closuresData.length > 0) {
                const firstClosure = closuresData[0];
                // 获取第一条记录的详细信息
                const detailData = await projectClosureApi.getClosureDetail(firstClosure.id);
                setSelectedClosure(detailData);
            }

            setInitialized(true);
        } catch (error) {
            console.error('初始化数据获取失败:', error);
            handleError('初始化数据获取失败');
        }
    };

    // 确保只在组件挂载时执行一次初始化
    useEffect(() => {
        initializeData();
    }, []); // 只在组件挂载时执行一次

    const handleProjectSearch = (e) => {
        const searchValue = e.target.value;
        setProjectSearchQuery(searchValue);
        fetchProjects(searchValue);
    };

    const handleProjectSelect = (project) => {
        setSelectedProject(project);
        // 不再自动获取结项列表，由用户通过搜索按钮触发
    };

    const handleSearch = async () => {
        try {
            const params = {
                page: 0,
                size: 10,
                name: searchQuery,
                projectId: selectedProject?.id || ''
            };

            const data = await projectClosureApi.getClosureList(params);

            // 检查数据格式并相应处理
            if (Array.isArray(data)) {
                // 如果返回的是数组，直接设置
                setTestCases(data);
                setTotalPages(Math.ceil(data.length / 10));
                setTotalElements(data.length);
            } else if (data.content) {
                // 如果返回的是分页对象，取 content
                setTestCases(data.content);
                setTotalPages(data.totalPages);
                setTotalElements(data.totalElements);
            } else {
                console.error('返回的数据格式不正确:', data);
                handleError('数据格式不正确');
                return;
            }


            if (Array.isArray(data) ? data.length === 0 : (data.content?.length === 0)) {
                handleError('未找到相关结项数据');
            }
        } catch (error) {
            console.error('搜索失败:', error);
            handleError('搜索失败');
        }
    };

    const handleReset = async () => {
        try {
            // 清空搜索条件
            setSearchQuery('');
            setSelectedProject(null);

            // 构建查询参数
            const params = {
                page: 0,
                size: 10
            };

            // 调用服务层方法
            const data = await projectClosureApi.resetClosureList(params);

            // 检查数据格式并相应处理
            if (Array.isArray(data)) {
                setTestCases(data);
                setTotalPages(Math.ceil(data.length / 10));
                setTotalElements(data.length);
            } else if (data.content) {
                setTestCases(data.content);
                setTotalPages(data.totalPages);
                setTotalElements(data.totalElements);
            } else {
                console.error('返回的数据格式不正确:', data);
                handleError('数据格式不正确');
                return;
            }
        } catch (error) {
            console.error('重置失败:', error);
            handleError('重置失败');
        }
    };

    const handleCreateTestCase = () => {
        // 表单验证
        const errors = {};
        if (!newTestCase.caseName?.trim()) errors.name = '请输入用例名称';
        if (!newTestCase.creator) errors.creator = '请选择创建人';
        if (!newTestCase.module?.trim()) errors.module = '请输入用例模块';

        setFieldErrors(errors);
        if (Object.keys(errors).length > 0) return;

        // 创建新测试用例对象
        const newCase = {
            id: testCases.length + 1,
            name: newTestCase.caseName,
            module: newTestCase.module,
            creatorId: parseInt(newTestCase.creator),
            creatorName: employees.find(emp => emp.id.toString() === newTestCase.creator)?.name,
            condition: newTestCase.precondition,
            purpose: newTestCase.purpose,
            input: newTestCase.inputData,
            step: newTestCase.steps,
            expect: newTestCase.expectedResult,
            result: '',
            projectFiles: [],
            projectId: selectedProject.id,
            createdTime: new Date().toISOString()
        };

        // 更新状态
        setTestCases([...testCases, newCase]);
        setShowNewPlanModal(false);
        setNewTestCase({
            projectId: '',
            projectName: '',
            caseName: '',
            creator: '',
            module: '',
            precondition: '',
            purpose: '',
            inputData: '',
            steps: '',
            expectedResult: '',
            files: []
        });
        setUploadedFiles([]);
    };

    const handleUpdatePlan = () => {
        if (!validateForm()) return;

        const updatedCases = testCases.map(testCase => {
            if (testCase.id === editingPlan.id) {
                return {
                    ...testCase,
                    ...editingPlan,
                    creatorName: employees.find(emp => emp.id.toString() === editingPlan.creator)?.name
                };
            }
            return testCase;
        });

        setTestCases(updatedCases);
        setShowEditModal(false);
        setEditingPlan(null);
        setUploadedFiles([]);
    };

    const handlePreviewFile = async (file) => {
        try {
            const previewUrl = await projectClosureApi.fileApi.previewFile(file.name);
            if (previewUrl) {
                window.open(previewUrl, '_blank');
            } else {
                handleError('获取预览链接失败');
            }
        } catch (error) {
            console.error('预览文件失败:', error);
            handleError('预览文件失败');
        }
    };

    const handleDeleteTestCase = () => {
        const updatedCases = testCases.filter(testCase => testCase.id !== deletingRequirement.id);
        setTestCases(updatedCases);
        setShowDeleteModal(false);
        setDeletingRequirement(null);
    };

    const handleFilePreview = (file) => {
        // 根据文件路径打开预览
        window.open(file.url, '_blank');
    };


    const handleFileDownload = () => {
        handleError('文件下载功能暂不可用');
    };

    const handleTemplateUpload = () => {
        if (!templateFile) return;

        setShowTemplateUploadModal(false);
        setTemplateFile(null);
    };

    const handleTemplateDownload = () => {
        handleError('模板下载功能暂不可用');
    };

    const handleSuccess = (message) => {
        setSuccessMessage(message);
        setTimeout(() => {
            setSuccessMessage('');
        }, 3000);
    };

    const validateForm = () => {
        const errors = {};

        if (!editingPlan.name?.trim()) {
            errors.name = '请输入用例名称';
        }
        if (!editingPlan.creator) {
            errors.creator = '请选择创建人';
        }
        if (!editingPlan.module?.trim()) {
            errors.module = '请输入用例模块';
        }

        setFieldErrors(errors);
        return Object.keys(errors).length === 0;
    };


    const handleRemoveFile = (index) => {
        const newFiles = uploadedFiles.filter((_, i) => i !== index);
        setUploadedFiles(newFiles);
    };

    const handleDragOver = (e) => {
        e.preventDefault();
    };

    const handleDrop = (e) => {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files);
        setUploadedFiles([...uploadedFiles, ...files]);
    };


    const handlePageChange = async (newPage) => {
        try {
            setCurrentPage(newPage);
            const params = {
                page: newPage,
                size: pageSize,
                projectId: selectedProject?.id || '',
                name: searchQuery || ''
            };

            const data = await projectClosureApi.getClosureList(params);
            setTestCases(data.content || []);
            setTotalPages(data.totalPages);
            setTotalElements(data.totalElements);
        } catch (error) {
            console.error('获取数据失败:', error);
            handleError('获取数据失败');
        }
    };


    const fetchEmployees = async () => {
        try {
            const data = await projectClosureApi.getEmployeesList();
            setEmployees(data);
        } catch (error) {
            console.error('获取员工列表失败:', error);
            handleError('获取员工列表失败');
        }
    };

    useEffect(() => {
        fetchEmployees();
    }, []);

    const handleClosureFileUpload = (e) => {
        const files = Array.from(e.target.files || []);
        setClosureFiles(prev => [...prev, ...files]);
        e.target.value = '';
    };

    const handleClosureFileDelete = (index) => {
        setClosureFiles(prev => prev.filter((_, i) => i !== index));
    };

    const handleClosureFileDrop = (e) => {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files);
        setClosureFiles(prev => [...prev, ...files]);
    };

    const handleClosureFileDragOver = (e) => {
        e.preventDefault();
    };

    const handleOpenClosureModal = () => {
        if (!selectedProject) {
            handleError('请先选择一个项目');
            return;
        }

        // 如果表单是空的，才设置项目信息
        if (!closureForm.projectId) {
            setClosureForm(prev => ({
                ...prev,
                projectId: selectedProject.id,
                projectName: selectedProject.name
            }));
        }

        // 打开弹窗
        setShowClosureModal(true);
    };

    const handleRemoveApprover = (approverId) => {
        setSelectedApprovers(prev => prev.filter(r => r.id !== approverId));
    };

    const handleCreateClosure = async () => {
        try {
            const formData = new FormData();
            const projectClosure = {
                projectId: closureForm.projectId,
                name: closureForm.name,
                projectManagerId: employees.find(emp => emp.name === closureForm.manager)?.id,
                startDate: formatDateTime(closureForm.applyTime),
                planEndDate: formatDateTime(closureForm.expectedEndTime),
                actualEndDate: formatDateTime(closureForm.actualEndTime),
                analysis: closureForm.analysis || '',
                point: 0,
                status: 0,
                closedBy: employees.find(emp => emp.name === closureForm.applicant)?.id,
                approvedBy: selectedApprovers.map(approver => approver.id)
            };

            const projectClosureBlob = new Blob([JSON.stringify(projectClosure)], {
                type: 'application/json'
            });
            formData.append('projectClosure', projectClosureBlob);

            closureFiles.forEach(file => {
                formData.append('files', file);
            });

            await projectClosureApi.createClosure(formData);
            setShowClosureModal(false);
            resetClosureForm();
            await fetchClosureList();
        } catch (error) {
            console.error('创建失败:', error);
            handleError('创建失败');
        }
    };

    // 添加获取项目结项列表的函数
    const fetchClosureList = async () => {
        try {
            const params = {
                page: currentPage,
                size: pageSize,
                projectId: selectedProject?.id || '',
                name: searchQuery || ''
            };

            const data = await projectClosureApi.getClosureList(params);

            // 由于接口返回的是数组，我们需要手动处理分页
            const content = Array.isArray(data) ? data : (data.content || []);

            // 计算总页数和总元素数
            const totalElements = content.length;
            const totalPages = Math.ceil(totalElements / pageSize);

            // 更新列表数据
            setTestCases(content);
            setTotalPages(totalPages);
            setTotalElements(totalElements);

            return {
                content,
                totalPages,
                totalElements
            };
        } catch (error) {
            console.error('获取项目结项列表失败:', error);
            handleError('获取项目结项列表失败');
            return null;
        }
    };

    // 修改获取详情数据的处理函数
    const handleEdit = async (item) => {
        try {
            const data = await projectClosureApi.getClosureDetail(item.id);
            // 格式化时间为 datetime-local 格式
            const formatDateTime = (dateStr) => {
                if (!dateStr) return '';
                return new Date(dateStr).toISOString().slice(0, 16);
            };

            // 处理批准人数据 - 将 approvedBy 和 approvedByNames 组合成对象数组
            let approvers = [];
            if (Array.isArray(data.approvedBy) && Array.isArray(data.approvedByNames)) {
                approvers = data.approvedBy.map((id, index) => ({
                    id: id,
                    name: data.approvedByNames[index]
                }));
            } else if (data.approvedBy && data.approvedByNames) {
                // 如果是单个值，转换为数组形式
                approvers = [{
                    id: data.approvedBy,
                    name: data.approvedByNames
                }];
            }

            // 设置批准人到 selectedApprovers 状态
            setSelectedApprovers(approvers);


            // 处理已上传的文件数据，确保包含 url 属性
            const existingFiles = data.projectFiles ? data.projectFiles.map(file => ({
                id: file.id,
                name: file.name,
                url: projectClosureApi.getFilePreviewUrl(file.id)
            })) : [];

            // 设置表单数据
            setEditForm({
                id: data.id,
                projectId: data.projectId,
                projectName: data.projectName || '',
                name: data.name || '',
                manager: data.projectManagerName || '',
                applyTime: formatDateTime(data.startDate),
                expectedEndTime: formatDateTime(data.planEndDate),
                actualEndTime: formatDateTime(data.actualEndDate),
                applicant: data.closedByName || '',
                approvers: approvers,  // 使用处理后的批准人数组
                status: String(data.status || '0'),  // 确保转换为字符串类型
                point: data.status === 1 ? String(data.point || '') : '',  // 如果状态是已结项，显示评分，否则为空
                originalPoint: String(data.point || ''),  // 保存原始评分值
                analysis: data.analysis || '',
                files: existingFiles
            });

            // 打开修改弹窗
            setShowEditModal(true);
        } catch (error) {
            console.error('获取结项详情失败:', error);
            handleError('获取结项详情失败');
        }
    };

    const handleView = async (item) => {
        try {
            const data = await projectClosureApi.getClosureDetail(item.id);
            setSelectedClosure(data);
        } catch (error) {
            console.error('获取结项详情失败:', error);
            handleError('获取结项详情失败');
        }
    };

    const handleDelete = async (item) => {
        setItemToDelete(item);
        setShowDeleteConfirm(true);
    };

    const handleConfirmDelete = async () => {
        try {
            await projectClosureApi.deleteClosure(itemToDelete.id);
            await fetchClosureList();
            setShowDeleteConfirm(false);
            setItemToDelete(null);
        } catch (error) {
            console.error('删除失败:', error);
            handleError('删除失败');
        }
    };

    const handleCancelDelete = () => {
        setShowDeleteConfirm(false);
        setItemToDelete(null);
    };

    // 添加获取结项详情的函数
    const fetchClosureDetail = async (id) => {
        try {
            const data = await projectClosureApi.getClosureDetail(id);
            setSelectedClosure(data);
        } catch (error) {
            console.error('获取结项详情失败:', error);
            handleError('获取结项详情失败');
        }
    };

    // 添加更新结项的处理函数
    const handleUpdateClosure = async () => {
        try {
            const formData = new FormData();
            const projectClosure = {
                id: editForm.id,
                name: editForm.name,
                projectId: editForm.projectId,
                projectManagerId: employees.find(emp => emp.name === editForm.manager)?.id,
                startDate: formatDateTime(editForm.applyTime),
                planEndDate: formatDateTime(editForm.expectedEndTime),
                actualEndDate: formatDateTime(editForm.actualEndTime),
                status: parseInt(editForm.status) || 0,
                analysis: editForm.analysis,
                point: editForm.point === '' ? 0 : parseInt(editForm.point),
                closedBy: employees.find(emp => emp.name === editForm.applicant)?.id,
                approvedBy: editForm.approvers.map(a => a.id),
                fileIds: editForm.files.filter(file => file.id).map(file => file.id)
            };

            const projectClosureBlob = new Blob([JSON.stringify(projectClosure)], {
                type: 'application/json'
            });
            formData.append('projectClosure', projectClosureBlob);

            const newFiles = editForm.files.filter(file => !file.id);
            newFiles.forEach(file => {
                formData.append('files', file);
            });

            await projectClosureApi.updateClosure(editForm.id, formData);
            setShowEditModal(false);
            await fetchClosureList();
            if (selectedClosure?.id === editForm.id) {
                await fetchClosureDetail(editForm.id);
            }
        } catch (error) {
            console.error('更新失败:', error);
            handleError('更新失败');
        }
    };

    // 添加文件处理函数
    const handleEditFileUpload = (event) => {
        const files = Array.from(event.target.files);
        setEditForm(prev => ({
            ...prev,
            files: [...prev.files, ...files]
        }));
    };

    const handleEditFileDelete = (index) => {
        // 直接从表单状态中移除文件
        setEditForm(prev => ({
            ...prev,
            files: prev.files.filter((_, i) => i !== index)
        }));
    };

    // 添加修改弹窗的批准人删除处理函数
    const handleEditRemoveApprover = (id) => {
        setSelectedApprovers(prev => prev.filter(approver => approver.id !== id));
    };

    // 添加点击外部关闭下拉框的处理函数
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (approverSelectRef.current && !approverSelectRef.current.contains(event.target)) {
                setShowApproverSelect(false);
            }
            if (projectSelectRef.current && !projectSelectRef.current.contains(event.target)) {
                setShowProjectSelect(false);
            }
            if (managerSelectRef.current && !managerSelectRef.current.contains(event.target)) {
                setShowManagerSelect(false);
            }
            if (applicantSelectRef.current && !applicantSelectRef.current.contains(event.target)) {
                setShowApplicantSelect(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // 添加处理关闭创建弹窗的函数
    const handleCloseClosureModal = () => {
        // 关闭弹窗
        setShowClosureModal(false);
    };

    // 添加监听器
    useEffect(() => {
    }, [testCases]);

    const fetchProjects = async (searchValue = '') => {
        try {
            const params = {
                page: 0,
                size: 10,
                keyword: searchValue
            };
            
            const data = await projectClosureApi.getProjectList(params);
            setProjects(data.content || []);
        } catch (error) {
            console.error('获取项目列表失败:', error);
            handleError('获取项目列表失败');
        }
    };

    return (
        <div className="flex h-full">
            <div className="flex-1 flex">
                {/* 左侧内容 */}
                <div className="flex-1 p-6">
                    {errorMessage && (
                        <ErrorMessage message={errorMessage} />
                    )}
                    {successMessage && (
                        <SuccessMessage message={successMessage} />
                    )}

                    <div className="p-4 border-b">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-lg font-medium">项目结项</h2>
                        </div>

                        <div className="flex items-center gap-4">
                            {/* 搜索框 */}
                            <div className="relative w-[300px]">
                                <input
                                    type="text"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    placeholder="搜索结项名称"
                                    className="w-full pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                />
                                <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
                            </div>

                            {/* 项目选择下拉框 */}
                            <div className="relative w-[300px]">
                                <select
                                    value={selectedProject?.name || ''}
                                    onChange={(e) => {
                                        const project = projects.find(p => p.name === e.target.value);
                                        setSelectedProject(project || null);
                                    }}
                                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 appearance-none bg-white"
                                >
                                    <option value="">全部项目</option>
                                    {projects.map(project => (
                                        <option key={project.id} value={project.name}>
                                            {project.name}
                                        </option>
                                    ))}
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                </div>
                            </div>

                            {/* 搜索和重置按钮 */}
                            <Button
                                onClick={handleSearch}
                                style={{ backgroundColor: '#007bff', color: 'white' }}
                            >
                                搜索
                            </Button>
                            <Button variant="outline" onClick={handleReset}>
                                重置
                            </Button>
                            <Button onClick={() => setShowClosureModal(true)} style={{ backgroundColor: '#007bff', color: 'white' }}>
                                <PlusIcon className="w-4 h-4 mr-1" />
                                创建结项
                            </Button>
                        </div>
                    </div>

                    <div className="flex-1 overflow-auto">
                        <table className="min-w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        项目名称
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        结项名称
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        负责人
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        评分
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        评审时间
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {testCases.map((item, index) => (
                                    <tr
                                        key={index}
                                        className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} cursor-pointer hover:bg-gray-100`}
                                        onClick={() => fetchClosureDetail(item.id)}
                                    >
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {item.projectName}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {item.name}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {item.projectManagerName}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {item.point}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {item.startDate}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <div className="flex items-center space-x-2">
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleEdit(item);
                                                    }}
                                                    className="text-blue-600 hover:text-blue-900"
                                                    title="修改"
                                                >
                                                    <Pencil1Icon className="w-4 h-4" />
                                                </button>
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleDelete(item);
                                                    }}
                                                    className="text-red-600 hover:text-red-900"
                                                    title="删除"
                                                >
                                                    <TrashIcon className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    <div className="flex items-center justify-end px-4 py-3 border-t">
                        <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500 mr-4">
                                共 {totalElements} 条记录
                            </span>
                            <Button
                                variant="outline"
                                onClick={() => handlePageChange(Math.max(0, currentPage - 1))}
                                disabled={currentPage === 0}
                                className="px-2 py-1 text-sm"
                            >
                                上一页
                            </Button>
                            <div className="flex items-center">
                                <button
                                    className={`px-3 py-1 text-sm rounded-lg ${currentPage === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                                    onClick={() => handlePageChange(0)}
                                >
                                    1
                                </button>
                                {currentPage > 2 && <span className="px-2 text-gray-500">...</span>}
                                {currentPage > 1 && (
                                    <button
                                        className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                                        onClick={() => handlePageChange(currentPage - 1)}
                                    >
                                        {currentPage}
                                    </button>
                                )}
                                {currentPage > 0 && currentPage < totalPages - 1 && (
                                    <button
                                        className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                                    >
                                        {currentPage + 1}
                                    </button>
                                )}
                                {currentPage < totalPages - 2 && (
                                    <button
                                        className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                                        onClick={() => handlePageChange(currentPage + 1)}
                                    >
                                        {currentPage + 2}
                                    </button>
                                )}
                                {currentPage < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                                {totalPages > 1 && (
                                    <button
                                        className={`px-3 py-1 text-sm rounded-lg ${currentPage === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                                        onClick={() => handlePageChange(totalPages - 1)}
                                    >
                                        {totalPages}
                                    </button>
                                )}
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => handlePageChange(Math.min(totalPages - 1, currentPage + 1))}
                                disabled={currentPage >= totalPages - 1}
                                className="px-2 py-1 text-sm"
                            >
                                下一页
                            </Button>
                        </div>
                    </div>
                </div>

                {/* 20px 宽的间隔 */}
                <div className="w-[20px] bg-gray-100"></div>

                {/* 右侧内容 */}
                <div className="flex-1">
                    {selectedClosure ? (
                        <div className="p-6">
                            <h3 className="text-lg font-medium mb-4">结项详情</h3>
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">项目名称</label>
                                        <div className="mt-1 text-sm">{selectedClosure.projectName}</div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">结项名称</label>
                                        <div className="mt-1 text-sm">{selectedClosure.name}</div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">负责人</label>
                                        <div className="mt-1 text-sm">{selectedClosure.projectManagerName}</div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">评分</label>
                                        <div className="mt-1 text-sm">{selectedClosure.point || "-"}</div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">申请时间</label>
                                        <div className="mt-1 text-sm">{selectedClosure.startDate}</div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">实际结束时间</label>
                                        <div className="mt-1 text-sm">{selectedClosure.actualEndDate}</div>
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">结项状态</label>
                                        <div className="mt-1 text-sm">
                                            {selectedClosure.status === '1' ? '已结项' : '未结项'}
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-500">结项批准人</label>
                                        <div className="mt-1 text-sm">
                                            {selectedClosure.approvedByNames ?
                                                (Array.isArray(selectedClosure.approvedByNames) ?
                                                    selectedClosure.approvedByNames.join('，') :
                                                    selectedClosure.approvedByNames
                                                ) : '-'
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-500">结项分析</label>
                                    <div className="mt-1 text-sm whitespace-pre-wrap">{selectedClosure.analysis}</div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-500">交付文件</label>
                                    <div className="mt-2 space-y-2">
                                        {selectedClosure.projectFiles?.map((file, index) => (
                                            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                                <div className="flex items-center text-sm text-gray-900">
                                                    <FileTextIcon className="w-4 h-4 mr-2" />
                                                    {file.name}
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        onClick={() => handlePreviewFile(file)}
                                                        className="text-blue-600 hover:text-blue-800"
                                                        title="预览"
                                                    >
                                                        <EyeOpenIcon className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={async () => {
                                                            try {
                                                                const response = await projectClosureApi.fileApi.downloadFile(file.name);
                                                                if (!response.ok) {
                                                                    throw new Error('下载文件失败');
                                                                }
                                                                const blob = await response.blob();
                                                                const url = window.URL.createObjectURL(blob);
                                                                const link = document.createElement('a');
                                                                link.href = url;
                                                                link.download = file.name;
                                                                document.body.appendChild(link);
                                                                link.click();
                                                                document.body.removeChild(link);
                                                                window.URL.revokeObjectURL(url);
                                                                handleSuccess('文件下载成功');
                                                            } catch (error) {
                                                                console.error('下载文件失败:', error);
                                                                handleError(error.message);
                                                            }
                                                        }}
                                                        className="text-green-500 hover:text-green-700"
                                                        title="下载"
                                                    >
                                                        <DownloadIcon className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="h-full flex items-center justify-center text-gray-400">
                            请选择一个结项查看详情
                        </div>
                    )}
                </div>
            </div>

            {/* 模态框 */}
            {showClosureModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-lg w-[500px] flex flex-col">
                        <div className="p-4 border-b flex items-center justify-between">
                            <h3 className="text-lg font-medium">创建项目结项</h3>
                            <button
                                onClick={() => setShowClosureModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <Cross2Icon className="w-4 h-4" />
                            </button>
                        </div>

                        <div className="p-6">
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            项目名称
                                        </label>
                                        <div className="relative" ref={projectSelectRef}>
                                            <div
                                                className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                                                onClick={() => setShowProjectSelect(!showProjectSelect)}
                                            >
                                                <span className={closureForm.projectName ? "text-gray-700" : "text-gray-400"}>
                                                    {closureForm.projectName || "请选择项目"}
                                                </span>
                                                <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                            </div>
                                            {showProjectSelect && (
                                                <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                                                    <div className="flex flex-col p-1">
                                                        {projects.map((project) => (
                                                            <div
                                                                key={project.id}
                                                                className="flex items-center gap-2 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded"
                                                                onClick={() => {
                                                                    setClosureForm({
                                                                        ...closureForm,
                                                                        projectName: project.name,
                                                                        projectId: project.id
                                                                    });
                                                                    if (project.name) {
                                                                        setClosureErrors(prev => ({ ...prev, projectName: '' }));
                                                                    }
                                                                    setShowProjectSelect(false);
                                                                }}
                                                            >
                                                                <span className="text-sm text-gray-700">{project.name}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                        {closureErrors.projectName && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.projectName}
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            结项名称
                                        </label>
                                        <input
                                            type="text"
                                            value={closureForm.name}
                                            onChange={(e) => setClosureForm({ ...closureForm, name: e.target.value })}
                                            className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                                            placeholder="请输入结项名称"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            负责人 <span className="text-red-500">*</span>
                                        </label>
                                        <div className="relative" ref={managerSelectRef}>
                                            <div
                                                className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                                                onClick={() => setShowManagerSelect(!showManagerSelect)}
                                            >
                                                <span className={closureForm.manager ? "text-gray-700" : "text-gray-400"}>
                                                    {closureForm.manager || "请选择负责人"}
                                                </span>
                                                <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                            </div>
                                            {showManagerSelect && (
                                                <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                                                    <div className="flex flex-col p-1">
                                                        {employees.map((employee) => (
                                                            <div
                                                                key={employee.id}
                                                                className="flex items-center gap-2 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded"
                                                                onClick={() => {
                                                                    setClosureForm({
                                                                        ...closureForm,
                                                                        manager: employee.name
                                                                    });
                                                                    if (employee.name) {
                                                                        setClosureErrors(prev => ({ ...prev, manager: '' }));
                                                                    }
                                                                    setShowManagerSelect(false);
                                                                }}
                                                            >
                                                                <span className="text-sm text-gray-700">{employee.name}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                        {closureErrors.manager && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.manager}
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            申请时间 <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="datetime-local"
                                            value={closureForm.applyTime}
                                            onChange={(e) => {
                                                setClosureForm({ ...closureForm, applyTime: e.target.value });
                                                if (e.target.value) {
                                                    setClosureErrors(prev => ({ ...prev, applyTime: '' }));
                                                }
                                            }}
                                            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${closureErrors.applyTime ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                            step="1"
                                        />
                                        {closureErrors.applyTime && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.applyTime}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            预计结束时间 <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="datetime-local"
                                            value={closureForm.expectedEndTime}
                                            onChange={(e) => {
                                                setClosureForm({ ...closureForm, expectedEndTime: e.target.value });
                                                if (e.target.value) {
                                                    setClosureErrors(prev => ({ ...prev, expectedEndTime: '' }));
                                                }
                                            }}
                                            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${closureErrors.expectedEndTime ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                            step="1"
                                        />
                                        {closureErrors.expectedEndTime && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.expectedEndTime}
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            实际结束时间 <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="datetime-local"
                                            value={closureForm.actualEndTime}
                                            onChange={(e) => {
                                                setClosureForm({ ...closureForm, actualEndTime: e.target.value });
                                                if (e.target.value) {
                                                    setClosureErrors(prev => ({ ...prev, actualEndTime: '' }));
                                                }
                                            }}
                                            className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${closureErrors.actualEndTime ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                            step="1"
                                        />
                                        {closureErrors.actualEndTime && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.actualEndTime}
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            结项发起人 <span className="text-red-500">*</span>
                                        </label>
                                        <div className="relative" ref={applicantSelectRef}>
                                            <div
                                                className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                                                onClick={() => setShowApplicantSelect(!showApplicantSelect)}
                                            >
                                                <span className={closureForm.applicant ? "text-gray-700" : "text-gray-400"}>
                                                    {closureForm.applicant || "请选择发起人"}
                                                </span>
                                                <ChevronRightIcon className="w-4 h-4 text-gray-400 rotate-90" />
                                            </div>
                                            {showApplicantSelect && (
                                                <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                                                    <div className="flex flex-col p-1">
                                                        {employees.map((employee) => (
                                                            <div
                                                                key={employee.id}
                                                                className="flex items-center gap-2 cursor-pointer px-2 py-1.5 hover:bg-gray-50 rounded"
                                                                onClick={() => {
                                                                    setClosureForm({
                                                                        ...closureForm,
                                                                        applicant: employee.name
                                                                    });
                                                                    if (employee.name) {
                                                                        setClosureErrors(prev => ({ ...prev, applicant: '' }));
                                                                    }
                                                                    setShowApplicantSelect(false);
                                                                }}
                                                            >
                                                                <span className="text-sm text-gray-700">{employee.name}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                        {closureErrors.applicant && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.applicant}
                                            </div>
                                        )}
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            批准人 <span className="text-red-500">*</span>
                                        </label>
                                        <div className="relative" ref={approverSelectRef}>
                                            <div
                                                className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                                                onClick={() => setShowApproverSelect(!showApproverSelect)}
                                            >
                                                {selectedApprovers.length > 0 ? (
                                                    <div className="flex flex-wrap gap-2">
                                                        {selectedApprovers.map(approver => (
                                                            <ApproverTag
                                                                key={approver.id}
                                                                approver={approver}
                                                                onRemove={handleRemoveApprover}
                                                            />
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">请选择批准人</span>
                                                )}
                                            </div>
                                            {showApproverSelect && (
                                                <div
                                                    className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto"
                                                >
                                                    <div className="flex flex-wrap p-2 gap-2">
                                                        {employees.map((employee) => (
                                                            <div
                                                                key={employee.id}
                                                                className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    const approver = {
                                                                        id: employee.id,
                                                                        name: employee.name
                                                                    };
                                                                    if (!selectedApprovers.find(r => r.id === employee.id)) {
                                                                        setSelectedApprovers([...selectedApprovers, approver]);
                                                                    }
                                                                }}
                                                            >
                                                                <input
                                                                    type="checkbox"
                                                                    checked={selectedApprovers.some(r => r.id === employee.id)}
                                                                    readOnly
                                                                />
                                                                <span>{employee.name}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                        {closureErrors.approver && (
                                            <div className="mt-1 text-sm text-red-500 flex items-center">
                                                <CrossCircledIcon className="w-4 h-4 mr-1" />
                                                {closureErrors.approver}
                                            </div>
                                        )}
                                    </div>
                                </div>


                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        结项分析
                                    </label>
                                    <textarea
                                        value={closureForm.analysis}
                                        onChange={(e) => setClosureForm({ ...closureForm, analysis: e.target.value })}
                                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${closureErrors.analysis ? 'border-red-500' : 'border-gray-300'
                                            } h-[60px] resize-none`}
                                        placeholder="请输入结项分析"
                                    />
                                    {closureErrors.analysis && (
                                        <div className="mt-1 text-sm text-red-500 flex items-center">
                                            <CrossCircledIcon className="w-4 h-4 mr-1" />
                                            {closureErrors.analysis}
                                        </div>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        附件
                                    </label>
                                    <label
                                        htmlFor="closure-file-upload"
                                        className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                                        onDragOver={handleClosureFileDragOver}
                                        onDrop={handleClosureFileDrop}
                                    >
                                        <div className="space-y-1 text-center">
                                            <svg
                                                className="mx-auto h-8 w-8 text-gray-400"
                                                stroke="currentColor"
                                                fill="none"
                                                viewBox="0 0 48 48"
                                                aria-hidden="true"
                                            >
                                                <path
                                                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                                    strokeWidth={2}
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                />
                                            </svg>
                                            <div className="flex text-sm text-gray-600 justify-center">
                                                <span className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                    点击上传文件
                                                </span>
                                                <input
                                                    id="closure-file-upload"
                                                    name="closure-file-upload"
                                                    type="file"
                                                    className="sr-only"
                                                    multiple
                                                    onChange={handleClosureFileUpload}
                                                />
                                                <p className="pl-1">或拖拽文件到这里</p>
                                            </div>
                                            <p className="text-xs text-gray-500">支持任意文件格式</p>
                                        </div>
                                    </label>
                                    {closureFiles.length > 0 && (
                                        <div className="mt-4 space-y-2">
                                            {closureFiles.map((file, index) => (
                                                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                                                    <div className="flex items-center text-sm text-gray-900">
                                                        <FileTextIcon className="w-4 h-4 mr-2" />
                                                        {file.name}
                                                    </div>
                                                    <button
                                                        onClick={() => handleClosureFileDelete(index)}
                                                        className="text-red-500 hover:text-red-700"
                                                        title="删除"
                                                    >
                                                        <TrashIcon className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                            <Button
                                variant="outline"
                                onClick={handleCloseClosureModal}
                            >
                                取消
                            </Button>
                            <Button onClick={handleCreateClosure}>
                                创建
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* 修改弹窗 */}
            {showEditModal && (
                <EditModal
                    editForm={editForm}
                    setEditForm={setEditForm}
                    showEditModal={showEditModal}
                    setShowEditModal={setShowEditModal}
                    employees={employees}
                    selectedApprovers={selectedApprovers}
                    setSelectedApprovers={setSelectedApprovers}
                    editShowApproverSelect={editShowApproverSelect}
                    setEditShowApproverSelect={setEditShowApproverSelect}
                    handleUpdateClosure={handleUpdateClosure}
                    handleEditRemoveApprover={handleEditRemoveApprover}
                    handleEditFileUpload={handleEditFileUpload}
                    handleEditFileDelete={handleEditFileDelete}
                    handlePreviewFile={handlePreviewFile}
                />
            )}

            {/* 添加确认弹窗 */}
            <ConfirmDialog
                isOpen={showDeleteConfirm}
                title="确认删除"
                message="确定要删除这条记录吗？此操作不可撤销。"
                onConfirm={handleConfirmDelete}
                onCancel={handleCancelDelete}
            />
        </div>
    );
});