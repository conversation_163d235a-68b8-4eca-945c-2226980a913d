import axios from 'axios';

import { fetchData } from './fetch';

const FILE_URL = 'http://172.27.1.153:8082';

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (searchName = '') => {
    const params = searchName ? `?name=${encodeURIComponent(searchName)}` : '';
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list${params}`).then(res => res.json());
  },

  // 获取单个项目详情
  getProjectDetail: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/one/${projectId}`).then(res => res.json()),
};

// 项目输出相关接口
export const projectOutputApi = {
  // 获取项目输出列表
  getProjectOutputList: (projectId, name = '') => {
    const params = name ? `?name=${encodeURIComponent(name)}` : '';
    return fetch(`${fetchData["STAFF_URL"]}/api/project-outs/by-project/${projectId}${params}`).then(res => res.json());
  },

  // 获取单个项目输出详情
  getProjectOutputDetail: (outputId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-outs/one/${outputId}`).then(res => res.json()),

  // 创建项目输出
  createProjectOutput: (formData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-outs/create`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json'
      },
      body: formData
    }).then(res => res.json()),

  // 更新项目输出
  updateProjectOutput: (outputId, formData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-outs/update/${outputId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除项目输出
  deleteProjectOutput: (outputId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-outs/delete/${outputId}`, {
      method: 'GET'
    }).then(res => res.json()),

  // 打包单个项目输出
  archiveProjectOutput: (projectOutId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/project-outs/archive/one?projectOutId=${projectOutId}`, {
      method: 'GET'
    }).then(res => res.blob()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
};

// 文件相关接口
export const fileApi = {
  // 下载文件
  downloadFile: (fileName) => 
    fetch(`${fetchData["PROJECT_URL"].replace}/api/file/download?fileName=${encodeURIComponent(fileName)}&bucketName=projectout`),

  // 预览文件
  previewFile: (fileName) => 
    fetch(`${fetchData["PROJECT_URL"].replace}/api/file/preview?fileName=${encodeURIComponent(fileName)}&bucketName=projectout`).then(res => res.text()),

  // 全量打包下载
  archiveAllFiles: () => 
    fetch(`${fetchData["PROJECT_URL"].replace('/api', '')}/api/file/archive?bucketId=2&bucketName=projectout`, {
      method: 'GET'
    }).then(res => res.blob()),
}; 