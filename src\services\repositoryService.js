import { userStore } from '../store/userStore';
import { fetchData } from './fetch';

const getHeaders = () => ({
  'Private-Token': userStore.getUserData()?.accessToken || '',
  'Content-Type': 'application/json'
});

export const repositoryService = {
  // 通过SSH Key获取用户信息
  getUserInfo: async () => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/user`, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取用户的项目列表
  getUserProjects: async (userId) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/users/${userId}/projects`, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取项目详情
  getProjectDetails: async (projectId) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}`, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取项目分支列表
  getProjectBranches: async (projectId) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/branches`, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取项目文件列表
  getProjectFiles: async (projectId, branch = 'main', path = '') => {
    const url = `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/tree?ref=${branch}${path ? `&path=${encodeURIComponent(path)}` : ''}`;
    const response = await fetch(url, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取文件内容
  getFileContent: async (projectId, filePath, branch = 'main') => {
    const response = await fetch(
      `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/files/${encodeURIComponent(filePath)}/raw?ref=${branch}`,
      { headers: getHeaders() }
    );
    return await response.text();
  },

  // 创建新分支
  createBranch: async (projectId, branchName, ref) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/branches`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        branch: branchName,
        ref
      })
    });
    return await response.json();
  },

  // 创建新项目
  createProject: async (projectData) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects`, {
      method: 'POST',
      headers: getHeaders(),
      body: JSON.stringify({
        ...projectData,
        initialize_with_readme: "true",
        default_branch: 'main'
      })
    });
    return await response.json();
  },

  // 上传文件
  uploadFile: async (projectId, filePath, content, branch, commitMessage) => {
    const response = await fetch(
      `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/files/${encodeURIComponent(filePath)}`,
      {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          branch,
          content,
          commit_message: commitMessage,
          encoding: 'base64'
        })
      }
    );
    return await response.json();
  },

  // 创建文件夹
  createFolder: async (projectId, path, branch, commitMessage) => {
    const response = await fetch(
      `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/files/${encodeURIComponent(path)}`,
      {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({
          branch,
          content: '',
          commit_message: commitMessage
        })
      }
    );
    return await response.json();
  },

  // 搜索项目
  searchProjects: async (searchValue) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects?search=${encodeURIComponent(searchValue)}`, {
      headers: getHeaders()
    });
    return await response.json();
  },

  // 获取所有项目列表（从项目管理系统）
  getAllProjects: async () => {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/projects/list`);
    return await response.json();
  },

  // 获取项目克隆URL
  getCloneUrls: async (projectId) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}`, {
      headers: getHeaders()
    });
    const projectData = await response.json();
    return {
      ssh_url_to_repo: projectData.ssh_url_to_repo,
      http_url_to_repo: projectData.http_url_to_repo
    };
  },

  // 上传SSH Key
  uploadSshKey: async (sshKeyData) => {
    const response = await fetch(`${fetchData["BASE_URL"]}/api/employees/upload/ssh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: sshKeyData.id,
        sshKey: sshKeyData.key
      })
    });
    return await response.json();
  },

  // 删除项目
  deleteProject: async (projectId) => {
    const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}`, {
      method: 'DELETE',
      headers: getHeaders()
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '删除项目失败');
    }
    
    return true;
  }
}; 