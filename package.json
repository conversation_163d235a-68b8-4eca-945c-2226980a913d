{"name": "vite-react-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.24.1", "axios": "^1.8.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "mobx": "^6.12.0", "mobx-react-lite": "^4.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "react-toastify": "^11.0.5", "styled-components": "^6.1.14", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@shadcn/ui": "^0.0.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "json-server": "^1.0.0-beta.3", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^5.4.14", "vite-plugin-style-import": "^2.0.0"}}