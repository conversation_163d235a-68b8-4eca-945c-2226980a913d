import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  CheckIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { productAcceptanceApi, projectApi, fileApi, employeeApi } from '../services/productAcceptanceService';

// 验收状态选项
const acceptanceStatusOptions = [
  { value: 0, label: '已通过' },
  { value: 1, label: '让步验收' },
  { value: 2, label: '未通过' }
];

// 错误提示组件
const ErrorMessage = ({ message }) => (
  <div className="fixed top-20 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 shadow-lg z-50">
    <Cross2Icon className="w-4 h-4 text-red-500" />
    <div className="text-sm text-red-800">{message}</div>
  </div>
);

// 时间输入组件
const DateTimeInput = ({ value, onChange, className, placeholder }) => (
  <div className="relative">
    <input
      type="datetime-local"
      value={value}
      onChange={onChange}
      className={`${className} ${!value ? 'text-transparent' : ''}`}
      step="1"
    />
    {!value && (
      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
        {placeholder || '请选择时间'}
      </div>
    )}
  </div>
);

const ProductAcceptance = observer(() => {
  // 基础数据状态
  const [projects, setProjects] = useState([]); // 项目列表
  const [acceptances, setAcceptances] = useState([]); // 验收数据
  const [employees, setEmployees] = useState([]); // 员工列表
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中项目

  // 搜索相关状态
  const [projectSearchQuery, setProjectSearchQuery] = useState(''); // 项目搜索
  const [searchQuery, setSearchQuery] = useState(''); // 验收搜索
  const [searchStartTime, setSearchStartTime] = useState(''); // 开始时间搜索
  const [searchEndTime, setSearchEndTime] = useState(''); // 结束时间搜索

  // 弹窗状态
  const [showNewModal, setShowNewModal] = useState(false); // 新建验收弹窗
  const [showEditModal, setShowEditModal] = useState(false); // 编辑验收弹窗
  const [showViewModal, setShowViewModal] = useState(false); // 查看验收弹窗
  const [showDeleteAcceptanceModal, setShowDeleteAcceptanceModal] = useState(false); // 删除确认弹窗

  // 表单数据状态
  const [newAcceptance, setNewAcceptance] = useState({
    acceptanceName: '',
    manager: '',
    memberIds: [],
    memberNames: [],
    standard: '',
    suggestion: '',
    acceptanceTime: '',
    files: []
  });

  const [editingAcceptance, setEditingAcceptance] = useState({
    id: '',
    name: '',
    responseName: '',
    memberIds: [],
    memberNames: [],
    standard: '',
    advice: '',
    acceptanceTime: '',
    endAcceptanceTime: '',
    result: '',
    files: [],
    projectFiles: []
  });

  // 其他状态
  const [viewingPlan, setViewingPlan] = useState(null); // 查看的验收详情
  const [deletingAcceptance, setDeletingAcceptance] = useState(null); // 待删除的验收
  const [formErrors, setFormErrors] = useState({}); // 表单错误
  const [errorMessage, setErrorMessage] = useState(''); // 全局错误提示
  const [timeError, setTimeError] = useState(''); // 时间选择错误
  const [totalPages, setTotalPages] = useState(0); // 总页数
  const [totalElements, setTotalElements] = useState(0); // 总记录数
  const [currentPage, setCurrentPage] = useState(0); // 当前页

  // 下拉框状态
  const [showAcceptorSelect, setShowAcceptorSelect] = useState(false);
  const [acceptorSearchQuery, setAcceptorSearchQuery] = useState('');
  const [isManagerDropdownOpen, setIsManagerDropdownOpen] = useState(false);
  const [isEditManagerDropdownOpen, setIsEditManagerDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);

  // 错误提示处理
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 2000);
  };

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await projectApi.getProjectList();
      if (response.data) {
        setProjects(response.data);
        if (response.data.length > 0) {
          setSelectedProject(response.data[0]);
          fetchAcceptanceData(response.data[0].id);
        }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      handleError('获取项目列表失败');
    }
  };

  // 获取验收数据
  const fetchAcceptanceData = async (projectId, searchParams = {}) => {
    try {
      const response = await productAcceptanceApi.getAcceptancePage({
        projectId,
        name: searchParams.searchQuery || '',
        startTime: searchParams.startTime || '',
        endTime: searchParams.endTime || '',
        page: 0,
        size: 10
      });

      if (response.data) {
        setAcceptances(response.data.content);
        setTotalPages(response.data.totalPages);
        setTotalElements(response.data.totalElements);
      }
    } catch (error) {
      console.error('获取验收数据失败:', error);
      handleError('获取验收数据失败');
    }
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const response = await employeeApi.getEmployeeList();
      if (response.data) {
        setEmployees(response.data.map(emp => ({
          id: emp.id,
          name: emp.name
        })));
      }
    } catch (error) {
      console.error('获取员工列表失败:', error);
      handleError('获取员工列表失败');
    }
  };

  // 项目选择处理
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    fetchAcceptanceData(project.id);
  };

  // 项目搜索处理
  const handleProjectSearch = (e) => {
    setProjectSearchQuery(e.target.value);
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchEmployees();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    if ((searchStartTime && !searchEndTime) || (!searchStartTime && searchEndTime)) {
      setTimeError('请同时选择验收时间和验收结束时间');
      setTimeout(() => setTimeError(''), 3000);
      return;
    }
    setTimeError('');
    fetchAcceptanceData(selectedProject.id, {
      searchQuery,
      startTime: searchStartTime,
      endTime: searchEndTime
    });
  };

  // 重置搜索
  const handleReset = () => {
    setSearchQuery('');
    setSearchStartTime('');
    setSearchEndTime('');
    setTimeError('');
    fetchAcceptanceData(selectedProject.id, {
      searchQuery: '',
      startTime: '',
      endTime: ''
    });
  };

  // 文件预览处理
  const handlePreviewFile = async (fileName) => {
    try {
      const response = await fileApi.previewFile(fileName);
      window.open(response.data, '_blank');
    } catch (error) {
      console.error('获取文件预览失败:', error);
      handleError('获取文件预览失败');
    }
  };

  // 文件下载处理
  const handleDownloadFile = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('文件下载失败:', error);
      handleError('文件下载失败');
    }
  };

  // 文件上传处理
  const handleFileUpload = (e) => {
    const newFiles = Array.from(e.target.files || []);
    setNewAcceptance(prev => ({
      ...prev,
      files: [...prev.files, ...newFiles]
    }));
  };

  // 文件删除处理
  const handleFileDelete = (index) => {
    setNewAcceptance(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  // 创建验收
  const handleCreateAcceptance = async () => {
    // 表单验证
    const errors = {};
    if (!newAcceptance.acceptanceName.trim()) errors.acceptanceName = '请输入验收名称';
    if (!newAcceptance.manager) errors.manager = '请选择负责人';
    if (!newAcceptance.memberIds?.length) errors.acceptors = '请选择验收人员';
    if (!newAcceptance.standard.trim()) errors.standard = '请输入验收标准';
    if (!newAcceptance.acceptanceTime) errors.acceptanceTime = '请选择验收时间';

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      const responseEmployee = employees.find(emp => emp.name === newAcceptance.manager);
      const acceptance = {
        projectId: selectedProject.id,
        name: newAcceptance.acceptanceName,
        startTime: newAcceptance.acceptanceTime,
        endTime: newAcceptance.acceptanceTime,
        responseId: responseEmployee?.id || 0,
        memberId: newAcceptance.memberIds,
        standard: newAcceptance.standard,
        advice: newAcceptance.suggestion || ''
      };

      const formData = new FormData();
      formData.append('acceptance', new Blob([JSON.stringify(acceptance)], {
        type: 'application/json'
      }));

      if (newAcceptance.files?.length) {
        newAcceptance.files.forEach(file => {
          formData.append('files', file);
        });
      }

      await productAcceptanceApi.createAcceptance(formData);
      setShowNewModal(false);
      setNewAcceptance({
        acceptanceName: '',
        manager: '',
        memberIds: [],
        memberNames: [],
        standard: '',
        suggestion: '',
        acceptanceTime: '',
        files: []
      });
      setFormErrors({});
      fetchAcceptanceData(selectedProject.id);
      handleError('创建成功');
    } catch (error) {
      console.error('创建验收失败:', error);
      handleError('创建验收失败');
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showAcceptorSelect && !event.target.closest('.acceptor-select-container')) {
        setShowAcceptorSelect(false);
      }
      if (isManagerDropdownOpen && !event.target.closest('.manager-dropdown-container')) {
        setIsManagerDropdownOpen(false);
      }
      if (isEditManagerDropdownOpen && !event.target.closest('.edit-manager-dropdown-container')) {
        setIsEditManagerDropdownOpen(false);
      }
      if (isStatusDropdownOpen && !event.target.closest('.status-dropdown-container')) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showAcceptorSelect, isManagerDropdownOpen, isEditManagerDropdownOpen, isStatusDropdownOpen]);

  // 日期格式化
  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    return dateTimeString.replace('T', ' ');
  };

  // 查看验收详情
  const handleView = async (acceptance) => {
    try {
      const response = await productAcceptanceApi.getAcceptanceDetail(acceptance.id);
      if (response.data) {
        setViewingPlan(response.data);
        setShowViewModal(true);
      }
    } catch (error) {
      console.error('获取验收详情失败:', error);
      handleError('获取验收详情失败');
    }
  };

  // 编辑验收
  const handleEdit = async (acceptance) => {
    try {
      const response = await productAcceptanceApi.getAcceptanceDetail(acceptance.id);
      if (response.data) {
        const acceptanceData = response.data;
        setEditingAcceptance({
          id: acceptanceData.id,
          name: acceptanceData.name,
          responseName: acceptanceData.responseName,
          memberIds: acceptanceData.memberId || [],
          memberNames: acceptanceData.memberNames || [],
          standard: acceptanceData.standard,
          advice: acceptanceData.advice || '',
          acceptanceTime: acceptanceData.startTime,
          endAcceptanceTime: acceptanceData.endTime,
          result: acceptanceData.result,
          files: [],
          projectFiles: acceptanceData.projectFiles || []
        });
        setShowEditModal(true);
      }
    } catch (error) {
      console.error('获取验收详情失败:', error);
      handleError('获取验收详情失败');
    }
  };

  // 更新验收
  const handleUpdateAcceptance = async () => {
    // 表单验证
    const errors = {};
    if (!editingAcceptance.name?.trim()) errors.name = '请输入验收名称';
    if (!editingAcceptance.responseName) errors.responseName = '请选择负责人';
    if (!editingAcceptance.memberIds?.length) errors.memberIds = '请选择验收人员';
    if (!editingAcceptance.standard?.trim()) errors.standard = '请输入验收标准';
    if (!editingAcceptance.acceptanceTime) errors.acceptanceTime = '请选择验收时间';
    if (editingAcceptance.result === undefined || editingAcceptance.result === '') {
      errors.result = '请选择验收状态';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      const responseEmployee = employees.find(emp => emp.name === editingAcceptance.responseName);
      const acceptance = {
        id: editingAcceptance.id,
        projectId: selectedProject.id,
        name: editingAcceptance.name,
        startTime: editingAcceptance.acceptanceTime,
        endTime: editingAcceptance.endAcceptanceTime,
        responseId: responseEmployee?.id || 0,
        memberId: editingAcceptance.memberIds,
        memberNames: editingAcceptance.memberNames,
        standard: editingAcceptance.standard,
        result: editingAcceptance.result,
        advice: editingAcceptance.advice || '',
        projectFiles: editingAcceptance.projectFiles || []
      };

      const formData = new FormData();
      formData.append('acceptance', new Blob([JSON.stringify(acceptance)], {
        type: 'application/json'
      }));

      if (editingAcceptance.files?.length) {
        editingAcceptance.files.forEach(file => {
          formData.append('files', file);
        });
      }

      await productAcceptanceApi.updateAcceptance(editingAcceptance.id, formData);
      setShowEditModal(false);
      setEditingAcceptance(null);
      setFormErrors({});
      fetchAcceptanceData(selectedProject.id);
      handleError('修改成功');
    } catch (error) {
      console.error('修改验收失败:', error);
      handleError('修改验收失败');
    }
  };

  // 删除验收
  const handleDelete = (acceptance) => {
    setDeletingAcceptance(acceptance);
    setShowDeleteAcceptanceModal(true);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    try {
      await productAcceptanceApi.deleteAcceptance(deletingAcceptance.id);
      setShowDeleteAcceptanceModal(false);
      setDeletingAcceptance(null);
      fetchAcceptanceData(selectedProject.id);
      handleError('删除成功');
    } catch (error) {
      console.error('删除验收失败:', error);
      handleError('删除验收失败');
    }
  };

  // 主页面渲染
  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 全局错误提示 */}
      {errorMessage && <ErrorMessage message={errorMessage} />}

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${
                selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
              }`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-1 text-sm">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  project.status === 0 ? 'bg-gray-100 text-gray-800' :
                  project.status === 1 ? 'bg-blue-100 text-blue-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {project.status === 0 ? '未开始' :
                   project.status === 1 ? '进行中' : '已结束'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧验收管理区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {selectedProject ? (
          <>
            {/* 页面标题 */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">产品验收管理</p>
              </div>
            </div>

            {/* 搜索和操作区域 */}
            <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
              <div className="flex flex-wrap gap-4 items-center">
                {/* 验收名称搜索 */}
                <div className="relative w-[300px]">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索验收名称..."
                    className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                </div>

                {/* 验收开始时间 */}
                <div className="w-[200px]">
                  <DateTimeInput
                    value={searchStartTime}
                    onChange={(e) => {
                      setSearchStartTime(e.target.value);
                      setTimeError('');
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                      timeError ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="开始时间"
                  />
                </div>

                {/* 验收结束时间 */}
                <div className="w-[200px]">
                  <DateTimeInput
                    value={searchEndTime}
                    onChange={(e) => {
                      setSearchEndTime(e.target.value);
                      setTimeError('');
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                      timeError ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="结束时间"
                  />
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-2">
                  <Button onClick={handleSearch} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} variant="outline">
                    重置
                  </Button>
                  <Button
                    onClick={() => setShowNewModal(true)}
                    className="flex items-center gap-2"
                    style={{ backgroundColor: '#0070f3', color: 'white' }}
                  >
                    <PlusIcon className="w-4 h-4" />
                    添加验收
                  </Button>
                </div>
              </div>

              {/* 时间选择错误提示 */}
              {timeError && (
                <div className="fixed top-20 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 shadow-lg z-50">
                  <Cross2Icon className="w-4 h-4 text-red-500" />
                  <div className="text-sm text-red-800">{timeError}</div>
                </div>
              )}
            </div>

            {/* 验收列表表格 */}
            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">验收名称</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">负责人</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">验收人员</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">验收时间</th>
                    <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {acceptances.map(acceptance => (
                    <tr key={acceptance.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900">{acceptance.name}</td>
                      <td className="px-4 py-3 text-sm text-gray-500">{acceptance.responseName}</td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {acceptance.memberNames?.join(', ') || '-'}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatDateTime(acceptance.startTime)}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center justify-center space-x-3">
                          <button
                            onClick={() => handleView(acceptance)}
                            className="text-gray-400 hover:text-green-500"
                            title="查看详情"
                          >
                            <EyeOpenIcon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEdit(acceptance)}
                            className="text-gray-400 hover:text-blue-500"
                            title="编辑验收"
                          >
                            <Pencil1Icon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(acceptance)}
                            className="text-gray-400 hover:text-red-500"
                            title="删除验收"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* 简化的分页器 */}
              <div className="flex items-center justify-between px-4 py-3 border-t">
                <div className="text-sm text-gray-500">
                  共 {totalElements} 条记录，第 {currentPage + 1} / {totalPages} 页
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newPage = Math.max(0, currentPage - 1);
                      setCurrentPage(newPage);
                      fetchAcceptanceData(selectedProject.id, {
                        searchQuery,
                        startTime: searchStartTime,
                        endTime: searchEndTime
                      });
                    }}
                    disabled={currentPage === 0}
                    className="px-3 py-1 text-sm"
                  >
                    上一页
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newPage = Math.min(totalPages - 1, currentPage + 1);
                      setCurrentPage(newPage);
                      fetchAcceptanceData(selectedProject.id, {
                        searchQuery,
                        startTime: searchStartTime,
                        endTime: searchEndTime
                      });
                    }}
                    disabled={currentPage >= totalPages - 1}
                    className="px-3 py-1 text-sm"
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目查看测试计划</p>
            </div>
          </div>
        )}



        {showViewModal && viewingPlan && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">查看验收</h3>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setViewingPlan(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}


                  {/* 验收名称和负责人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                      <div className="text-gray-900">{selectedProject?.name}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收名称</label>
                      <div className="text-gray-900">{viewingPlan.name}</div>
                    </div>
                  </div>

                  {/* 验收人员和验收时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                      <div className="text-gray-900">{viewingPlan.responseName}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收人员</label>
                      <div className="text-gray-900">{viewingPlan.memberNames.join(', ')}</div>
                    </div>
                  </div>

                  {/* 验收标准和验收建议 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收时间</label>
                      <div className="text-gray-900">{formatDateTime(viewingPlan.startTime)}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收标准</label>
                      <div className="text-gray-900 whitespace-pre-wrap">{viewingPlan.standard}</div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">验收建议</label>
                    <div className="text-gray-900 whitespace-pre-wrap">{viewingPlan.advice || '-'}</div>
                  </div>
                  {/* 附件列表 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">附件</label>
                    {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 ? (
                      <div className="space-y-2">
                        {viewingPlan.projectFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center">
                              <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-600">{file.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handlePreviewFile(file.name)}
                                className="text-blue-500 hover:text-blue-700"
                                title="预览文件"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDownloadFile(file.name)}
                                className="text-blue-500 hover:text-blue-700"
                                title="下载文件"
                              >
                                <DownloadIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-gray-500">暂无附件</div>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowViewModal(false);
                    setViewingPlan(null);
                  }}
                >
                  关闭
                </Button>
              </div>
            </div>
          </div>
        )}





        {/* 创建验收弹窗 */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">添加产品验收</h3>
                <button
                  onClick={() => setShowNewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      readOnly
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>

                  {/* 验收名称和负责人 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收名称 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newAcceptance.acceptanceName}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            acceptanceName: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceName: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceName ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收名称"
                      />
                      {formErrors.acceptanceName && (
                        <div className="text-red-500 text-sm mt-1">
                          {formErrors.acceptanceName}
                        </div>
                      )}
                    </div>

                    {/* 负责人 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative manager-dropdown-container">
                        <div
                          onClick={() => setIsManagerDropdownOpen(!isManagerDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newAcceptance.manager ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          } ${formErrors.manager ? 'border-red-500' : ''}`}
                        >
                          <span className={newAcceptance.manager ? 'text-gray-900' : 'text-gray-400'}>
                            {newAcceptance.manager || '请选择负责人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isManagerDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isManagerDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(employee => (
                                <div
                                  key={employee.id}
                                  onClick={() => {
                                    setNewAcceptance({
                                      ...newAcceptance,
                                      manager: employee.name
                                    });
                                    setFormErrors({
                                      ...formErrors,
                                      manager: ''
                                    });
                                    setIsManagerDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    newAcceptance.manager === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {employee.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.manager && (
                        <div className="text-red-500 text-sm mt-1">
                          {formErrors.manager}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 验收人员和验收时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收人员 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收人员 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative acceptor-select-container">
                        <div
                          className={`min-h-[38px] px-3 py-2 border rounded-lg focus:outline-none cursor-pointer ${formErrors.acceptors ? 'border-red-500' : 'border-gray-300'
                            }`}
                          onClick={() => setShowAcceptorSelect(!showAcceptorSelect)}
                        >
                          {newAcceptance.memberNames && newAcceptance.memberNames.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {newAcceptance.memberNames.map((name, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-sm flex items-center gap-1"
                                >
                                  {name}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newMemberNames = [...newAcceptance.memberNames];
                                      const newMemberIds = [...newAcceptance.memberIds];
                                      newMemberNames.splice(index, 1);
                                      newMemberIds.splice(index, 1);
                                      setNewAcceptance({
                                        ...newAcceptance,
                                        memberNames: newMemberNames,
                                        memberIds: newMemberIds
                                      });
                                    }}
                                    className="hover:text-blue-900"
                                  >
                                    <Cross2Icon className="w-3 h-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择验收人员</span>
                          )}
                        </div>

                        {formErrors.acceptors && (
                          <div className="text-red-500 text-sm mt-1">{formErrors.acceptors}</div>
                        )}

                        {showAcceptorSelect && (
                          <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border rounded-lg shadow-lg">
                            <div className="p-2">
                              <div className="relative">
                                <input
                                  type="text"
                                  value={acceptorSearchQuery}
                                  onChange={(e) => setAcceptorSearchQuery(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none"
                                  placeholder="搜索验收人员..."
                                />
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                              </div>
                              <div className="mt-2 max-h-[200px] overflow-y-auto">
                                {employees
                                  .filter(emp => emp.name.toLowerCase().includes(acceptorSearchQuery.toLowerCase()))
                                  .map(emp => (
                                    <div
                                      key={emp.id}
                                      className={`p-2 cursor-pointer hover:bg-gray-50 rounded-lg flex items-center justify-between ${(newAcceptance.memberIds || []).includes(emp.id) ? 'bg-blue-50' : ''
                                        }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = (newAcceptance.memberIds || []).includes(emp.id);
                                        let newMemberIds = [...(newAcceptance.memberIds || [])];
                                        let newMemberNames = [...(newAcceptance.memberNames || [])];

                                        if (isSelected) {
                                          newMemberIds = newMemberIds.filter(id => id !== emp.id);
                                          newMemberNames = newMemberNames.filter(name => name !== emp.name);
                                        } else {
                                          newMemberIds.push(emp.id);
                                          newMemberNames.push(emp.name);
                                        }

                                        setNewAcceptance({
                                          ...newAcceptance,
                                          memberIds: newMemberIds,
                                          memberNames: newMemberNames
                                        });

                                        if (newMemberIds.length > 0) {
                                          setFormErrors(prev => ({
                                            ...prev,
                                            acceptors: ''
                                          }));
                                        }
                                      }}
                                    >
                                      <span className="text-sm text-gray-900">{emp.name}</span>
                                      {(newAcceptance.memberIds || []).includes(emp.id) && (
                                        <CheckIcon className="w-4 h-4 text-blue-500" />
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 验收时间 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={newAcceptance.acceptanceTime}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            acceptanceTime: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1" // 添加 step="1" 以支持精确到秒
                      />
                      {formErrors.acceptanceTime && (
                        <p className="text-red-500 text-sm mt-1">{formErrors.acceptanceTime}</p>
                      )}
                    </div>
                  </div>

                  {/* 验收标准和验收建议 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收标准 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收标准 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={newAcceptance.standard}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            standard: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              standard: ''
                            }));
                          }
                        }}
                        rows="2"
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.standard ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收标准"
                      />
                      {formErrors.standard && (
                        <p className="text-red-500 text-sm mt-1">{formErrors.standard}</p>
                      )}
                    </div>

                    {/* 验收建议 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收建议
                      </label>
                      <textarea
                        value={newAcceptance.suggestion}
                        onChange={(e) => setNewAcceptance({
                          ...newAcceptance,
                          suggestion: e.target.value
                        })}
                        rows="2"
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                        placeholder="请输入验收建议"
                      />
                    </div>
                  </div>

                  {/* 附件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <label
                      htmlFor="file-upload"
                      className="flex flex-col items-center justify-center w-full h-[120px] border border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 bg-white"
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg
                          className="w-8 h-8 mb-2 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                          />
                        </svg>
                        <p className="text-sm text-blue-500">点击上传文件</p>
                        <p className="text-xs text-gray-500">或拖拽文件到这里</p>
                        <p className="text-xs text-gray-400">支持任意文件类型</p>
                      </div>
                      <input
                        id="file-upload"
                        type="file"
                        className="hidden"
                        multiple
                        onChange={handleFileUpload}
                      />
                    </label>

                    {/* 已上传文件列表 */}
                    {newAcceptance.files.length > 0 && (
                      <div className="mt-4">
                        <div className="h-[100px] overflow-y-auto">
                          {newAcceptance.files.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-2"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <button
                                onClick={() => handleFileDelete(index)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNewModal(false)}
                >
                  取消
                </Button>
                <Button onClick={handleCreateAcceptance} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                  创建
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 修改弹窗 */}
        {showEditModal && editingAcceptance && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">修改验收</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingAcceptance(null);
                    setFormErrors({});
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input
                      type="text"
                      value={selectedProject?.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>

                  {/* 验收名称和负责人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={editingAcceptance.name}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            name: e.target.value
                          });
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceName: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceName ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收名称"
                      />
                      {formErrors.acceptanceName && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.acceptanceName}</div>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative edit-manager-dropdown-container">
                        <div
                          onClick={() => setIsEditManagerDropdownOpen(!isEditManagerDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !editingAcceptance.responseName ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          } ${formErrors.manager ? 'border-red-500' : ''}`}
                        >
                          <span className={editingAcceptance.responseName ? 'text-gray-900' : 'text-gray-400'}>
                            {editingAcceptance.responseName || '请选择负责人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditManagerDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isEditManagerDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(employee => (
                                <div
                                  key={employee.id}
                                  onClick={() => {
                                    setEditingAcceptance({
                                      ...editingAcceptance,
                                      responseName: employee.name
                                    });
                                    setFormErrors({
                                      ...formErrors,
                                      manager: ''
                                    });
                                    setIsEditManagerDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    editingAcceptance.responseName === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {employee.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.manager && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.manager}</div>
                      )}
                    </div>
                  </div>

                  {/* 验收人员和验收时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收人员选择部分 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收人员 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative acceptor-select-container">
                        <div
                          className={`min-h-[38px] px-3 py-2 border rounded-lg focus:outline-none cursor-pointer ${formErrors.memberIds ? 'border-red-500' : 'border-gray-300'
                            }`}
                          onClick={() => setShowAcceptorSelect(!showAcceptorSelect)}
                        >
                          {editingAcceptance.memberNames && editingAcceptance.memberNames.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {editingAcceptance.memberNames.map((name, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-sm flex items-center gap-1"
                                >
                                  {name}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newMemberNames = [...editingAcceptance.memberNames];
                                      const newMemberIds = [...editingAcceptance.memberIds];
                                      newMemberNames.splice(index, 1);
                                      newMemberIds.splice(index, 1);
                                      setEditingAcceptance(prev => ({
                                        ...prev,
                                        memberNames: newMemberNames,
                                        memberIds: newMemberIds
                                      }));
                                    }}
                                    className="hover:text-blue-900"
                                  >
                                    <Cross2Icon className="w-3 h-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择验收人员</span>
                          )}
                        </div>

                        {formErrors.acceptors && (
                          <div className="text-red-500 text-sm mt-1">{formErrors.acceptors}</div>
                        )}

                        {showAcceptorSelect && (
                          <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border rounded-lg shadow-lg">
                            <div className="p-2">
                              <div className="relative">
                                <input
                                  type="text"
                                  value={acceptorSearchQuery}
                                  onChange={(e) => setAcceptorSearchQuery(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none"
                                  placeholder="搜索验收人员..."
                                />
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                              </div>
                              <div className="mt-2 max-h-[200px] overflow-y-auto">
                                {employees
                                  .filter(emp => emp.name.toLowerCase().includes(acceptorSearchQuery.toLowerCase()))
                                  .map(emp => (
                                    <div
                                      key={emp.id}
                                      className={`p-2 cursor-pointer hover:bg-gray-50 rounded-lg flex items-center justify-between ${(editingAcceptance?.memberIds || []).includes(emp.id) ? 'bg-blue-50' : ''
                                        }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = (editingAcceptance?.memberIds || []).includes(emp.id);
                                        let newMemberIds = [...(editingAcceptance?.memberIds || [])];
                                        let newMemberNames = [...(editingAcceptance?.memberNames || [])];

                                        if (isSelected) {
                                          newMemberIds = newMemberIds.filter(id => id !== emp.id);
                                          newMemberNames = newMemberNames.filter(name => name !== emp.name);
                                        } else {
                                          newMemberIds.push(emp.id);
                                          newMemberNames.push(emp.name);
                                        }

                                        setEditingAcceptance(prev => ({
                                          ...prev,
                                          memberIds: newMemberIds,
                                          memberNames: newMemberNames
                                        }));

                                        if (newMemberIds.length > 0) {
                                          setFormErrors(prev => ({
                                            ...prev,
                                            acceptors: ''
                                          }));
                                        }
                                      }}
                                    >
                                      <span className="text-sm text-gray-900">{emp.name}</span>
                                      {(editingAcceptance?.memberIds || []).includes(emp.id) && (
                                        <CheckIcon className="w-4 h-4 text-blue-500" />
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 验收时间 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={editingAcceptance.acceptanceTime}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            acceptanceTime: e.target.value
                          });
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {formErrors.acceptanceTime && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.acceptanceTime}</div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收结束时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={editingAcceptance.endAcceptanceTime}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            endAcceptanceTime: e.target.value
                          });
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              endAcceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.endAcceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {formErrors.endAcceptanceTime && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.endAcceptanceTime}</div>
                      )}
                    </div>

                    {/* 验收状态 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收状态 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative status-dropdown-container">
                        <div
                          onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            editingAcceptance.result === undefined || editingAcceptance.result === '' ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingAcceptance.result !== undefined && editingAcceptance.result !== '' ? 'text-gray-900' : 'text-gray-400'}>
                            {editingAcceptance.result !== undefined && editingAcceptance.result !== '' 
                              ? acceptanceStatusOptions.find(option => option.value === editingAcceptance.result)?.label 
                              : '请选择验收状态'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isStatusDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isStatusDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {acceptanceStatusOptions.map(option => (
                                <div
                                  key={option.value}
                                  onClick={() => {
                                    setEditingAcceptance({
                                      ...editingAcceptance,
                                      result: option.value
                                    });
                                    setIsStatusDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    editingAcceptance.result === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {option.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 验收标准和验收建议 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收标准 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收标准 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={editingAcceptance.standard}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            standard: e.target.value
                          });
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              standard: ''
                            }));
                          }
                        }}
                        rows="1"
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.standard ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收标准"
                      />
                      {formErrors.standard && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.standard}</div>
                      )}
                    </div>

                    {/* 验收建议 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收建议</label>
                      <textarea
                        value={editingAcceptance.advice}
                        onChange={(e) => setEditingAcceptance({
                          ...editingAcceptance,
                          advice: e.target.value
                        })}
                        rows="1"
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                        placeholder="请输入验收建议"
                      />
                    </div>
                  </div>



                  {/* 附件 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <div className="space-y-2">
                      {/* 已上传的文件列表 */}
                      {editingAcceptance.projectFiles && editingAcceptance.projectFiles.length > 0 && (
                        <div className="max-h-[100px] overflow-y-auto space-y-2 pr-2">
                          {editingAcceptance.projectFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={() => handlePreviewFile(file.name)}
                                  className="text-blue-500 hover:text-blue-700"
                                  title="预览文件"
                                >
                                  <EyeOpenIcon className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    setEditingAcceptance(prev => ({
                                      ...prev,
                                      projectFiles: prev.projectFiles.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="text-red-500 hover:text-red-700"
                                  title="删除文件"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* 文件上传按钮 */}
                      <label
                        htmlFor="edit-file-upload"
                        className="flex flex-col items-center justify-center w-full h-[120px] border border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 bg-white"
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg
                            className="w-8 h-8 mb-2 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                            />
                          </svg>
                          <p className="text-sm text-blue-500">点击上传文件</p>
                          <p className="text-xs text-gray-500">或拖拽文件到这里</p>
                          <p className="text-xs text-gray-400">支持任意文件类型</p>
                        </div>
                        <input
                          id="edit-file-upload"
                          type="file"
                          className="hidden"
                          multiple
                          onChange={(e) => {
                            const newFiles = Array.from(e.target.files || []);
                            setEditingAcceptance(prev => ({
                              ...prev,
                              files: [...(prev.files || []), ...newFiles]
                            }));
                          }}
                        />
                      </label>

                      {/* 新上传的文件列表 */}
                      {editingAcceptance.files && editingAcceptance.files.length > 0 && (
                        <div className="space-y-2">
                          {editingAcceptance.files.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <button
                                onClick={() => {
                                  setEditingAcceptance(prev => ({
                                    ...prev,
                                    files: prev.files.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="text-red-500 hover:text-red-700"
                                title="删除文件"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingAcceptance(null);
                    setFormErrors({});
                  }}
                >
                  取消
                </Button>
                <Button onClick={handleUpdateAcceptance} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                  保存
                </Button>
              </div>
            </div>
          </div>
        )}

        {showDeleteAcceptanceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-[400px]">
              <h3 className="text-lg font-medium mb-4">确认删除</h3>
              <p className="text-gray-600 mb-6">
                确定要删除验收 &ldquo;{deletingAcceptance?.name}&rdquo; 吗？此操作不可恢复。
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteAcceptanceModal(false);
                    setDeletingAcceptance(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  style={{ backgroundColor: '#dc2626', color: 'white' }}
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

export default ProductAcceptance;