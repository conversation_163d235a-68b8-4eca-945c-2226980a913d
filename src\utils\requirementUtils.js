// 需求类型映射
export const categoryMap = { 'user': 0, 'project': 1, 'functional': 2, 'quality': 3 };
export const reverseCategoryMap = { 0: 'user', 1: 'project', 2: 'functional', 3: 'quality' };

// 功能分组映射
export const groupMap = { 'core': 0, 'business': 1, 'system': 2, 'platform': 3 };
export const reverseGroupMap = { 0: 'core', 1: 'business', 2: 'system', 3: 'platform' };

// 优先级映射
export const priorityMap = { 'low': 0, 'medium': 1, 'high': 2 };
export const reversePriorityMap = { 0: 'low', 1: 'medium', 2: 'high' };

// 格式化需求数据
export const formatRequirementData = (data) => ({
  ...data,
  category: reverseCategoryMap[data.category] || '',
  group: reverseGroupMap[data.group] || '',
  priority: reversePriorityMap[data.priority] || ''
});

// 格式化需求列表数据
export const formatRequirementList = (dataList) => {
  return dataList.map(item => formatRequirementData(item));
};

// 转换需求数据为API格式
export const convertToApiFormat = (data) => ({
  ...data,
  category: categoryMap[data.category],
  group: groupMap[data.group],
  priority: priorityMap[data.priority]
}); 