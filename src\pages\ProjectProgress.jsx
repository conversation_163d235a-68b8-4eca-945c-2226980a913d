import { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronDownIcon,
  ChevronRightIcon,
  DragHandleDots2Icon,
  CalendarIcon,
  PersonIcon,
  ExclamationTriangleIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { projectApi, taskApi, employeeApi } from '../services/projectProgressService';

/**
 * 项目进度管理页面
 * 主要功能：
 * 1. 项目列表展示和切换
 * 2. 任务树形结构展示和管理
 * 3. 任务的增删改查操作
 * 4. 任务拖拽排序功能
 * 5. 任务进度跟踪和状态管理
 */

// 任务状态映射
const taskStatuses = [
  { id: 0, name: '未开始', color: 'bg-gray-100 text-gray-800' },
  { id: 1, name: '进行中', color: 'bg-blue-100 text-blue-800' },
  { id: 2, name: '已完成', color: 'bg-green-100 text-green-800' },
  { id: 3, name: '延期', color: 'bg-red-100 text-red-800' }
];

// 项目状态映射
const projectStatusMap = {
  0: { name: '未开始', color: 'bg-gray-100 text-gray-800' },
  1: { name: '进行中', color: 'bg-blue-100 text-blue-800' },
  2: { name: '已结束', color: 'bg-green-100 text-green-800' }
};

export const ProjectProgress = observer(() => {
  // 基础状态
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [projects, setProjects] = useState([]);
  const [projectLoading, setProjectLoading] = useState(false);

  // 任务相关状态
  const [tasks, setTasks] = useState([]);
  const [taskLoading, setTaskLoading] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState({});
  const [parentTaskOptions, setParentTaskOptions] = useState([]);

  // 模态框状态
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showNewTaskModal, setShowNewTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [selectedParentTask, setSelectedParentTask] = useState(null);

  // 表单数据
  const [newTask, setNewTask] = useState({
    name: '',
    startDate: '',
    endDate: '',
    employeeId: '',
    employeeName: '',
    parentId: '',
    progress: 0
  });
  const [formErrors, setFormErrors] = useState({
    name: false,
    startDate: false,
    endDate: false,
    employeeId: false
  });

  // 员工相关状态
  const [employees, setEmployees] = useState([]);
  const [employeesLoading, setEmployeesLoading] = useState(false);
  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);

  // 拖拽状态
  const [draggedTask, setDraggedTask] = useState(null);
  const [dragOverTask, setDragOverTask] = useState(null);

  // UI状态
  const [errorMessage, setErrorMessage] = useState('');
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);

  // DOM引用
  const employeeDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);

  // 显示错误消息
  const showError = useCallback((message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 5000);
  }, []);

  // 重置表单错误状态
  const resetFormErrors = useCallback(() => {
    setFormErrors({
      name: false,
      startDate: false,
      endDate: false,
      employeeId: false
    });
  }, []);

  // 获取员工列表
  const fetchEmployees = useCallback(async () => {
    setEmployeesLoading(true);
    try {
      const data = await employeeApi.getEmployeeList();
      setEmployees(data);
    } catch (err) {
      console.error('获取员工列表错误:', err);
      showError('获取员工列表失败');
    } finally {
      setEmployeesLoading(false);
    }
  }, [showError]);

  // 切换任务展开/收起状态
  const toggleTask = (taskId) => {
    setExpandedTasks(prev => ({
      ...prev,
      [taskId]: !prev[taskId]
    }));
  };

  // 编辑任务
  const handleEditTask = async (task) => {
    setTaskLoading(true);
    try {
      const [taskData, parentTask] = await Promise.all([
        taskApi.getTaskDetail(task.id),
        task.parentId ? taskApi.getTaskDetail(task.parentId) : Promise.resolve(null)
      ]);

      setSelectedTask({
        id: taskData.id,
        name: taskData.name,
        progress: taskData.progress,
        status: taskData.status,
        startDate: taskData.startDate,
        endDate: taskData.endDate,
        employeeId: taskData.employeeId,
        employeeName: taskData.employeeName,
        projectId: taskData.projectId,
        parentId: taskData.parentId || null,
        parentTaskName: parentTask ? parentTask.name : ''
      });
      setShowTaskModal(true);
    } catch (err) {
      console.error('获取任务详情错误:', err);
      showError(err.message);
    } finally {
      setTaskLoading(false);
    }
  };

  // 表单验证
  const validateForm = (data) => {
    const errors = {
      name: !data.name,
      startDate: !data.startDate,
      endDate: !data.endDate,
      employeeId: !data.employeeId
    };
    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  // 保存任务编辑
  const handleSaveEdit = async () => {
    if (!validateForm(selectedTask)) return;

    try {
      const taskData = {
        id: selectedTask.id,
        name: selectedTask.name,
        progress: selectedTask.progress || 0,
        status: selectedTask.status || 0,
        startDate: selectedTask.startDate,
        endDate: selectedTask.endDate,
        employeeId: parseInt(selectedTask.employeeId),
        projectId: selectedProject.id,
        parentId: selectedTask.parentId || null
      };

      await taskApi.updateTask(selectedTask.id, taskData);
      await fetchTasks(selectedProject.id);
      setShowTaskModal(false);
      setSelectedTask(null);
    } catch (err) {
      console.error('更新任务错误:', err);
      showError(err.message);
    }
  };

  // 重置新任务表单
  const resetNewTaskForm = () => {
    setNewTask({
      name: '',
      startDate: '',
      endDate: '',
      employeeId: '',
      employeeName: '',
      parentId: '',
      progress: 0
    });
    setFormErrors({
      name: false,
      startDate: false,
      endDate: false,
      employeeId: false
    });
  };

  // 添加子任务
  const handleAddSubtask = (parentTask) => {
    setSelectedParentTask(parentTask);
    setNewTask({
      ...newTask,
      parentId: parentTask.id.toString()
    });
    setShowNewTaskModal(true);
  };

  // 添加新任务
  const handleAddTask = async () => {
    resetNewTaskForm();
    setSelectedParentTask(null);
    setShowNewTaskModal(true);

    if (selectedProject) {
      const tasks = await fetchTasks(selectedProject.id);
      setParentTaskOptions(tasks || []);
    }
  };

  // 创建任务
  const handleCreateTask = async () => {
    if (!validateForm(newTask)) return;

    try {
      const taskData = {
        id: 0,
        name: newTask.name,
        progress: newTask.progress,
        status: 0,
        startDate: newTask.startDate,
        endDate: newTask.endDate,
        employeeId: parseInt(newTask.employeeId),
        projectId: selectedProject.id,
        employeeName: newTask.employeeName,
        parentId: newTask.parentId ? parseInt(newTask.parentId) : null
      };

      await taskApi.createTask(taskData);
      fetchTasks(selectedProject.id);
      setShowNewTaskModal(false);
      resetNewTaskForm();
    } catch (err) {
      console.error('创建任务错误:', err);
      showError(err.message);
    }
  };

  // 递归设置任务展开状态
  const setTasksExpandedState = (taskList, expanded) => {
    const newExpandedTasks = {};
    const processTask = (task) => {
      newExpandedTasks[task.id] = expanded;
      if (task.subtasks && task.subtasks.length > 0) {
        task.subtasks.forEach(processTask);
      }
    };
    taskList.forEach(processTask);
    return newExpandedTasks;
  };

  // 展开所有任务
  const handleExpandAll = () => {
    setExpandedTasks(setTasksExpandedState(tasks, true));
  };

  // 收起所有任务
  const handleCollapseAll = () => {
    setExpandedTasks(setTasksExpandedState(tasks, false));
  };



  // 获取任务列表
  const fetchTasks = useCallback(async (projectId) => {
    setTaskLoading(true);
    try {
      const data = await taskApi.getTasksByProject(projectId);

      // 处理任务数据结构，将children重命名为subtasks
      const processTaskData = (task) => ({
        ...task,
        subtasks: task.children ? task.children.map(child => processTaskData(child)) : []
      });

      const treeData = data.map(task => processTaskData(task));
      setTasks(treeData);
      return treeData;
    } catch (err) {
      console.error('获取任务列表错误:', err);
      showError('获取任务列表失败');
      return [];
    } finally {
      setTaskLoading(false);
    }
  }, [showError]);

  // 获取项目列表
  const fetchProjects = useCallback(async () => {
    setProjectLoading(true);
    try {
      const data = await projectApi.getProjectList();
      setProjects(data);

      if (data.length > 0) {
        setSelectedProject(data[0]);
        fetchTasks(data[0].id);
      }
    } catch (err) {
      console.error('获取项目列表错误:', err);
      showError('获取项目列表失败');
    } finally {
      setProjectLoading(false);
    }
  }, [showError, fetchTasks]);

  const handleProjectChange = async (e) => {
    const project = projects.find(p => p.id === parseInt(e.target.value));
    setSelectedProject(project);
    setSearchQuery(''); // 切换项目时清空搜索框

    if (project) {
      setTaskLoading(true);
      try {
        const data = await taskApi.getTasksByProject(project.id);

        // 修改数据处理逻辑，将 children 重命名为 subtasks
        const processTask = (task) => {
          return {
            ...task,
            subtasks: task.children ? task.children.map(child => processTask(child)) : []
          };
        };

        // 处理所有顶级任务
        const treeData = data.map(task => processTask(task));
        setTasks(treeData);
      } catch (err) {
        console.error('获取任务列表错误:', err);
        setErrorMessage(err.message);
        setTimeout(() => setErrorMessage(''), 5000);
      } finally {
        setTaskLoading(false);
      }
    } else {
      setTasks([]);
    }
  };

  // 搜索任务（带防抖）
  const searchTasks = useCallback(async (searchValue) => {
    if (!selectedProject) {
      showError('请先选择项目');
      return;
    }

    setTaskLoading(true);
    try {
      const data = await taskApi.getTasksByProject(selectedProject.id, searchValue);

      // 处理任务数据结构
      const processTask = (task) => ({
        ...task,
        subtasks: task.children ? task.children.map(child => processTask(child)) : []
      });

      const treeData = data.map(task => processTask(task));
      setTasks(treeData);
    } catch (err) {
      console.error('搜索任务错误:', err);
      showError(err.message);
    } finally {
      setTaskLoading(false);
    }
  }, [selectedProject, showError]);

  // 防抖搜索处理
  const searchTimeoutRef = useRef(null);
  const handleSearchChange = (value) => {
    setSearchQuery(value);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      searchTasks(value);
    }, 300);
  };

  // 修改拖拽处理函数
  const handleDrop = (e, targetTask) => {
    e.preventDefault();
    if (!draggedTask || draggedTask.id === targetTask.id) return;

    // 确保只能在同级之间拖动
    if (draggedTask.parentId !== targetTask.parentId) {
      return; // 如果不是同级任务，直接返回
    }

    // 创建新的任务列表副本
    const newTasks = [...tasks];

    // 递归查找并更新任务位置的函数
    const reorderTasks = (taskList) => {
      // 如果是顶层任务
      if (draggedTask.parentId === null) {
        const draggedIndex = taskList.findIndex(t => t.id === draggedTask.id);
        const targetIndex = taskList.findIndex(t => t.id === targetTask.id);
        if (draggedIndex !== -1 && targetIndex !== -1) {
          const [draggedItem] = taskList.splice(draggedIndex, 1);
          taskList.splice(targetIndex, 0, draggedItem);
          return taskList;
        }
      }

      // 如果是子任务，递归查找并更新
      return taskList.map(task => {
        if (task.subtasks && task.subtasks.length > 0) {
          const subtasks = [...task.subtasks];
          const draggedIndex = subtasks.findIndex(t => t.id === draggedTask.id);
          const targetIndex = subtasks.findIndex(t => t.id === targetTask.id);

          if (draggedIndex !== -1 && targetIndex !== -1) {
            const [draggedItem] = subtasks.splice(draggedIndex, 1);
            subtasks.splice(targetIndex, 0, draggedItem);
            return {
              ...task,
              subtasks
            };
          }

          return {
            ...task,
            subtasks: reorderTasks(task.subtasks)
          };
        }
        return task;
      });
    };

    // 执行重排序并更新状态
    const reorderedTasks = reorderTasks(newTasks);
    setTasks(reorderedTasks);

    // 重置拖拽状态
    setDraggedTask(null);
    setDragOverTask(null);
  };

  // 删除任务
  const handleDeleteTask = async (taskId) => {
    if (!window.confirm('确定要删除此任务吗？')) {
      return;
    }

    try {
      const result = await taskApi.deleteTask(taskId);
      if (result === true) {
        await fetchTasks(selectedProject.id);
      } else {
        showError('删除失败');
      }
    } catch (err) {
      console.error('删除任务错误:', err);
      showError(err.message);
    }
  };

  // 组件初始化
  useEffect(() => {
    fetchProjects();
    fetchEmployees();
  }, [fetchProjects, fetchEmployees]);

  // 添加点击外部关闭下拉选择的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (employeeDropdownRef.current && !employeeDropdownRef.current.contains(event.target)) {
        setEmployeeSearchResults([]);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const renderTask = (task, level = 0) => {
    const isExpanded = expandedTasks[task.id];
    const hasSubtasks = task.subtasks && task.subtasks.length > 0;
    const statusInfo = taskStatuses.find(s => s.id === task.status) || { name: '未知', color: 'bg-gray-100 text-gray-800' };
    const isDragging = draggedTask?.id === task.id;
    const isDragOver = dragOverTask?.id === task.id;
    const canDrop = draggedTask && draggedTask.parentId === task.parentId;

    return (
      <div key={task.id}>
        <div
          className={`flex items-center p-4 hover:bg-gray-50 transition-all duration-200
            ${isDragging ? 'opacity-50 bg-gray-50' : ''}
            ${isDragOver && canDrop ? 'border-t-2 border-blue-500' : ''}`}
          style={{
            paddingLeft: `${level * 2 + 1}rem`,
            borderLeft: level > 0 ? '2px solid #e5e7eb' : 'none',
            position: 'relative'
          }}
          draggable={true}
          onDragStart={(e) => {
            e.stopPropagation();
            setDraggedTask(task);
          }}
          onDragOver={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (canDrop) {
              setDragOverTask(task);
            }
          }}
          onDrop={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleDrop(e, task);
          }}
          onDragEnd={() => {
            setDraggedTask(null);
            setDragOverTask(null);
          }}
        >
          {/* 添加连接线 */}
          {level > 0 && (
            <div
              className="absolute w-4 h-px bg-gray-200"
              style={{
                left: `${level * 2}rem`,
                top: '50%',
                transform: 'translateY(-50%)'
              }}
            />
          )}

          <div className="flex items-center gap-4 flex-1">
            <DragHandleDots2Icon
              className="w-5 h-5 text-gray-400 cursor-move"
              style={{ cursor: 'grab' }}
            />
            {hasSubtasks && (
              <button
                className="p-1 hover:bg-gray-200 rounded"
                onClick={() => toggleTask(task.id)}
              >
                {isExpanded ? (
                  <ChevronDownIcon className="w-4 h-4" />
                ) : (
                  <ChevronRightIcon className="w-4 h-4" />
                )}
              </button>
            )}
            {!hasSubtasks && <div className="w-6" />}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{task.name}</span>
              </div>
              <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <CalendarIcon className="w-4 h-4" />
                  {task.startDate} 至 {task.endDate}
                </div>
                <div className="flex items-center gap-1">
                  <PersonIcon className="w-4 h-4" />
                  {task.employeeName}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-6">
              <div className="w-48">
                <div className="flex justify-between text-sm mb-1">
                  <span>进度</span>
                  <span>{task.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${task.progress}%` }}
                  ></div>
                </div>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs ${statusInfo.color}`}>
                {statusInfo.name}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditTask(task)}
                  className="text-blue-500 hover:text-blue-700"
                >
                  编辑
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleAddSubtask(task)}
                  className="text-green-500 hover:text-green-700"
                >
                  添加子任务
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteTask(task.id)}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        </div>
        {isExpanded && hasSubtasks && (
          <div className="relative">
            {task.subtasks.map(subtask => renderTask(subtask, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // 添加一个错误提示组件
  const ErrorMessage = ({ message, onClose }) => {
    if (!message) return null;

    return (
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[100]">
        <div className="bg-white rounded-lg shadow-lg border border-red-100 px-4 py-3 flex items-center gap-2">
          <ExclamationTriangleIcon className="w-4 h-4 text-red-500" />
          <span className="text-gray-700">{message}</span>
          <button
            onClick={onClose}
            className="ml-2 p-1 hover:bg-gray-100 rounded-full"
          >
            <Cross2Icon className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>
    );
  };



  return (
    <div className="flex-1 pt-16 flex bg-gray-50" style={{ height: 'calc(100vh - 40px)' }}>
      {/* 全局错误提示组件 */}
      <ErrorMessage
        message={errorMessage}
        onClose={() => setErrorMessage('')}
      />

      {/* 左侧项目列表面板 */}
      <div className="w-[220px] border-r bg-white">
        <div className="px-6 py-6">
          <h2 className="text-lg font-medium mb-4">项目列表</h2>
          {/* 项目搜索框 */}
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-md bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
              onChange={async (e) => {
                const value = e.target.value;
                setProjectLoading(true);
                try {
                  const data = await projectApi.getProjectList(value);
                  setProjects(data);
                } catch (err) {
                  console.error('搜索项目错误:', err);
                } finally {
                  setProjectLoading(false);
                }
              }}
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
          {projectLoading ? (
            <div className="text-center text-gray-500 py-4">加载中...</div>
          ) : (
            <div className="space-y-0.5">
              {projects.map(project => (
                <button
                  key={project.id}
                  onClick={() => handleProjectChange({ target: { value: project.id } })}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors duration-200
                    ${selectedProject?.id === project.id
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-50'
                    }`}
                >
                  <div>{project.name}</div>
                  <div className={`mt-1 inline-block px-2 py-0.5 rounded-full text-xs ${projectStatusMap[project.status]?.color || 'bg-gray-100 text-gray-800'}`}>
                    {projectStatusMap[project.status]?.name || '未知状态'}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 右侧任务管理区域 */}
      <div className="flex-1 px-8 py-6 overflow-y-auto">
        {selectedProject ? (
          <div className="bg-white rounded-md border">
            {/* 任务操作工具栏 */}
            <div className="p-4 border-b">
              <div className="flex justify-between items-center">
                {/* 任务搜索框 */}
                <div className="flex gap-2 items-center">
                  <div className="relative w-64">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => {
                        handleSearchChange(e.target.value);
                      }}
                      placeholder="搜索任务..."
                      className="w-full pl-8 pr-4 py-2 border rounded-md bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                    <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                  </div>
                </div>
                {/* 操作按钮组 */}
                <div className="flex gap-2">
                  <Button
                    className="flex items-center gap-1"
                    onClick={handleAddTask}
                  >
                    <PlusIcon className="w-4 h-4" />
                    新建任务
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleExpandAll}
                  >
                    展开全部
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCollapseAll}
                  >
                    收起全部
                  </Button>
                </div>
              </div>
            </div>
            {/* 任务列表内容区域 */}
            <div className="divide-y">
              {taskLoading ? (
                <div className="p-4 text-center text-gray-500">
                  加载任务中...
                </div>
              ) : tasks.length > 0 ? (
                tasks.map(task => renderTask(task))
              ) : (
                <div className="p-4 text-center text-gray-500">
                  暂无任务数据
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 mt-8">
            请选择要管理的项目
          </div>
        )}
      </div>

      {/* 任务编辑模态框 */}
      {showTaskModal && selectedTask && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            {taskLoading ? (
              <div className="p-6 text-center">
                <div className="text-gray-500">加载任务数据中...</div>
              </div>
            ) : (
              <div>
                <div className="p-6 border-b flex justify-between items-center">
                  <h3 className="text-xl font-semibold">编辑任务</h3>
                  <button
                    onClick={() => setShowTaskModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-lg"
                  >
                    <Cross2Icon className="w-4 h-4" />
                  </button>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        任务名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={selectedTask.name}
                        onChange={(e) => setSelectedTask({
                          ...selectedTask,
                          name: e.target.value
                        })}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={employeeDropdownRef}>
                        <div
                          onClick={() => !employeesLoading && setEmployeeSearchResults(employees)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !selectedTask.employeeId ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          } ${employeesLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span className={selectedTask.employeeId ? 'text-gray-900' : 'text-gray-400'}>
                            {employeesLoading ? '加载中...' : (selectedTask.employeeName || '请选择负责人')}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${employeeSearchResults.length > 0 ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {employeeSearchResults.length > 0 && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employeeSearchResults.map(employee => (
                                <div
                                  key={employee.id}
                                  onClick={() => {
                                    setSelectedTask({
                                      ...selectedTask,
                                      employeeId: employee.id.toString(),
                                      employeeName: employee.name
                                    });
                                    setEmployeeSearchResults([]);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    selectedTask.employeeId === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {employee.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        上级任务
                      </label>
                      <input
                        type="text"
                        value={selectedTask.parentTaskName || '无'}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        进度
                      </label>
                      <div className="flex items-center gap-4">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={selectedTask.progress}
                          onChange={(e) => setSelectedTask({
                            ...selectedTask,
                            progress: parseInt(e.target.value)
                          })}
                          className="flex-1"
                        />
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            min="0"
                            max="100"
                            value={selectedTask.progress}
                            onChange={(e) => {
                              const value = e.target.value;
                              const progress = Math.min(100, Math.max(0, parseInt(value) || 0));
                              setSelectedTask({
                                ...selectedTask,
                                progress: progress
                              });
                            }}
                            className="w-16 px-2 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                          />
                          <span className="text-gray-500">%</span>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm text-gray-500 mt-1">
                        <span>0%</span>
                        <span>100%</span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        状态
                      </label>
                      <div className="relative" ref={statusDropdownRef}>
                        <div
                          onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-blue-500"
                        >
                          <span className="text-gray-900">
                            {taskStatuses.find(s => s.id === selectedTask.status)?.name || '请选择状态'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isStatusDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isStatusDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {taskStatuses.map(status => (
                                <div
                                  key={status.id}
                                  onClick={() => {
                                    setSelectedTask({
                                      ...selectedTask,
                                      status: status.id
                                    });
                                    setIsStatusDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    selectedTask.status === status.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {status.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          开始日期 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="date"
                          value={selectedTask.startDate}
                          onChange={(e) => setSelectedTask({
                            ...selectedTask,
                            startDate: e.target.value
                          })}
                          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          结束日期 <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="date"
                          value={selectedTask.endDate}
                          onChange={(e) => setSelectedTask({
                            ...selectedTask,
                            endDate: e.target.value
                          })}
                          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                  </div>
                </div>
                <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowTaskModal(false);
                      setFormErrors({
                        name: false,
                        startDate: false,
                        endDate: false,
                        employeeId: false
                      });
                    }}
                  >
                    取消
                  </Button>
                  <Button onClick={handleSaveEdit}>
                    保存
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 新建任务模态框 */}
      {showNewTaskModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建任务</h3>
              <button
                onClick={() => {
                  setShowNewTaskModal(false);
                  resetNewTaskForm();
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 项目名称 - 不可修改 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>

                {/* 任务名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    任务名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={newTask.name}
                    onChange={(e) => {
                      setNewTask({ ...newTask, name: e.target.value });
                      setFormErrors({ ...formErrors, name: false });
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                      ${formErrors.name ? 'border-red-500 bg-red-50' : 'border-gray-300'}`}
                    placeholder="请输入任务名称"
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-sm text-red-500">请输入任务名称</p>
                  )}
                </div>

                {/* 上级任务 */}
                {selectedParentTask ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      上级任务
                    </label>
                    <input
                      type="text"
                      value={selectedParentTask.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                ) : parentTaskOptions.length > 0 ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      上级任务
                    </label>
                    <select
                      value={newTask.parentId}
                      onChange={(e) => setNewTask({ ...newTask, parentId: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">无上级任务</option>
                      {taskLoading ? (
                        <option disabled>加载中...</option>
                      ) : (
                        parentTaskOptions.map(task => (
                          <option key={task.id} value={task.id}>
                            {task.name}
                          </option>
                        ))
                      )}
                    </select>
                  </div>
                ) : null}

                {/* 负责人 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    负责人 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={newTask.employeeId}
                    onChange={(e) => {
                      const selectedEmployee = employees.find(emp => emp.id.toString() === e.target.value);
                      setNewTask({
                        ...newTask,
                        employeeId: e.target.value,
                        employeeName: selectedEmployee ? selectedEmployee.name : ''
                      });
                      setFormErrors({ ...formErrors, employeeId: false });
                    }}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择负责人</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* 添加进度输入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    进度
                  </label>
                  <div className="flex items-center gap-4">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={newTask.progress}
                      onChange={(e) => setNewTask({
                        ...newTask,
                        progress: parseInt(e.target.value)
                      })}
                      className="flex-1"
                    />
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={newTask.progress}
                        onChange={(e) => {
                          const value = e.target.value;
                          const progress = Math.min(100, Math.max(0, parseInt(value) || 0));
                          setNewTask({
                            ...newTask,
                            progress: progress
                          });
                        }}
                        className="w-16 px-2 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                      />
                      <span className="text-gray-500">%</span>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm text-gray-500 mt-1">
                    <span>0%</span>
                    <span>100%</span>
                  </div>
                </div>

                {/* 开始日期和结束日期 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始日期 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={newTask.startDate}
                      onChange={(e) => {
                        setNewTask({ ...newTask, startDate: e.target.value });
                        setFormErrors({ ...formErrors, startDate: false });
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                        ${formErrors.startDate ? 'border-red-500 bg-red-50' : 'border-gray-300'}`}
                    />
                    {formErrors.startDate && (
                      <p className="mt-1 text-sm text-red-500">请选择开始日期</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结束日期 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="date"
                      value={newTask.endDate}
                      onChange={(e) => {
                        setNewTask({ ...newTask, endDate: e.target.value });
                        setFormErrors({ ...formErrors, endDate: false });
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
                        ${formErrors.endDate ? 'border-red-500 bg-red-50' : 'border-gray-300'}`}
                    />
                    {formErrors.endDate && (
                      <p className="mt-1 text-sm text-red-500">请选择结束日期</p>
                    )}
                  </div>
                </div>

              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewTaskModal(false);
                  resetNewTaskForm();
                }}
              >
                取消
              </Button>
              <Button onClick={handleCreateTask}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});