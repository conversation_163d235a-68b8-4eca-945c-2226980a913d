/**
 * 产品交付管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 成果交付的增删改查
 * 3. 支持层级结构的子成果管理
 * 4. 文件上传和预览下载
 * 5. 分页显示和搜索过滤
 */

import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronRightIcon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  CheckIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import {
  projectApi,
  deliveryApi,
  fileApi,
  employeeApi
} from '../services/productDeliveryService';

// 错误提示组件
const ErrorMessage = ({ message }) => {
  return (
    <div className="absolute -top-16 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 shadow-lg">
      <div className="text-red-500">
        <Cross2Icon className="w-4 h-4" />
      </div>
      <div className="text-sm text-red-800">{message}</div>
    </div>
  );
};

// 成果交付类型选项
const deliveryTypeOptions = [
  { value: 0, label: '文件' },
  { value: 1, label: '软件源码' },
  { value: 2, label: '工具' },
  { value: 3, label: '硬件成果' }
];

// 交付状态选项
const deliveryStatusOptions = [
  { value: 0, label: '未交付' },
  { value: 1, label: '已交付' }
];

const ProductDelivery = observer(() => {
  // 基础状态
  const [projects, setProjects] = useState([]); // 项目列表
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [projectSearchQuery, setProjectSearchQuery] = useState(''); // 项目搜索关键词
  const [employees, setEmployees] = useState([]); // 员工列表
  const [errorMessage, setErrorMessage] = useState(''); // 错误提示信息

  // 成果交付相关状态
  const [deliveries, setDeliveries] = useState([]); // 成果交付列表
  const [searchQuery, setSearchQuery] = useState(''); // 搜索关键词
  const [searchDeliveryPerson, setSearchDeliveryPerson] = useState(''); // 交付人搜索
  const [currentPage, setCurrentPage] = useState(0); // 当前页码
  const [pageSize] = useState(10); // 每页大小
  const [totalPages, setTotalPages] = useState(0); // 总页数
  const [totalElements, setTotalElements] = useState(0); // 总记录数

  // 子成果相关状态
  const [childDeliveries, setChildDeliveries] = useState({}); // 子成果数据
  const [expandedDeliveries, setExpandedDeliveries] = useState(new Set()); // 展开的成果ID集合

  // 弹窗状态
  const [showCreateDeliveryModal, setShowCreateDeliveryModal] = useState(false); // 创建成果交付弹窗
  const [showViewDeliveryModal, setShowViewDeliveryModal] = useState(false); // 查看成果交付弹窗
  const [showEditDeliveryModal, setShowEditDeliveryModal] = useState(false); // 编辑成果交付弹窗
  const [showDeleteDeliveryModal, setShowDeleteDeliveryModal] = useState(false); // 删除确认弹窗
  const [showAddChildDeliveryModal, setShowAddChildDeliveryModal] = useState(false); // 添加子成果弹窗

  // 表单数据状态
  const [newDelivery, setNewDelivery] = useState({
    projectId: '',
    projectName: '',
    name: '',
    type: '',
    deliveryPerson: '',
    receiver: '',
    deliveryTime: '',
    parentDelivery: '',
    files: []
  });
  const [editingDelivery, setEditingDelivery] = useState(null); // 正在编辑的成果交付
  const [viewingDelivery, setViewingDelivery] = useState(null); // 正在查看的成果交付
  const [deletingDelivery, setDeletingDelivery] = useState(null); // 待删除的成果交付
  const [parentDelivery, setParentDelivery] = useState(null); // 父成果交付（用于添加子成果）

  // 表单验证错误状态
  const [deliveryErrors, setDeliveryErrors] = useState({
    name: '',
    type: '',
    deliveryPerson: '',
    receiver: '',
    deliveryTime: ''
  });

  // 下拉框状态
  const [isDeliveryTypeDropdownOpen, setIsDeliveryTypeDropdownOpen] = useState(false);
  const [isDeliveryPersonDropdownOpen, setIsDeliveryPersonDropdownOpen] = useState(false);
  const [isDeliveryStatusDropdownOpen, setIsDeliveryStatusDropdownOpen] = useState(false);

  // refs引用
  const deliveryTypeDropdownRef = useRef(null);
  const deliveryPersonDropdownRef = useRef(null);
  const deliveryStatusDropdownRef = useRef(null);

  // 错误提示处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => {
      setErrorMessage('');
    }, 2000);
  };

  // 获取项目列表数据
  const fetchProjects = async () => {
    try {
      const data = await projectApi.getProjects();
      setProjects(data);

      // 如果有项目数据且没有选中的项目，自动选择第一个项目
      if (data && data.length > 0 && !selectedProject) {
        const firstProject = data[0];
        setSelectedProject(firstProject);
        // 获取第一个项目的交付数据
        fetchDeliveryData(firstProject.id);
      }
    } catch {
      handleError('获取项目列表失败');
    }
  };



  // 项目选择处理函数
  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    // 重置搜索条件
    setSearchQuery('');
    setSearchDeliveryPerson('');
    setCurrentPage(0);
    // 获取新选中项目的交付数据
    fetchDeliveryData(project.id, 0);
  };

  // 搜索处理函数
  const handleSearch = () => {
    setCurrentPage(0);
    fetchDeliveryData(selectedProject.id, 0);
  };

  // 重置搜索条件
  const handleReset = () => {
    setSearchQuery('');
    setSearchDeliveryPerson('');
    setCurrentPage(0);
    fetchDeliveryData(selectedProject.id, 0);
  };

  // 获取成果交付列表数据
  const fetchDeliveryData = async (projectId, pageNum = 0, size = 10) => {
    try {
      const data = await deliveryApi.getDeliveryList(projectId, pageNum, size);
      setDeliveries(data || []);
      setTotalPages(Math.ceil(data.length / size));
      setTotalElements(data.length);
    } catch {
      handleError('获取交付数据失败');
    }
  };

  // 项目搜索处理函数
  const handleProjectSearch = async (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);

    try {
      if (searchValue.trim()) {
        const data = await projectApi.searchProjects(searchValue);
        setProjects(data);
      } else {
        // 如果搜索词为空，重新获取所有项目
        fetchProjects();
      }
    } catch {
      handleError('搜索项目失败');
    }
  };

  // 初始化时获取项目列表
  useEffect(() => {
    fetchProjects();
  }, []);





  // 文件预览处理函数
  const handlePreviewFile = async (file) => {
    try {
      const previewUrl = await fileApi.previewFile(file.name);
      if (previewUrl) {
        const newWindow = window.open(previewUrl, '_blank');
        if (newWindow === null) {
          throw new Error('无法打开新窗口，请检查浏览器是否阻止弹出窗口');
        }
      } else {
        handleError('获取预览链接失败');
      }
    } catch (error) {
      console.error('预览文件失败:', error);
      handleError(error.message || '预览文件失败');
    }
  };

  // 文件下载处理函数
  const handleDownloadFile = async (file) => {
    try {
      const blob = await fileApi.downloadFile(file.name);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch {
      handleError('下载文件失败');
    }
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      if (data) {
        setEmployees(data);
      }
    } catch {
      handleError('获取员工列表失败');
    }
  };

  // 在组件挂载时获取员工列表
  useEffect(() => {
    fetchEmployees();
  }, []);



  // 添加点击外部关闭下拉框的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (deliveryTypeDropdownRef.current && !deliveryTypeDropdownRef.current.contains(event.target)) {
        setIsDeliveryTypeDropdownOpen(false);
      }
      if (deliveryPersonDropdownRef.current && !deliveryPersonDropdownRef.current.contains(event.target)) {
        setIsDeliveryPersonDropdownOpen(false);
      }
      if (deliveryStatusDropdownRef.current && !deliveryStatusDropdownRef.current.contains(event.target)) {
        setIsDeliveryStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 添加日期格式化函数
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };






  // 修改添加按钮的点击处理函数
  const handleAddDelivery = () => {
    if (!selectedProject) {
      handleError('请先选择一个项目');
      return;
    }
    setNewDelivery({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      name: '',
      type: '',
      deliveryPerson: '',
      receiver: '',
      deliveryTime: '',
      parentDelivery: '',
      files: []
    });
    setDeliveryErrors({});
    setShowCreateDeliveryModal(true);
  };

  // 修改 handleCreateDelivery 函数中的验证和数据处理部分
  const handleCreateDelivery = async () => {
    try {
      // 表单验证
      const errors = {};
      if (!newDelivery.name) errors.name = '请输入成果交付名称';
      if (!newDelivery.type) errors.type = '请选择成果交付类型';
      if (!newDelivery.deliveryPerson) errors.deliveryPerson = '请选择交付人';
      if (!newDelivery.deliveryTime) errors.deliveryTime = '请选择交付时间';

      if (Object.keys(errors).length > 0) {
        setDeliveryErrors(errors);
        return;
      }

      // 获取选中的交付人信息
      const deliveryPerson = employees.find(emp => emp.name === newDelivery.deliveryPerson);

      // 创建成果交付的基本信息
      const deliverableData = {
        projectId: selectedProject.id,
        name: newDelivery.name,
        type: parseInt(newDelivery.type),
        status: 0, // 默认状态
        deliveredBy: deliveryPerson?.id || 0,
        receivedBy: newDelivery.receiver,
        deliveredTime: new Date(newDelivery.deliveryTime).toISOString(),
        createdTime: new Date().toISOString(),
        parentId: parentDelivery?.id || null, // 设置父成果ID
        fileIds: [],
        projectFiles: [],
        deliveredByName: newDelivery.deliveryPerson,
        receivedByName: newDelivery.receiver
      };

      // 创建 FormData 对象
      const formData = new FormData();

      // 将成果交付数据转换为JSON字符串并添加到FormData
      formData.append('deliverable', new Blob([JSON.stringify(deliverableData)], {
        type: 'application/json'
      }));

      // 添加文件到 FormData
      newDelivery.files.forEach(file => {
        formData.append('files', file);
      });

      // 调用服务层创建方法
      const response = await deliveryApi.createDelivery(formData);

      if (response) {
        // 重置表单和错误
        setNewDelivery({
          projectId: '',
          projectName: '',
          name: '',
          type: '',
          deliveryPerson: '',
          receiver: '',
          deliveryTime: '',
          parentId: '',
          parentName: '',
          files: []
        });
        setDeliveryErrors({});
        
        // 关闭弹窗
        setShowAddChildDeliveryModal(false);
        setShowCreateDeliveryModal(false);
        
        // 清空父成果信息
        setParentDelivery(null);
        
        // 如果是子成果，先刷新父成果的子成果列表
        if (deliverableData.parentId) {
          // 获取父成果的子成果列表
          const childData = await deliveryApi.getDeliveryList(
            selectedProject.id,
            0,
            10,
            deliverableData.parentId
          );
          
          if (childData) {
            setChildDeliveries(prev => ({
              ...prev,
              [deliverableData.parentId]: childData || []
            }));
          }
          
          // 确保父成果是展开状态
          setExpandedDeliveries(prev => new Set([...prev, deliverableData.parentId]));
        }
        
        // 刷新主列表数据（顶级成果）
        const mainData = await deliveryApi.getDeliveryList(
          selectedProject.id,
          currentPage,
          pageSize,
          0
        );
        
        if (mainData) {
          setDeliveries(mainData || []);
          setTotalPages(Math.ceil(mainData.length / pageSize));
          setTotalElements(mainData.length);
        }
        
        handleError('创建成功');
      }
    } catch (error) {
      console.error('创建成果交付失败:', error);
      handleError('创建成果交付失败');
    }
  };

  // 添加查看成果交付的处理函数
  const handleViewDelivery = async (delivery) => {
    try {
      const data = await deliveryApi.getDeliveryDetail(delivery.id);
      if (data) {
        setViewingDelivery(data);
        setShowViewDeliveryModal(true);
      }
    } catch (error) {
      console.error('获取成果交付详情失败:', error);
      handleError('获取成果交付详情失败');
    }
  };

  // 修改编辑成果交付的处理函数
  const handleEditDelivery = async (delivery) => {
    try {
      const data = await deliveryApi.getDeliveryDetail(delivery.id);
      if (data) {
        setEditingDelivery({
          ...data,
          deliveredByName: data.deliveredByName || '', 
          deliveredBy: data.deliveredBy || 0,  
          status: data.status === undefined ? 0 : data.status,
        });
        setShowEditDeliveryModal(true);
      }
    } catch (error) {
      console.error('获取成果交付详情失败:', error);
      handleError('获取成果交付详情失败');
    }
  };

  // 修改更新成果交付的处理函数
  const handleUpdateDelivery = async () => {
    // 表单验证
    const errors = {};
    if (!editingDelivery.name?.trim()) {
      errors.name = '请输入成果交付名称';
    }
    if (editingDelivery.type === undefined) {
      errors.type = '请选择成果交付类型';
    }
    if (!editingDelivery.deliveredByName?.trim()) {
      errors.deliveryPerson = '请输入交付人';
    }
    if (!editingDelivery.receivedBy?.trim()) {
      errors.receiver = '请输入接收人';
    }
    if (!editingDelivery.deliveredTime) {
      errors.deliveryTime = '请选择交付时间';
    }
    if (editingDelivery.status === undefined) {
      errors.status = '请选择交付状态';
    }

    if (Object.keys(errors).length > 0) {
      setDeliveryErrors(errors);
      return;
    }

    try {
      // 准备请求数据
      const formData = new FormData();

      // 构建 deliverable 对象
      const deliverable = {
        id: editingDelivery.id,
        projectId: selectedProject.id,
        name: editingDelivery.name,
        type: editingDelivery.type,
        status: editingDelivery.status,
        deliveredBy: editingDelivery.deliveredBy,
        receivedBy: editingDelivery.receivedBy,
        deliveredTime: editingDelivery.deliveredTime,
        createdTime: editingDelivery.createdTime,
        parentId: editingDelivery.parentId || 0,
        fileIds: editingDelivery.projectFiles?.map(file => file.id) || [],
        projectFiles: editingDelivery.projectFiles || [],
        deliveredByName: editingDelivery.deliveredByName,
        receivedByName: editingDelivery.receivedByName
      };

      // 将 deliverable 对象转换为 Blob 并添加到 FormData
      const deliverableBlob = new Blob([JSON.stringify(deliverable)], {
        type: 'application/json'
      });
      formData.append('deliverable', deliverableBlob);

      // 添加新上传的文件
      if (editingDelivery.files) {
        editingDelivery.files.forEach(file => {
          if (file instanceof File) {  // 只上传新添加的文件
            formData.append('files', file);
          }
        });
      }

      // 调用更新接口
      await deliveryApi.updateDelivery(editingDelivery.id, formData);

      // 如果是子成果，刷新父成果的子成果列表
      if (editingDelivery.parentId) {
        const childData = await deliveryApi.getDeliveryList(
          selectedProject.id,
          0,
          10,
          editingDelivery.parentId
        );
        
        if (childData) {
          setChildDeliveries(prev => ({
            ...prev,
            [editingDelivery.parentId]: childData || []
          }));
        }
      }

      // 如果是父成果，刷新其子成果列表
      if (childDeliveries[editingDelivery.id]) {
        const childData = await deliveryApi.getDeliveryList(
          selectedProject.id,
          0,
          10,
          editingDelivery.id
        );
        
        if (childData) {
          setChildDeliveries(prev => ({
            ...prev,
            [editingDelivery.id]: childData || []
          }));
        }
      }

      // 刷新主列表数据
      const mainData = await deliveryApi.getDeliveryList(
        selectedProject.id,
        currentPage,
        pageSize,
        0
      );
      
      if (mainData) {
        setDeliveries(mainData || []);
        setTotalPages(Math.ceil(mainData.length / pageSize));
        setTotalElements(mainData.length);
      }

      // 关闭弹窗并清空编辑状态
      setShowEditDeliveryModal(false);
      setEditingDelivery(null);
      setDeliveryErrors({});

      // 显示成功提示
      handleError('更新成功');
    } catch (error) {
      console.error('更新成果交付失败:', error);
      handleError('更新失败');
    }
  };

  // 修改删除成果的处理函数
  const handleDeleteDelivery = (delivery) => {
    setDeletingDelivery(delivery);
    setShowDeleteDeliveryModal(true);
  };

  // 在其他处理函数附近添加这个函数（比如在 handleCreateDelivery 函数前面）
  const handleAddChildDelivery = (delivery) => {
    // 设置父成果信息
    setParentDelivery(delivery);

    // 设置新建成果的初始值，包括父成果信息
    setNewDelivery({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      name: '',
      type: '',
      deliveryPerson: '',
      receiver: '',
      deliveryTime: '',
      parentId: delivery.id,
      parentName: delivery.name,
      files: []
    });

    // 显示子成果添加弹窗（注意这里使用的是子成果的弹窗状态）
    setShowAddChildDeliveryModal(true);
  };

  // 修改删除成果的处理函数
  const handleDeleteConfirm = async () => {
    try {
      await deliveryApi.deleteDelivery(deletingDelivery.id);
      
      // 关闭弹窗
      setShowDeleteDeliveryModal(false);
      
      // 记录父成果ID（如果有的话）
      const parentId = deletingDelivery.parentId;
      
      // 清空删除状态
      setDeletingDelivery(null);
      
      // 如果是子成果，刷新父成果的子成果列表
      if (parentId) {
        const childData = await deliveryApi.getDeliveryList(
          selectedProject.id,
          0,
          10,
          parentId
        );
        
        if (childData) {
          setChildDeliveries(prev => ({
            ...prev,
            [parentId]: childData || []
          }));
        }
      }
      
      // 如果删除的是父成果，从 childDeliveries 中移除其子成果数据
      if (childDeliveries[deletingDelivery.id]) {
        setChildDeliveries(prev => {
          const newState = { ...prev };
          delete newState[deletingDelivery.id];
          return newState;
        });
        
        // 从展开状态中移除
        setExpandedDeliveries(prev => {
          const newSet = new Set(prev);
          newSet.delete(deletingDelivery.id);
          return newSet;
        });
      }
      
      // 刷新主列表数据
      const mainData = await deliveryApi.getDeliveryList(
        selectedProject.id,
        currentPage,
        pageSize,
        0
      );
      
      if (mainData) {
        setDeliveries(mainData || []);
        setTotalPages(Math.ceil(mainData.length / pageSize));
        setTotalElements(mainData.length);
      }
      
      handleError('删除成功');
    } catch (error) {
      console.error('删除成果失败:', error);
      handleError('删除成果失败');
    }
  };

  // 添加删除确认弹窗
  {
    showDeleteDeliveryModal && deletingDelivery && (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-[400px]">
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4">确认删除</h3>
            <p className="text-gray-600">
              确定要删除成果交付"{deletingDelivery.name}"吗？此操作不可恢复。
            </p>
          </div>
          <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
            <Button
              variant="outline"
              onClick={() => {
                setShowDeleteDeliveryModal(false);
                setDeletingDelivery(null);
              }}
            >
              取消
            </Button>
            <Button
              onClick={handleDeleteConfirm}
              style={{ backgroundColor: '#dc2626', color: 'white' }}
            >
              删除
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // 添加处理展开/收起的函数
  const handleToggleExpand = async (delivery) => {
    try {
      const isExpanded = expandedDeliveries.has(delivery.id);
      
      if (!isExpanded) {
        // 每次展开时都获取最新的子交付数据
        const data = await deliveryApi.getDeliveryList(
          selectedProject.id,
          0,
          10,
          delivery.id
        );
        
        if (data) {
          setChildDeliveries(prev => ({
            ...prev,
            [delivery.id]: data || []
          }));
        }
      }

      setExpandedDeliveries(prev => {
        const newSet = new Set(prev);
        if (isExpanded) {
          newSet.delete(delivery.id);
        } else {
          newSet.add(delivery.id);
        }
        return newSet;
      });
    } catch (error) {
      console.error('获取子交付数据失败:', error);
      handleError('获取子交付数据失败');
    }
  };

  // 渲染成果交付表格行（支持层级显示）
  const renderDeliveryRow = (delivery, level = 0) => {
    const isExpanded = expandedDeliveries.has(delivery.id);

    return (
      <React.Fragment key={delivery.id}>
        <tr
          className={`hover:bg-gray-50 ${level > 0 ? 'bg-gray-50' : ''} cursor-pointer`}
          onClick={() => handleToggleExpand(delivery)}
        >
          <td className="px-4 py-3 text-sm text-gray-900">
            <div className="flex items-center" style={{ paddingLeft: `${level * 20}px` }}>
              {/* 展开/收起图标 */}
              <ChevronRightIcon
                className={`w-4 h-4 mr-2 text-gray-500 transition-transform ${
                  isExpanded ? 'transform rotate-90' : ''
                }`}
              />
              <span>{delivery.name}</span>
            </div>
          </td>
          <td className="px-4 py-3 text-sm text-gray-900">
            {deliveryTypeOptions.find(option => option.value === delivery.type)?.label || '-'}
          </td>
          <td className="px-4 py-3 text-sm text-gray-900">{delivery.deliveredByName}</td>
          <td className="px-4 py-3 text-sm text-gray-900">{delivery.receivedBy}</td>
          <td className="px-4 py-3 text-sm text-gray-900">{formatDateTime(delivery.deliveredTime)}</td>
          {/* 操作按钮 */}
          <td className="px-4 py-3 text-sm text-center" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-center gap-2">
              <button
                onClick={() => handleViewDelivery(delivery)}
                className="text-blue-600 hover:text-blue-800"
                title="查看"
              >
                <EyeOpenIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleEditDelivery(delivery)}
                className="text-blue-600 hover:text-blue-800"
                title="编辑"
              >
                <Pencil1Icon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleAddChildDelivery(delivery)}
                className="text-green-600 hover:text-green-800"
                title="添加子成果"
              >
                <PlusIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleDeleteDelivery(delivery)}
                className="text-red-500 hover:text-red-700"
                title="删除"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>
          </td>
        </tr>
        {/* 渲染子成果 */}
        {isExpanded && childDeliveries[delivery.id]?.map(child =>
          renderDeliveryRow(child, level + 1)
        )}
      </React.Fragment>
    );
  };

  // 修改表格主体的渲染
  <tbody>
    {deliveries.map(delivery => renderDeliveryRow(delivery))}
  </tbody>

  // 修改最外层的布局结构
  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage message={errorMessage} />
      )}

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''}`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-2 text-sm">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  project.status === 0 ? 'bg-gray-100 text-gray-600' :
                  project.status === 1 ? 'bg-blue-100 text-blue-600' :
                  'bg-green-100 text-green-600'
                }`}>
                  {project.status === 0 ? '未开始' :
                   project.status === 1 ? '进行中' :
                   '已结束'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {selectedProject ? (
          <>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">测试计划</p>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow-sm mb-6 relative"> {/* 添加 relative 定位 */}
              <div className="flex flex-wrap gap-4 items-center">
                {/* 搜索框 */}
                <div className="relative w-[300px]">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索成果交付..."
                    className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                </div>

                {/* 添加交付人选择 */}
                <div className="w-48">
                  <select
                    value={searchDeliveryPerson}
                    onChange={(e) => setSearchDeliveryPerson(e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">全部交付人</option>
                    {employees.map(emp => (
                      <option key={emp.id} value={emp.id}>
                        {emp.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleSearch}
                    style={{ backgroundColor: '#0070f3', color: 'white' }}
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={handleReset}
                    variant="outline"
                  >
                    重置
                  </Button>
                  {/* 添加"添加验收"按钮 */}
                  <Button
                    className="flex items-center gap-2"
                    style={{ backgroundColor: '#0070f3', color: 'white' }}
                    onClick={handleAddDelivery}
                  >
                    <PlusIcon className="w-4 h-4" />
                    添加成果交付
                  </Button>
                </div>
              </div>


            </div>

            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">成果交付名称</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">成果交付类型</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">交付人</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">接收人</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">交付时间</th>
                    <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {deliveries.map(delivery => renderDeliveryRow(delivery))}
                  {deliveries.length === 0 && (
                    <tr>
                      <td colSpan="6" className="px-4 py-6 text-center text-gray-500">
                        暂无数据
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>

              {/* 分页器 */}
              <div className="flex items-center justify-end px-4 py-3 border-t">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500 mr-4">
                    共 {totalElements} 条记录
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newPage = Math.max(0, currentPage - 1);
                      setCurrentPage(newPage);
                      fetchDeliveryData(selectedProject.id, newPage);
                    }}
                    disabled={currentPage === 0}
                    className="px-2 py-1 text-sm"
                  >
                    上一页
                  </Button>
                  <div className="flex items-center">
                    <button
                      className={`px-3 py-1 text-sm rounded-lg ${currentPage === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                      onClick={() => {
                        setCurrentPage(0);
                        fetchDeliveryData(selectedProject.id, 0);
                      }}
                    >
                      1
                    </button>
                    {currentPage > 2 && <span className="px-2 text-gray-500">...</span>}
                    {currentPage > 1 && (
                      <button
                        className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                        onClick={() => {
                          setCurrentPage(currentPage - 1);
                          fetchDeliveryData(selectedProject.id, currentPage - 1);
                        }}
                      >
                        {currentPage}
                      </button>
                    )}
                    {currentPage > 0 && currentPage < totalPages - 1 && (
                      <button
                        className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                      >
                        {currentPage + 1}
                      </button>
                    )}
                    {currentPage < totalPages - 2 && (
                      <button
                        className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                        onClick={() => {
                          setCurrentPage(currentPage + 1);
                          fetchDeliveryData(selectedProject.id, currentPage + 1);
                        }}
                      >
                        {currentPage + 2}
                      </button>
                    )}
                    {currentPage < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                    {totalPages > 1 && (
                      <button
                        className={`px-3 py-1 text-sm rounded-lg ${currentPage === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                        onClick={() => {
                          setCurrentPage(totalPages - 1);
                          fetchDeliveryData(selectedProject.id, totalPages - 1);
                        }}
                      >
                        {totalPages}
                      </button>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const newPage = Math.min(totalPages - 1, currentPage + 1);
                      setCurrentPage(newPage);
                      fetchDeliveryData(selectedProject.id, newPage);
                    }}
                    disabled={currentPage >= totalPages - 1}
                    className="px-2 py-1 text-sm"
                  >
                    下一页
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目查看测试计划</p>
            </div>
          </div>
        )}





        {/* 成果交付相关弹窗 */}
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">编辑计划</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingPlan(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        项目名称
                      </label>
                      <input
                        type="text"
                        value={selectedProject?.name}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        计划名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={editingPlan.name}
                        onChange={(e) => setEditingPlan({ ...editingPlan, name: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.planName ? 'border-red-500' : 'border-gray-300'}`}
                        placeholder="请输入计划名称"
                      />
                      {planErrors.planName && <p className="text-red-500 text-sm">{planErrors.planName}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        创建人 <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={editingPlan.creator}
                        onChange={(e) => setEditingPlan({ ...editingPlan, creator: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.creator ? 'border-red-500' : 'border-gray-300'}`}
                      >
                        <option value="">请选择创建人</option>
                        {employees.map(employee => (
                          <option key={employee.id} value={employee.id}>{employee.name}</option>
                        ))}
                      </select>
                      {planErrors.creator && <p className="text-red-500 text-sm">{planErrors.creator}</p>}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        计划状态 <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={editingPlan.status}
                        onChange={(e) => setEditingPlan({ ...editingPlan, status: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.status ? 'border-red-500' : 'border-gray-300'}`}
                      >
                        <option value="">请选择计划状态</option>
                        {planStatusOptions.map(status => (
                          <option key={status.value} value={status.value}>{status.label}</option>
                        ))}
                      </select>
                      {planErrors.status && <p className="text-red-500 text-sm">{planErrors.status}</p>}
                    </div>
                  </div>



                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        开始时间 <span className="text-red-500">*</span>
                      </label>
                      <DateTimeInput
                        value={editingPlan.startTime ? new Date(editingPlan.startTime).toISOString().slice(0, 19) : ''}
                        onChange={(e) => setEditingPlan({ ...editingPlan, startTime: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.startTime ? 'border-red-500' : 'border-gray-300'}`}
                      />
                      {planErrors.startTime && <p className="text-red-500 text-sm">{planErrors.startTime}</p>}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        结束时间 <span className="text-red-500">*</span>
                      </label>
                      <DateTimeInput
                        value={editingPlan.endTime ? new Date(editingPlan.endTime).toISOString().slice(0, 19) : ''}
                        onChange={(e) => setEditingPlan({ ...editingPlan, endTime: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.endTime ? 'border-red-500' : 'border-gray-300'}`}
                      />
                      {planErrors.endTime && <p className="text-red-500 text-sm">{planErrors.endTime}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划描述
                    </label>
                    <textarea
                      value={editingPlan.description || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, description: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入计划描述..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      已上传文件
                    </label>
                    {editingPlan.projectFiles && editingPlan.projectFiles.length > 0 ? (
                      <div className="space-y-2">
                        {editingPlan.projectFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center">
                              <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-600">{file.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handlePreviewFile(file)}
                                className="text-blue-500 hover:text-blue-700"
                                title="预览文件"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDownloadFile(file)}
                                className="text-blue-500 hover:text-blue-700"
                                title="下载文件"
                              >
                                <DownloadIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 p-2">暂无已上传文件</div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <label
                      htmlFor="edit-file-upload"
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                    >
                      <div className="flex flex-col items-center">
                        <svg
                          className="w-8 h-8 text-gray-400 mb-1"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                          aria-hidden="true"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div className="text-sm text-gray-600">
                          <span>点击或拖拽文件到此处上传</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                      </div>
                    </label>
                    <input
                      id="edit-file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        const newFiles = Array.from(e.target.files || []);
                        setEditingPlan(prev => ({
                          ...prev,
                          files: [...(prev.files || []), ...newFiles]
                        }));
                      }}
                    />

                    {editingPlan.files && editingPlan.files.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <div className="text-sm font-medium text-gray-700">已上传文件：</div>
                        {editingPlan.files.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center">
                              <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-600">{file.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => {
                                  setEditingPlan(prev => ({
                                    ...prev,
                                    files: prev.files.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingPlan(null);
                  }}
                >
                  取消
                </Button>
                <Button onClick={handleUpdatePlan}>
                  保存
                </Button>
              </div>
            </div>
          </div>
        )}

        {showDeleteModal && deletingRequirement && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[400px]">
              <div className="p-6 border-b">
                <h3 className="text-xl font-semibold">确认删除</h3>
              </div>
              <div className="p-6">
                <p className="text-gray-600">
                  确定要删除测试计划 "{deletingRequirement.name}" 吗？此操作不可恢复。
                </p>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setDeletingRequirement(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  onClick={confirmDelete}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 创建验收弹窗 */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">添加产品验收</h3>
                <button
                  onClick={() => setShowNewModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      readOnly
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>

                  {/* 验收名称和负责人 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收名称 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newAcceptance.acceptanceName}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            acceptanceName: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceName: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceName ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收名称"
                      />
                      {formErrors.acceptanceName && (
                        <div className="text-red-500 text-sm mt-1">
                          {formErrors.acceptanceName}
                        </div>
                      )}
                    </div>

                    {/* 负责人 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={newAcceptance.manager}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            manager: e.target.value
                          });
                          // 当选择有效时，清除对应的错误提示
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              manager: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.manager ? 'border-red-500' : 'border-gray-300'
                          }`}
                      >
                        <option value="">请选择负责人</option>
                        {employees.map((emp) => (
                          <option key={emp.id} value={emp.name}>{emp.name}</option>
                        ))}
                      </select>
                      {formErrors.manager && (
                        <div className="text-red-500 text-sm mt-1">
                          {formErrors.manager}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 验收人员和验收时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收人员 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收人员 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative acceptor-select-container">
                        <div
                          className={`min-h-[38px] px-3 py-2 border rounded-lg focus:outline-none cursor-pointer ${formErrors.acceptors ? 'border-red-500' : 'border-gray-300'
                            }`}
                          onClick={() => setShowAcceptorSelect(!showAcceptorSelect)}
                        >
                          {newAcceptance.memberNames && newAcceptance.memberNames.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {newAcceptance.memberNames.map((name, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-sm flex items-center gap-1"
                                >
                                  {name}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newMemberNames = [...newAcceptance.memberNames];
                                      const newMemberIds = [...newAcceptance.memberIds];
                                      newMemberNames.splice(index, 1);
                                      newMemberIds.splice(index, 1);
                                      setNewAcceptance({
                                        ...newAcceptance,
                                        memberNames: newMemberNames,
                                        memberIds: newMemberIds
                                      });
                                    }}
                                    className="hover:text-blue-900"
                                  >
                                    <Cross2Icon className="w-3 h-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择验收人员</span>
                          )}
                        </div>

                        {formErrors.acceptors && (
                          <div className="text-red-500 text-sm mt-1">{formErrors.acceptors}</div>
                        )}

                        {showAcceptorSelect && (
                          <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border rounded-lg shadow-lg">
                            <div className="p-2">
                              <div className="relative">
                                <input
                                  type="text"
                                  value={acceptorSearchQuery}
                                  onChange={(e) => setAcceptorSearchQuery(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none"
                                  placeholder="搜索验收人员..."
                                />
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                              </div>
                              <div className="mt-2 max-h-[200px] overflow-y-auto">
                                {employees
                                  .filter(emp => emp.name.toLowerCase().includes(acceptorSearchQuery.toLowerCase()))
                                  .map(emp => (
                                    <div
                                      key={emp.id}
                                      className={`p-2 cursor-pointer hover:bg-gray-50 rounded-lg flex items-center justify-between ${(newAcceptance.memberIds || []).includes(emp.id) ? 'bg-blue-50' : ''
                                        }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = (newAcceptance.memberIds || []).includes(emp.id);
                                        let newMemberIds = [...(newAcceptance.memberIds || [])];
                                        let newMemberNames = [...(newAcceptance.memberNames || [])];

                                        if (isSelected) {
                                          newMemberIds = newMemberIds.filter(id => id !== emp.id);
                                          newMemberNames = newMemberNames.filter(name => name !== emp.name);
                                        } else {
                                          newMemberIds.push(emp.id);
                                          newMemberNames.push(emp.name);
                                        }

                                        setNewAcceptance({
                                          ...newAcceptance,
                                          memberIds: newMemberIds,
                                          memberNames: newMemberNames
                                        });

                                        if (newMemberIds.length > 0) {
                                          setFormErrors(prev => ({
                                            ...prev,
                                            acceptors: ''
                                          }));
                                        }
                                      }}
                                    >
                                      <span className="text-sm text-gray-900">{emp.name}</span>
                                      {(newAcceptance.memberIds || []).includes(emp.id) && (
                                        <CheckIcon className="w-4 h-4 text-blue-500" />
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 验收时间 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={newAcceptance.acceptanceTime}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            acceptanceTime: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1" // 添加 step="1" 以支持精确到秒
                      />
                      {formErrors.acceptanceTime && (
                        <p className="text-red-500 text-sm mt-1">{formErrors.acceptanceTime}</p>
                      )}
                    </div>
                  </div>

                  {/* 验收标准和验收建议 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收标准 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收标准 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={newAcceptance.standard}
                        onChange={(e) => {
                          setNewAcceptance({
                            ...newAcceptance,
                            standard: e.target.value
                          });
                          // 当输入有效时，清除对应的错误提示
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              standard: ''
                            }));
                          }
                        }}
                        rows="3"
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.standard ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收标准"
                      />
                      {formErrors.standard && (
                        <p className="text-red-500 text-sm mt-1">{formErrors.standard}</p>
                      )}
                    </div>

                    {/* 验收建议 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收建议
                      </label>
                      <textarea
                        value={newAcceptance.suggestion}
                        onChange={(e) => setNewAcceptance({
                          ...newAcceptance,
                          suggestion: e.target.value
                        })}
                        rows="3"
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                        placeholder="请输入验收建议"
                      />
                    </div>
                  </div>

                  {/* 附件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <label
                      htmlFor="delivery-file-upload"
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                      onClick={() => document.getElementById('delivery-file-upload').click()}
                    >
                      <div className="flex flex-col items-center">
                        <svg
                          className="w-8 h-8 text-gray-400 mb-1"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                          aria-hidden="true"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div className="text-sm text-gray-600">
                          <span>点击或拖拽文件到此处上传</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                      </div>
                    </label>
                    <input
                      id="delivery-file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        const newFiles = Array.from(e.target.files || []);
                        setNewAcceptance(prev => ({
                          ...prev,
                          files: [...prev.files, ...newFiles]
                        }));
                      }}
                    />

                    {/* 已上传文件列表 */}
                    {newAcceptance.files.length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">已选择的文件：</div>
                        <div className="max-h-[200px] overflow-y-auto space-y-2">
                          {newAcceptance.files.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border border-gray-200"
                            >
                              <div className="flex items-center">
                                <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <button
                                onClick={() => {
                                  setNewAcceptance(prev => ({
                                    ...prev,
                                    files: prev.files.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="p-1 hover:bg-gray-200 rounded-full"
                                title="删除文件"
                              >
                                <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNewModal(false)}
                >
                  取消
                </Button>
                <Button onClick={handleCreateAcceptance} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                  创建
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 修改弹窗 */}
        {showEditModal && editingAcceptance && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">修改验收</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingAcceptance(null);
                    setFormErrors({});
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input
                      type="text"
                      value={selectedProject?.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>

                  {/* 验收名称和负责人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={editingAcceptance.name}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            name: e.target.value
                          });
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceName: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceName ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收名称"
                      />
                      {formErrors.acceptanceName && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.acceptanceName}</div>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        负责人 <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={editingAcceptance.responseName}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            responseName: e.target.value
                          });
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              manager: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.manager ? 'border-red-500' : 'border-gray-300'
                          }`}
                      >
                        <option value="">请选择负责人</option>
                        {employees.map((emp) => (
                          <option key={emp.id} value={emp.name}>{emp.name}</option>
                        ))}
                      </select>
                      {formErrors.manager && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.manager}</div>
                      )}
                    </div>
                  </div>

                  {/* 验收人员和验收时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收人员选择部分 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收人员 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative acceptor-select-container">
                        <div
                          className={`min-h-[38px] px-3 py-2 border rounded-lg focus:outline-none cursor-pointer ${formErrors.memberIds ? 'border-red-500' : 'border-gray-300'
                            }`}
                          onClick={() => setShowAcceptorSelect(!showAcceptorSelect)}
                        >
                          {editingAcceptance.memberNames && editingAcceptance.memberNames.length > 0 ? (
                            <div className="flex flex-wrap gap-2">
                              {editingAcceptance.memberNames.map((name, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-sm flex items-center gap-1"
                                >
                                  {name}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newMemberNames = [...editingAcceptance.memberNames];
                                      const newMemberIds = [...editingAcceptance.memberIds];
                                      newMemberNames.splice(index, 1);
                                      newMemberIds.splice(index, 1);
                                      setEditingAcceptance(prev => ({
                                        ...prev,
                                        memberNames: newMemberNames,
                                        memberIds: newMemberIds
                                      }));
                                    }}
                                    className="hover:text-blue-900"
                                  >
                                    <Cross2Icon className="w-3 h-3" />
                                  </button>
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择验收人员</span>
                          )}
                        </div>

                        {formErrors.acceptors && (
                          <div className="text-red-500 text-sm mt-1">{formErrors.acceptors}</div>
                        )}

                        {showAcceptorSelect && (
                          <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border rounded-lg shadow-lg">
                            <div className="p-2">
                              <div className="relative">
                                <input
                                  type="text"
                                  value={acceptorSearchQuery}
                                  onChange={(e) => setAcceptorSearchQuery(e.target.value)}
                                  onClick={(e) => e.stopPropagation()}
                                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none"
                                  placeholder="搜索验收人员..."
                                />
                                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                              </div>
                              <div className="mt-2 max-h-[200px] overflow-y-auto">
                                {employees
                                  .filter(emp => emp.name.toLowerCase().includes(acceptorSearchQuery.toLowerCase()))
                                  .map(emp => (
                                    <div
                                      key={emp.id}
                                      className={`p-2 cursor-pointer hover:bg-gray-50 rounded-lg flex items-center justify-between ${(editingAcceptance?.memberIds || []).includes(emp.id) ? 'bg-blue-50' : ''
                                        }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const isSelected = (editingAcceptance?.memberIds || []).includes(emp.id);
                                        let newMemberIds = [...(editingAcceptance?.memberIds || [])];
                                        let newMemberNames = [...(editingAcceptance?.memberNames || [])];

                                        if (isSelected) {
                                          newMemberIds = newMemberIds.filter(id => id !== emp.id);
                                          newMemberNames = newMemberNames.filter(name => name !== emp.name);
                                        } else {
                                          newMemberIds.push(emp.id);
                                          newMemberNames.push(emp.name);
                                        }

                                        setEditingAcceptance(prev => ({
                                          ...prev,
                                          memberIds: newMemberIds,
                                          memberNames: newMemberNames
                                        }));

                                        if (newMemberIds.length > 0) {
                                          setFormErrors(prev => ({
                                            ...prev,
                                            acceptors: ''
                                          }));
                                        }
                                      }}
                                    >
                                      <span className="text-sm text-gray-900">{emp.name}</span>
                                      {(editingAcceptance?.memberIds || []).includes(emp.id) && (
                                        <CheckIcon className="w-4 h-4 text-blue-500" />
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 验收时间 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={editingAcceptance.acceptanceTime}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            acceptanceTime: e.target.value
                          });
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              acceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.acceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {formErrors.acceptanceTime && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.acceptanceTime}</div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收结束时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={editingAcceptance.endAcceptanceTime}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            endAcceptanceTime: e.target.value
                          });
                          if (e.target.value) {
                            setFormErrors(prev => ({
                              ...prev,
                              endAcceptanceTime: ''
                            }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.endAcceptanceTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {formErrors.endAcceptanceTime && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.endAcceptanceTime}</div>
                      )}
                    </div>

                    {/* 验收状态 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收状态 <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={editingAcceptance.result}
                        onChange={(e) => setEditingAcceptance({
                          ...editingAcceptance,
                          result: parseInt(e.target.value)
                        })}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      >
                        <option value="">请选择验收状态</option>
                        {acceptanceStatusOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                  </div>

                  {/* 验收标准和验收建议 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 验收标准 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        验收标准 <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={editingAcceptance.standard}
                        onChange={(e) => {
                          setEditingAcceptance({
                            ...editingAcceptance,
                            standard: e.target.value
                          });
                          if (e.target.value.trim()) {
                            setFormErrors(prev => ({
                              ...prev,
                              standard: ''
                            }));
                          }
                        }}
                        rows="2"
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${formErrors.standard ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入验收标准"
                      />
                      {formErrors.standard && (
                        <div className="text-red-500 text-sm mt-1">{formErrors.standard}</div>
                      )}
                    </div>

                    {/* 验收建议 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">验收建议</label>
                      <textarea
                        value={editingAcceptance.advice}
                        onChange={(e) => setEditingAcceptance({
                          ...editingAcceptance,
                          advice: e.target.value
                        })}
                        rows="2"
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                        placeholder="请输入验收建议"
                      />
                    </div>
                  </div>



                  {/* 附件 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <div className="space-y-2">
                      {/* 已上传的文件列表 */}
                      {editingAcceptance.projectFiles && editingAcceptance.projectFiles.length > 0 && (
                        <div className="space-y-2">
                          {editingAcceptance.projectFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={() => handlePreviewFile(file)}
                                  className="text-blue-500 hover:text-blue-700"
                                  title="预览文件"
                                >
                                  <EyeOpenIcon className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDownloadFile(file)}
                                  className="text-blue-500 hover:text-blue-700"
                                  title="下载文件"
                                >
                                  <DownloadIcon className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    // 从projectFiles中移除文件
                                    setEditingAcceptance(prev => ({
                                      ...prev,
                                      projectFiles: prev.projectFiles.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="text-red-500 hover:text-red-700"
                                  title="删除文件"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* 文件上传按钮 */}
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors">
                        <div className="flex flex-col items-center">
                          <svg
                            className="w-8 h-8 text-gray-400 mb-1"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <div className="text-sm text-gray-600">
                            <span>点击或拖拽文件到此处上传</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                          <input
                            id="edit-file-upload"
                            type="file"
                            className="hidden"
                            multiple
                            onChange={(e) => {
                              const newFiles = Array.from(e.target.files || []);
                              setEditingAcceptance(prev => ({
                                ...prev,
                                files: [...(prev.files || []), ...newFiles]
                              }));
                            }}
                          />
                        </div>
                      </div>

                      {/* 新上传的文件列表 */}
                      {editingAcceptance.files && editingAcceptance.files.length > 0 && (
                        <div className="space-y-2">
                          {editingAcceptance.files.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <button
                                onClick={() => {
                                  setEditingAcceptance(prev => ({
                                    ...prev,
                                    files: prev.files.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="text-red-500 hover:text-red-700"
                                title="删除文件"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingAcceptance(null);
                    setFormErrors({});
                  }}
                >
                  取消
                </Button>
                <Button onClick={handleUpdateAcceptance} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                  保存
                </Button>
              </div>
            </div>
          </div>
        )}

        {showDeleteAcceptanceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-[400px]">
              <h3 className="text-lg font-medium mb-4">确认删除</h3>
              <p className="text-gray-600 mb-6">
                确定要删除验收"{deletingAcceptance?.name}"吗？此操作不可恢复。
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteAcceptanceModal(false);
                    setDeletingAcceptance(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  style={{ backgroundColor: '#dc2626', color: 'white' }}
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 添加创建成果交付的弹窗 */}
        {showCreateDeliveryModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  {/* 根据是否有父成果显示不同的标题 */}
                  <h3 className="text-lg font-semibold">
                    {newDelivery.parentId ? '新增子成果' : '新增成果交付'}
                  </h3>
                  <button
                    onClick={() => {
                      setShowCreateDeliveryModal(false);
                      setNewDelivery({
                        projectId: '',
                        projectName: '',
                        name: '',
                        type: '',
                        deliveryPerson: '',
                        receiver: '',
                        deliveryTime: '',
                        parentId: '',
                        parentName: '',
                        files: []
                      });
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Cross2Icon className="w-5 h-5" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>

                  {/* 如果是子成果，显示父成果名称 */}
                  {newDelivery.parentId && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">父成果</label>
                      <input
                        type="text"
                        value={newDelivery.parentName || ''}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                  )}

                  {/* 成果交付名称和类型 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        成果交付名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newDelivery.name}
                        onChange={(e) => setNewDelivery({ ...newDelivery, name: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.name ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入成果交付名称"
                      />
                      {deliveryErrors.name && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.name}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        成果交付类型 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryTypeDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryTypeDropdownOpen(!isDeliveryTypeDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newDelivery.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newDelivery.type ? 'text-gray-900' : 'text-gray-400'}>
                            {newDelivery.type ? deliveryTypeOptions.find(t => t.value === Number(newDelivery.type))?.label : '请选择成果交付类型'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryTypeDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryTypeDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliveryTypeOptions.map(type => (
                                <div
                                  key={type.value}
                                  onClick={() => {
                                    setNewDelivery({ ...newDelivery, type: type.value });
                                    setDeliveryErrors({ ...deliveryErrors, type: '' });
                                    setIsDeliveryTypeDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(newDelivery.type) === type.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {type.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.type && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.type}</p>
                      )}
                    </div>
                  </div>

                  {/* 交付人和接收人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryPersonDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryPersonDropdownOpen(!isDeliveryPersonDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newDelivery.deliveryPerson ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newDelivery.deliveryPerson ? 'text-gray-900' : 'text-gray-400'}>
                            {newDelivery.deliveryPerson || '请选择交付人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryPersonDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryPersonDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(emp => (
                                <div
                                  key={emp.id}
                                  onClick={() => {
                                    setNewDelivery({ ...newDelivery, deliveryPerson: emp.name });
                                    setDeliveryErrors({ ...deliveryErrors, deliveryPerson: '' });
                                    setIsDeliveryPersonDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    newDelivery.deliveryPerson === emp.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {emp.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.deliveryPerson && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryPerson}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        接收人 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newDelivery.receiver}
                        onChange={(e) => setNewDelivery({ ...newDelivery, receiver: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.receiver ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入接收人"
                      />
                      {deliveryErrors.receiver && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.receiver}</p>
                      )}
                    </div>
                  </div>

                  {/* 交付时间和父成果 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付时间 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="datetime-local"
                        value={newDelivery.deliveryTime}
                        onChange={(e) => setNewDelivery({ ...newDelivery, deliveryTime: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.deliveryTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {deliveryErrors.deliveryTime && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryTime}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        父成果
                      </label>
                      <input
                        type="text"
                        value={newDelivery.parentDelivery}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                      />
                    </div>
                  </div>

                  {/* 附件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <label
                      htmlFor="create-delivery-file-upload"
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                    >
                      <div className="flex flex-col items-center">
                        <svg
                          className="w-8 h-8 text-gray-400 mb-1"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                          aria-hidden="true"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div className="text-sm text-gray-600">
                          <span>点击或拖拽文件到此处上传</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                      </div>
                    </label>
                    <input
                      id="create-delivery-file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      onChange={(e) => {
                        const newFiles = Array.from(e.target.files || []);
                        setNewDelivery(prev => ({
                          ...prev,
                          files: [...prev.files, ...newFiles]
                        }));
                      }}
                    />

                    {/* 已上传文件列表 */}
                    {newDelivery.files.length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">已选择的文件：</div>
                        <div className="max-h-[200px] overflow-y-auto space-y-2">
                          {newDelivery.files.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border border-gray-200"
                            >
                              <div className="flex items-center">
                                <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <button
                                onClick={() => {
                                  setNewDelivery(prev => ({
                                    ...prev,
                                    files: prev.files.filter((_, i) => i !== index)
                                  }));
                                }}
                                className="p-1 hover:bg-gray-200 rounded-full"
                                title="删除文件"
                              >
                                <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateDeliveryModal(false)}
                >
                  取消
                </Button>
                <Button
                  onClick={handleCreateDelivery}
                  style={{ backgroundColor: '#0070f3', color: 'white' }}
                >
                  创建
                </Button>
              </div>
            </div>
          </div>
        )}

        {showViewDeliveryModal && viewingDelivery && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">查看成果交付</h3>
                <button
                  onClick={() => {
                    setShowViewDeliveryModal(false);
                    setViewingDelivery(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <div className="w-full px-3 py-2  rounded-lg ">
                      {selectedProject?.name}
                    </div>
                  </div>

                  {/* 成果交付名称和类型 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">成果交付名称</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {viewingDelivery.name}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">成果交付类型</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {deliveryTypeOptions.find(option => option.value === viewingDelivery.type)?.label || '-'}
                      </div>
                    </div>
                  </div>

                  {/* 交付人和接收人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">交付人</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {viewingDelivery.deliveredByName}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">接收人</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {viewingDelivery.receivedBy}
                      </div>
                    </div>
                  </div>

                  {/* 交付时间和父成果 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">交付时间</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {formatDateTime(viewingDelivery.deliveredTime)}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">父成果</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {viewingDelivery.parentId || '-'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">交付状态</label>
                      <div className="w-full px-3 py-2  rounded-lg ">
                        {deliveryStatusOptions.find(option => option.value === viewingDelivery.status)?.label || '-'}
                      </div>
                    </div>
                  </div>

                  {/* 附件列表 */}
                  {viewingDelivery.projectFiles && viewingDelivery.projectFiles.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">附件</label>
                      <div className="space-y-2">
                        {viewingDelivery.projectFiles.map((file) => (
                          <div
                            key={file.id}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center">
                              <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-600">{file.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {/* 添加预览按钮 */}
                              <button
                                onClick={() => handlePreviewFile(file)}
                                className="text-blue-600 hover:text-blue-800"
                                title="预览"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </button>
                              {/* 下载按钮 */}
                              <button
                                onClick={() => handleDownloadFile(file)}
                                className="text-blue-600 hover:text-blue-800"
                                title="下载"
                              >
                                <DownloadIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowViewDeliveryModal(false);
                    setViewingDelivery(null);
                  }}
                >
                  关闭
                </Button>
              </div>
            </div>
          </div>
        )}

        {showEditDeliveryModal && editingDelivery && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">编辑成果交付</h3>
                <button
                  onClick={() => {
                    setShowEditDeliveryModal(false);
                    setEditingDelivery(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Cross2Icon className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input
                      type="text"
                      value={selectedProject?.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>

                  {/* 成果交付名称和类型 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">成果交付名称</label>
                      <input
                        type="text"
                        value={editingDelivery.name}
                        onChange={(e) => setEditingDelivery({ ...editingDelivery, name: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.name ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入成果交付名称"
                      />
                      {deliveryErrors.name && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.name}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">成果交付类型</label>
                      <div className="relative" ref={deliveryTypeDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryTypeDropdownOpen(!isDeliveryTypeDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !editingDelivery.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingDelivery.type ? 'text-gray-900' : 'text-gray-400'}>
                            {editingDelivery.type ? deliveryTypeOptions.find(t => t.value === Number(editingDelivery.type))?.label : '请选择成果交付类型'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryTypeDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryTypeDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliveryTypeOptions.map(type => (
                                <div
                                  key={type.value}
                                  onClick={() => {
                                    setEditingDelivery({ ...editingDelivery, type: type.value });
                                    setDeliveryErrors({ ...deliveryErrors, type: '' });
                                    setIsDeliveryTypeDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(editingDelivery.type) === type.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {type.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.type && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.type}</p>
                      )}
                    </div>
                  </div>

                  {/* 交付人和接收人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryPersonDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryPersonDropdownOpen(!isDeliveryPersonDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !editingDelivery.deliveredByName ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingDelivery.deliveredByName ? 'text-gray-900' : 'text-gray-400'}>
                            {editingDelivery.deliveredByName || '请选择交付人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryPersonDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryPersonDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(emp => (
                                <div
                                  key={emp.id}
                                  onClick={() => {
                                    setEditingDelivery({
                                      ...editingDelivery,
                                      deliveredByName: emp.name,
                                      deliveredBy: emp.id || 0
                                    });
                                    setDeliveryErrors({ ...deliveryErrors, deliveryPerson: '' });
                                    setIsDeliveryPersonDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    editingDelivery.deliveredByName === emp.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {emp.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.deliveryPerson && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryPerson}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">接收人</label>
                      <input
                        type="text"
                        value={editingDelivery.receivedBy}
                        onChange={(e) => setEditingDelivery({ ...editingDelivery, receivedBy: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.receiver ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入接收人"
                      />
                      {deliveryErrors.receiver && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveredByName}</p>
                      )}
                    </div>
                  </div>

                  {/* 交付时间和父成果 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">交付时间</label>
                      <input
                        type="datetime-local"
                        value={editingDelivery.deliveredTime}
                        onChange={(e) => setEditingDelivery({ ...editingDelivery, deliveredTime: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.deliveryTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                        step="1"
                      />
                      {deliveryErrors.deliveryTime && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryTime}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">父成果</label>
                      <input
                        type="text"
                        value={editingDelivery.parentId || ''}
                        onChange={(e) => setEditingDelivery({ ...editingDelivery, parentId: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.parentDelivery ? 'border-red-500' : 'border-gray-300'
                          }`}
                        disabled
                      />
                      {deliveryErrors.parentDelivery && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.parentDelivery}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* 交付状态 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付状态 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryStatusDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryStatusDropdownOpen(!isDeliveryStatusDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            editingDelivery.status === undefined ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingDelivery.status !== undefined ? 'text-gray-900' : 'text-gray-400'}>
                            {editingDelivery.status !== undefined ? deliveryStatusOptions.find(s => s.value === Number(editingDelivery.status))?.label : '请选择交付状态'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryStatusDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryStatusDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliveryStatusOptions.map(status => (
                                <div
                                  key={status.value}
                                  onClick={() => {
                                    setEditingDelivery({ ...editingDelivery, status: status.value });
                                    setDeliveryErrors({ ...deliveryErrors, status: '' });
                                    setIsDeliveryStatusDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(editingDelivery.status) === status.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {status.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.status && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.status}</p>
                      )}
                    </div>
                  </div>



                  {/* 附件上传 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">附件</label>

                    {/* 已上传的文件列表 */}
                    {editingDelivery.projectFiles && editingDelivery.projectFiles.length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">已上传文件：</div>
                        <div className="max-h-[100px] overflow-y-auto border rounded-lg">
                          {editingDelivery.projectFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 hover:bg-gray-50 border-b last:border-b-0"
                            >
                              <div className="flex items-center">
                                <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-600">{file.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  onClick={() => handlePreviewFile(file)}
                                  className="text-blue-600 hover:text-blue-800"
                                  title="预览"
                                >
                                  <EyeOpenIcon className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    setEditingDelivery(prev => ({
                                      ...prev,
                                      projectFiles: prev.projectFiles.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="text-red-500 hover:text-red-700"
                                  title="删除"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 文件上传区域 */}
                    <div>
                      <label
                        htmlFor="edit-delivery-file-upload"
                        className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                      >
                        <div className="flex flex-col items-center">
                          <svg
                            className="w-8 h-8 text-gray-400 mb-1"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <div className="text-sm text-gray-600">
                            <span>点击或拖拽文件到此处上传</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                        </div>
                      </label>
                      <input
                        id="edit-delivery-file-upload"
                        type="file"
                        className="hidden"
                        multiple
                        onChange={(e) => {
                          const newFiles = Array.from(e.target.files || []);
                          setEditingDelivery(prev => ({
                            ...prev,
                            files: [...(prev.files || []), ...newFiles]
                          }));
                        }}
                      />

                      {/* 新上传的文件列表 */}
                      {editingDelivery.files && editingDelivery.files.length > 0 && (
                        <div className="mt-4">
                          <div className="text-sm font-medium text-gray-700 mb-2">待上传文件：</div>
                          <div className="max-h-[100px] overflow-y-auto border rounded-lg">
                            {editingDelivery.files.map((file, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between p-2 hover:bg-gray-50 border-b last:border-b-0"
                              >
                                <div className="flex items-center">
                                  <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                                  <span className="text-sm text-gray-600">{file.name}</span>
                                </div>
                                <button
                                  onClick={() => {
                                    setEditingDelivery(prev => ({
                                      ...prev,
                                      files: prev.files.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="text-red-500 hover:text-red-700"
                                  title="删除"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowEditDeliveryModal(false);
                    setEditingDelivery(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  onClick={handleUpdateDelivery}
                  style={{ backgroundColor: '#0070f3', color: 'white' }}
                >
                  保存
                </Button>
              </div>
            </div>
          </div>
        )}

        {showDeleteDeliveryModal && deletingDelivery && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[400px]">
              <div className="p-6">
        <h3 className="text-lg font-semibold mb-4">确认删除</h3>
        <p className="text-gray-600">
          确定要删除成果交付"{deletingDelivery.name}"吗？此操作不可恢复。
        </p>
      </div>
      <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
        <Button
          variant="outline"
          onClick={() => {
            setShowDeleteDeliveryModal(false);
            setDeletingDelivery(null);
          }}
        >
          取消
        </Button>
        <Button
          onClick={handleDeleteConfirm}
          style={{ backgroundColor: '#dc2626', color: 'white' }}
        >
          删除
        </Button>
      </div>
    </div>
  </div>
)}

        {/* 添加子成果弹窗 */}
        {showAddChildDeliveryModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold">添加子成果</h3>
                  <button
                    onClick={() => setShowAddChildDeliveryModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Cross2Icon className="w-5 h-5" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* 项目名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input
                      type="text"
                      value={selectedProject?.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>


                  {/* 成果交付名称和类型 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        成果交付名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newDelivery.name}
                        onChange={(e) => setNewDelivery({ ...newDelivery, name: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.name ? 'border-red-500' : 'border-gray-300'
                          }`}
                        placeholder="请输入成果交付名称"
                      />
                      {deliveryErrors.name && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.name}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        成果交付类型 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryTypeDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryTypeDropdownOpen(!isDeliveryTypeDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newDelivery.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newDelivery.type ? 'text-gray-900' : 'text-gray-400'}>
                            {newDelivery.type ? deliveryTypeOptions.find(t => t.value === Number(newDelivery.type))?.label : '请选择成果交付类型'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryTypeDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryTypeDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliveryTypeOptions.map(type => (
                                <div
                                  key={type.value}
                                  onClick={() => {
                                    setNewDelivery({ ...newDelivery, type: type.value });
                                    setDeliveryErrors({ ...deliveryErrors, type: '' });
                                    setIsDeliveryTypeDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(newDelivery.type) === type.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {type.label}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 交付人和接收人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={deliveryPersonDropdownRef}>
                        <div
                          onClick={() => setIsDeliveryPersonDropdownOpen(!isDeliveryPersonDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newDelivery.deliveryPerson ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newDelivery.deliveryPerson ? 'text-gray-900' : 'text-gray-400'}>
                            {newDelivery.deliveryPerson || '请选择交付人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isDeliveryPersonDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isDeliveryPersonDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(emp => (
                                <div
                                  key={emp.id}
                                  onClick={() => {
                                    setNewDelivery({ ...newDelivery, deliveryPerson: emp.name });
                                    setDeliveryErrors({ ...deliveryErrors, deliveryPerson: '' });
                                    setIsDeliveryPersonDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    newDelivery.deliveryPerson === emp.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {emp.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {deliveryErrors.deliveryPerson && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryPerson}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        接收人 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newDelivery.receiver}
                        onChange={(e) => setNewDelivery({ ...newDelivery, receiver: e.target.value })}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none border-gray-300"
                        placeholder="请输入接收人"
                      />
                    </div>
                  </div>

                  {/* 交付时间 */}
                  <div className="grid grid-cols-2 gap-4">
                    {/* 父成果名称 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">父成果</label>
                      <input
                        type="text"
                        value={parentDelivery?.name}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付时间 <span className="text-red-500">*</span>
                      </label>
                      <DateTimeInput
                        value={newDelivery.deliveryTime}
                        onChange={(e) => setNewDelivery({ ...newDelivery, deliveryTime: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${deliveryErrors.deliveryTime ? 'border-red-500' : 'border-gray-300'
                          }`}
                      />
                      {deliveryErrors.deliveryTime && (
                        <p className="text-red-500 text-sm mt-1">{deliveryErrors.deliveryTime}</p>
                      )}
                    </div>
                  </div>
                  {/* 文件上传区域 */}
                  <div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        附件
                      </label>
                      <label
                        htmlFor="add-child-delivery-file-upload"
                        className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                      >
                        <div className="flex flex-col items-center">
                          <svg
                            className="w-8 h-8 text-gray-400 mb-1"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <div className="text-sm text-gray-600">
                            <span>点击或拖拽文件到此处上传</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-0.5">支持任意文件格式</p>
                        </div>
                      </label>
                      <input
                        id="add-child-delivery-file-upload"
                        type="file"
                        className="hidden"
                        multiple
                        onChange={(e) => {
                          const newFiles = Array.from(e.target.files || []);
                          setNewDelivery(prev => ({
                            ...prev,
                            files: [...prev.files, ...newFiles]
                          }));
                        }}
                      />

                      {/* 已上传文件列表 */}
                      {newDelivery.files.length > 0 && (
                        <div className="mt-4">
                          <div className="max-h-[200px] overflow-y-auto space-y-2">
                            {newDelivery.files.map((file, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border border-gray-200"
                              >
                                <div className="flex items-center">
                                  <svg className="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                  </svg>
                                  <span className="text-sm text-gray-600">{file.name}</span>
                                </div>
                                <button
                                  onClick={() => {
                                    setNewDelivery(prev => ({
                                      ...prev,
                                      files: prev.files.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="p-1 hover:bg-gray-200 rounded-full"
                                  title="删除文件"
                                >
                                  <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowAddChildDeliveryModal(false)}
                >
                  取消
                </Button>
                <Button
                  onClick={handleCreateDelivery} // 可以复用原有的创建函数
                  style={{ backgroundColor: '#0070f3', color: 'white' }}
                >
                  保存
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

export default ProductDelivery;