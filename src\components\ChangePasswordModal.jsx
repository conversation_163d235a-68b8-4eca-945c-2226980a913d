import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Cross2Icon } from '@radix-ui/react-icons';
import { Button } from './ui/button';
import { userStore } from '../store/userStore';
import { AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';
import { changePasswordApi } from '../services/changePasswordModalService';

export const ChangePasswordModal = observer(({ isOpen, onClose }) => {
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [oldPasswordError, setOldPasswordError] = useState('');
  const [newPasswordError, setNewPasswordError] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const userData = userStore.getUserData();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setOldPasswordError('');
    setNewPasswordError('');

    if (!oldPassword || !newPassword) {
      setNewPasswordError('请填写所有必填项');
      return;
    }

    if (newPassword.length < 6) {
      setNewPasswordError('新密码长度不能少于6位');
      return;
    }

    // 验证当前密码是否正确
    if (oldPassword !== userData?.password) {
      setOldPasswordError('当前密码不对，请重新输入');
      return;
    }

    // 验证新密码是否与当前密码相同
    if (oldPassword === newPassword) {
      setOldPasswordError('新密码不能和当前密码相同');
      setNewPasswordError('新密码不能和当前密码相同');
      return;
    }

    try {
      // 调用修改密码接口
      await changePasswordApi.updatePassword({
        id: userData.id,
        username: userData.username,
        password: newPassword
      });
      
      await userStore.updatePassword(oldPassword, newPassword);
      onClose();
    } catch (err) {
      setNewPasswordError(err.message || '修改密码失败，请重试');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[400px]">
        <div className="p-6 border-b flex justify-between items-center">
          <h3 className="text-xl font-semibold">修改密码</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Cross2Icon className="w-4 h-4" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                用户名
              </label>
              <input
                type="text"
                value={userData?.username || ''}
                disabled
                className="w-full px-3 py-2 border rounded-lg bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                当前密码 <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type={showOldPassword ? "text" : "password"}
                  value={oldPassword}
                  onChange={(e) => setOldPassword(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                  placeholder="请输入当前密码"
                />
                <button
                  type="button"
                  onClick={() => setShowOldPassword(!showOldPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showOldPassword ? (
                    <AiOutlineEyeInvisible className="h-5 w-5" />
                  ) : (
                    <AiOutlineEye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {oldPasswordError && (
                <div className="text-red-500 text-sm">{oldPasswordError}</div>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                新密码 <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type={showNewPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                  placeholder="请输入新密码"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showNewPassword ? (
                    <AiOutlineEyeInvisible className="h-5 w-5" />
                  ) : (
                    <AiOutlineEye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {newPasswordError && (
                <div className="text-red-500 text-sm">{newPasswordError}</div>
              )}
            </div>
          </div>
          <div className="flex justify-end gap-2 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              取消
            </Button>
            <Button type="submit" style={{ backgroundColor: '#007bff', color: '#fff' }}>
              确认修改
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}); 