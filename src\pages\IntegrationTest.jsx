/**
 * 集成测试管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 集成测试用例的增删改查
 * 3. 测试用例文件上传和下载
 * 4. 模板文件管理
 * 5. 分页和搜索功能
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  CheckCircledIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { integrationTestApi, fileApi, projectApi, employeeApi } from '../services/IntegrationTestService';

// 项目状态显示映射
const getProjectStatusDisplay = (status) => {
  const statusMap = {
    0: { label: '未开始', color: 'bg-gray-100 text-gray-800' },
    1: { label: '进行中', color: 'bg-blue-100 text-blue-800' },
    2: { label: '已结束', color: 'bg-green-100 text-green-800' }
  };
  const statusInfo = statusMap[status] || { label: '未知状态', color: 'bg-gray-100 text-gray-800' };
  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
      {statusInfo.label}
    </span>
  );
};

// 错误消息组件
const ErrorMessage = ({ message }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
    <Cross2Icon className="w-4 h-4 text-red-500" />
    <div className="text-sm text-red-800">{message}</div>
  </div>
);

// 成功消息组件
const SuccessMessage = ({ message }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-green-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
    <CheckCircledIcon className="w-4 h-4 text-green-500" />
    <div className="text-sm text-green-800">{message}</div>
  </div>
);

// 配置选项
const testTypeOptions = [
  { value: 0, label: '软件集成' },
  { value: 1, label: '系统集成' },
  { value: 2, label: '软硬件集成' }
];

const resultTypeOptions = [
  { value: 0, label: '通过' },
  { value: 1, label: '未通过' }
];

// 显示映射函数
const getTypeDisplay = (type) => {
  const typeMap = {
    0: { label: '软件集成', color: 'bg-blue-100 text-blue-800' },
    1: { label: '系统集成', color: 'bg-green-100 text-green-800' },
    2: { label: '软硬件集成', color: 'bg-purple-100 text-purple-800' }
  };
  const typeInfo = typeMap[type] || { label: '未知类型', color: 'bg-gray-100 text-gray-800' };
  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
      {typeInfo.label}
    </span>
  );
};

const getResultTypeDisplay = (resultType) => {
  const resultMap = {
    0: { label: '通过', color: 'bg-green-100 text-green-800' },
    1: { label: '未通过', color: 'bg-red-100 text-red-800' }
  };
  const typeInfo = resultMap[resultType] || { label: '未知', color: 'bg-gray-100 text-gray-800' };
  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
      {typeInfo.label}
    </span>
  );
};

export const IntegrationTest = observer(() => {
  // 项目相关状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');

  // 测试用例相关状态
  const [testCases, setTestCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchCreator, setSearchCreator] = useState('');

  // 分页状态
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);

  // 模态框状态
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showTemplateUploadModal, setShowTemplateUploadModal] = useState(false);
  const [showTemplateListModal, setShowTemplateListModal] = useState(false);

  // 编辑和查看状态
  const [editingPlan, setEditingPlan] = useState(null);
  const [viewingPlan, setViewingPlan] = useState(null);
  const [deletingRequirement, setDeletingRequirement] = useState(null);

  // 员工和下拉框状态
  const [employees, setEmployees] = useState([]);
  const [loadingApprovers, setLoadingApprovers] = useState(false);
  const [isApproverDropdownOpen, setIsApproverDropdownOpen] = useState(false);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  // 新建测试表单状态
  const [newTest, setNewTest] = useState({
    projectName: '',
    testName: '',
    creator: '',
    module: '',
    testType: '',
    purpose: '',
    precondition: '',
    inputData: '',
    steps: '',
    expectedResult: '',
    attachments: []
  });

  // 模板文件状态
  const [templateFile, setTemplateFile] = useState(null);
  const [templateFileName, setTemplateFileName] = useState('integration_test_template.xlsx');
  const [templateFiles, setTemplateFiles] = useState([]);

  // 消息和错误状态
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [errors, setErrors] = useState({});

  // DOM引用
  const approverDropdownRef = useRef(null);
  const typeDropdownRef = useRef(null);

  // 消息处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 2000);
  };

  const handleSuccess = (message) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 2000);
  };

  // 项目相关函数
  const fetchProjects = useCallback(async (searchName = '') => {
    try {
      const data = await projectApi.getProjectList(searchName);
      setProjects(data);
      if (data.length > 0 && !selectedProject) {
        setSelectedProject(data[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      handleError('获取项目列表失败');
    }
  }, [selectedProject]);

  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    await fetchTestCases(project.id);
  };

  // 员工相关函数
  const fetchEmployees = useCallback(async () => {
    try {
      setLoadingApprovers(true);
      const data = await employeeApi.getEmployeeList();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表失败:', error);
      handleError('获取员工列表失败');
    } finally {
      setLoadingApprovers(false);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchEmployees();
  }, [fetchProjects, fetchEmployees]);

  const handleUpdatePlan = async () => {
    try {
      const formData = new FormData();

      const updateData = {
        id: editingPlan.id,
        projectId: selectedProject.id,
        name: editingPlan.name,
        module: editingPlan.module,
        type: editingPlan.type,
        number: editingPlan.number,
        purpose: editingPlan.purpose,
        condition: editingPlan.condition || '',
        input: editingPlan.input,
        step: editingPlan.step,
        expect: editingPlan.expect,
        result: editingPlan.actualResult,
        resultType: editingPlan.resultType,
        creatorId: editingPlan.creatorId,
        createdTime: editingPlan.createdTime,
        creatorName: editingPlan.creatorName,
        fileIds: [],
        projectFiles: []
      };

      if (editingPlan.projectFiles && editingPlan.projectFiles.length > 0) {
        updateData.projectFiles = editingPlan.projectFiles.map(file => ({
          id: file.id,
          name: file.name,
          path: file.path,
          type: file.type,
          size: file.size,
          otherId: file.otherId,
          uploadTime: file.uploadTime,
          uploaderId: file.uploaderId,
          description: file.description,
          module: file.module
        }));
      }

      const updateBlob = new Blob([JSON.stringify(updateData)], {
        type: 'application/json'
      });
      formData.append('testIntegration', updateBlob);

      if (editingPlan.newFiles && editingPlan.newFiles.length > 0) {
        editingPlan.newFiles.forEach(file => {
          formData.append('files', file);
        });
      }

      await integrationTestApi.updateTest(editingPlan.id, formData);


      setShowEditModal(false);
      setEditingPlan(null);
      await fetchTestCases(selectedProject.id);
    } catch (error) {
      console.error('更新集成测试失败:', error);
      handleError('更新集成测试失败');
    }
  };



  // 搜索和重置函数
  const handleSearch = async () => {
    await fetchTestCasesWithParams({
      name: searchQuery || '',
      creatorId: searchCreator || ''
    });
  };

  const handleReset = async () => {
    setSearchQuery('');
    setSearchCreator('');
    await fetchTestCasesWithParams({});
  };

  // 通用的获取测试用例函数
  const fetchTestCasesWithParams = async (extraParams = {}) => {
    try {
      const params = {
        projectId: selectedProject?.id || '',
        page: 0,
        size: 10,
        ...extraParams
      };

      const data = await integrationTestApi.getTestList(params);
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        creatorName: item.creatorName,
        number: item.number,
        type: item.type,
        createdTime: item.createdTime
      }));

      setTestCases(formattedData);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
      setPage(0);
    } catch (error) {
      console.error('获取集成测试列表失败:', error);
      handleError('获取集成测试列表失败');
    }
  };



  // 文件处理函数
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files || []);
    setNewTest(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const handleRemoveFile = (index) => {
    setNewTest(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };


  // 修改创建用例函数
  const handleCreateTest = async () => {
    const newErrors = {};
    if (!newTest.testName) newErrors.testName = '请输入测试名称';
    if (!newTest.creator) newErrors.creator = '请输入创建人';
    if (!newTest.testType) newErrors.testType = '请选择测试类型';

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    try {
      const selectedEmployee = employees.find(emp => emp.name === newTest.creator);

      const formData = new FormData();
      newTest.attachments.forEach(file => {
        formData.append('files', file);
      });

      const testIntegration = {
        projectId: selectedProject?.id || 0,
        name: newTest.testName,
        module: newTest.module,
        type: parseInt(newTest.testType),
        purpose: newTest.purpose,
        condition: newTest.precondition,
        input: newTest.inputData,
        step: newTest.steps,
        expect: newTest.expectedResult,
        creatorId: selectedEmployee?.id || 0,
        creatorName: newTest.creator
      };

      const testIntegrationBlob = new Blob([JSON.stringify(testIntegration)], {
        type: 'application/json'
      });
      formData.append('testIntegration', testIntegrationBlob);

      await integrationTestApi.createTest(formData);

      setShowNewModal(false);
      setNewTest({
        projectName: '',
        testName: '',
        creator: '',
        module: '',
        testType: '',
        purpose: '',
        precondition: '',
        inputData: '',
        steps: '',
        expectedResult: '',
        resultType: '',
        attachments: []
      });

      await fetchTestCases(selectedProject.id);

    } catch (error) {
      console.error('创建测试失败:', error);
      handleError('创建测试失败');
    }
  };

  // 获取测试用例列表
  const fetchTestCases = async (projectId) => {
    await fetchTestCasesWithParams({ projectId });
  };

  // 页码变化处理
  const handlePageChange = async (newPage) => {
    setPage(newPage);
    await fetchTestCases(selectedProject.id);
  };

  // 修改编辑按钮的点击处理函数
  const handleEditClick = async (id) => {
    try {
      const data = await integrationTestApi.getTestDetail(id);
      
      // 将获取的数据设置到编辑表单中
      setEditingPlan({
        id: data.id,
        name: data.name,
        creatorName: data.creatorName,
        creatorId: data.creatorId,
        type: data.type,
        number: data.number,
        module: data.module,
        purpose: data.purpose,
        condition: data.condition,
        input: data.input,
        step: data.step,
        expect: data.expect,
        actualResult: data.result,
        resultType: data.resultType,
        createdTime: data.createdTime,
        projectFiles: data.projectFiles || []
      });

      setShowEditModal(true);
    } catch (error) {
      console.error('获取集成测试详情失败:', error);
      handleError('获取集成测试详情失败');
    }
  };

  // 添加查看测试用例详情的函数
  const handleViewTestCase = async (id) => {
    try {
      const data = await integrationTestApi.getTestDetail(id);
      setViewingPlan(data);
      setShowViewModal(true);
    } catch (error) {
      console.error('获取集成测试详情失败:', error);
      handleError('获取集成测试详情失败');
    }
  };

  // 修改文件预览函数
  const handleFilePreview = async (file) => {
    try {
      const previewUrl = await fileApi.previewFile(file.name, 'testintegration');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败');
    }
  };



  // 修改删除测试用例的函数
  const handleDeleteTestCase = async () => {
    try {
      await integrationTestApi.deleteTest(deletingRequirement.id);
      setShowDeleteModal(false);
      setDeletingRequirement(null);
      await fetchTestCases(selectedProject.id);
    } catch (error) {
      console.error('删除集成测试失败:', error);
      handleError('删除集成测试失败');
    }
  };

  // 修改文件下载函数
  const handleFileDownload = async (file) => {
    try {
      const response = await fileApi.downloadFile(file.name, 'testintegration');
      if (!response.ok) {
        throw new Error('文件下载失败');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('文件下载失败:', error);
      handleError('文件下载失败');
    }
  };

  // 修改模板下载函数
  const handleTemplateDownload = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName, 'model');
      if (!response.ok) {
        throw new Error('模板下载失败');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('模板下载失败:', error);
      handleError('模板下载失败');
    }
  };

  // 修改模板上传函数
  const handleTemplateUpload = async () => {
    if (!templateFile) return;

    try {
      const formData = new FormData();
      formData.append('file', templateFile);
      formData.append('bucketName', 'model');

      const fileName = await fileApi.uploadFile(formData);
      localStorage.setItem('integrationTestTemplateFileName', fileName);
      setTemplateFileName(fileName);

      handleSuccess('模板上传成功');
      setShowTemplateUploadModal(false);
      setTemplateFile(null);
    } catch (error) {
      console.error('模板上传失败:', error);
      handleError('模板上传失败');
    }
  };

  // 模板文件名本地存储管理
  useEffect(() => {
    const savedFileName = localStorage.getItem('integrationTestTemplateFileName');
    if (savedFileName) {
      setTemplateFileName(savedFileName);
    }
  }, []);

  useEffect(() => {
    if (templateFileName) {
      localStorage.setItem('integrationTestTemplateFileName', templateFileName);
    }
  }, [templateFileName]);



  // 修改添加按钮的点击处理函数
  const handleAddClick = () => {
    // 检查是否有选中的项目
    if (!selectedProject) {
      handleError('请先选择项目');
      return;
    }

    // 设置新测试的初始值，包含选中项目的信息
    setNewTest(prev => ({
      ...prev,
      projectName: selectedProject.name,
      projectId: selectedProject.id
    }));

    setShowNewModal(true);
  };

  // 获取模板文件列表
  const fetchTemplateFiles = async () => {
    try {
      const data = await fileApi.getAllFileNames('model');
      setTemplateFiles(data);
    } catch (error) {
      console.error('获取模板文件列表失败:', error);
      handleError('获取模板文件列表失败');
    }
  };

  // 修改模板预览函数
  const handleTemplatePreview = async (fileName) => {
    try {
      const previewUrl = await fileApi.previewFile(fileName, 'model');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败');
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (approverDropdownRef.current && !approverDropdownRef.current.contains(event.target)) {
        setIsApproverDropdownOpen(false);
      }
      if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setErrorMessage('')}
        />
      )}

      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="space-y-2">
          {projects.map(project => (
            <div
              key={project.id}
              onClick={() => handleProjectSelect(project)}
              className={`p-2 rounded-lg cursor-pointer hover:bg-blue-50 ${selectedProject?.id === project.id ? 'bg-blue-50' : ''}`}
            >
              <div className="flex flex-col gap-2">
                <span>{project.name}</span>
                <div>{getProjectStatusDisplay(project.status)}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">测试用例</p>
            </div>

          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="搜索用例名称"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              <div className="w-[150px]">
                <select
                  value={searchCreator}
                  onChange={(e) => setSearchCreator(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部创建人</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>{employee.name}</option>
                  ))}
                </select>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleSearch}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  搜索
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="px-4 py-2"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white"
                  onClick={handleAddClick}
                >
                  <PlusIcon className="w-4 h-4" />
                  添加集成测试
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center gap-1"
                  onClick={() => {
                    setShowTemplateListModal(true);
                    fetchTemplateFiles();
                  }}
                >
                  <DownloadIcon className="w-4 h-4" />
                  下载模版
                </Button>
                {/* 添加上传模板按钮 */}
                <div className="relative">
                  <Button
                    variant="outline"
                    className="flex items-center gap-1 bg-blue-500 text-white"
                    onClick={() => setShowTemplateUploadModal(true)}
                  >
                    上传模版
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">集成测试名称</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建人</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">集成用例编码</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">集成用例类型</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建时间</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody>
                {testCases.map((testCase) => (
                  <tr key={testCase.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.name}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.creatorName}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.number}</td>
                    <td className="px-4 py-3 text-sm text-center">
                      {getTypeDisplay(testCase.type)}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.createdTime}</td>
                    <td className="px-4 py-3 text-sm text-center">
                      <div className="flex items-center justify-center space-x-3">
                        <button
                          onClick={() => handleViewTestCase(testCase.id)}
                          className="text-blue-500 hover:text-blue-700"
                          title="查看"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditClick(testCase.id)}
                          className="text-gray-400 hover:text-blue-700"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setDeletingRequirement(testCase);
                            setShowDeleteModal(true);
                          }}
                          className="text-gray-400 hover:text-red-500"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex items-center justify-end px-4 py-3 border-t">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 mr-4">
                  共 {totalElements} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.max(0, page - 1))}
                  disabled={page === 0}
                  className="px-2 py-1 text-sm"
                >
                  上一页
                </Button>
                <div className="flex items-center">
                  <button
                    className={`px-3 py-1 text-sm rounded-lg ${page === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                    onClick={() => handlePageChange(0)}
                  >
                    1
                  </button>
                  {page > 2 && <span className="px-2 text-gray-500">...</span>}
                  {page > 1 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page - 1)}
                    >
                      {page}
                    </button>
                  )}
                  {page > 0 && page < totalPages - 1 && (
                    <button
                      className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                    >
                      {page + 1}
                    </button>
                  )}
                  {page < totalPages - 2 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page + 1)}
                    >
                      {page + 2}
                    </button>
                  )}
                  {page < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                  {totalPages > 1 && (
                    <button
                      className={`px-3 py-1 text-sm rounded-lg ${page === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                      onClick={() => handlePageChange(totalPages - 1)}
                    >
                      {totalPages}
                    </button>
                  )}
                </div>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.min(totalPages - 1, page + 1))}
                  disabled={page >= totalPages - 1}
                  className="px-2 py-1 text-sm"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看测试用例</p>
          </div>
        </div>
      )}

      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加集成测试</h3>
              <button
                onClick={() => setShowNewModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={newTest.projectName}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newTest.testName}
                      onChange={(e) => setNewTest({ ...newTest, testName: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.testName ? 'border-red-500' : 'border-gray-300'
                        }`}
                      placeholder="请输入测试名称"
                    />
                    {errors.testName && <p className="text-red-500 text-sm">{errors.testName}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={approverDropdownRef}>
                      <div
                        onClick={() => !loadingApprovers && setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTest.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        } ${loadingApprovers ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        <span className={newTest.creator ? 'text-gray-900' : 'text-gray-400'}>
                          {loadingApprovers ? '加载中...' : (newTest.creator || '请选择创建人')}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isApproverDropdownOpen && !loadingApprovers && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewTest({ ...newTest, creator: employee.name });
                                  setErrors({ ...errors, creator: false });
                                  setIsApproverDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newTest.creator === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.creator && <p className="text-red-500 text-sm">{errors.creator}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={typeDropdownRef}>
                      <div
                        onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTest.testType ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTest.testType ? 'text-gray-900' : 'text-gray-400'}>
                          {newTest.testType ? testTypeOptions.find(t => t.value === Number(newTest.testType))?.label : '请选择测试类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTypeDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testTypeOptions.map(type => (
                              <div
                                key={type.value}
                                onClick={() => {
                                  setNewTest({ ...newTest, testType: type.value });
                                  setErrors({ ...errors, testType: false });
                                  setIsTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(newTest.testType) === type.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {type.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.testType && <p className="text-red-500 text-sm">{errors.testType}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试模块
                    </label>
                    <input
                      type="text"
                      value={newTest.module}
                      onChange={(e) => setNewTest({ ...newTest, module: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入测试模块"
                    />
                  </div>
                </div>

                {/* 第三行：预置条件 */}


                {/* 第四行：集成测试目的和输入数据 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <textarea
                      value={newTest.precondition}
                      onChange={(e) => setNewTest({ ...newTest, precondition: e.target.value })}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入预置条件..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试目的
                    </label>
                    <textarea
                      value={newTest.purpose}
                      onChange={(e) => setNewTest({ ...newTest, purpose: e.target.value })}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入测试目的..."
                    />
                  </div>

                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <textarea
                      value={newTest.inputData}
                      onChange={(e) => setNewTest({ ...newTest, inputData: e.target.value })}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入测试数据..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试步骤
                    </label>
                    <textarea
                      value={newTest.steps}
                      onChange={(e) => setNewTest({ ...newTest, steps: e.target.value })}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                      placeholder="请输入测试步骤..."
                    />
                  </div>

                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    预期结果
                  </label>
                  <textarea
                    value={newTest.expectedResult}
                    onChange={(e) => setNewTest({ ...newTest, expectedResult: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                    placeholder="请输入预期结果..."
                  />
                </div>


                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <label
                    htmlFor="file-upload"
                    className="mt-1 flex justify-center px-6 py-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-500 transition-colors"
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-6 w-6 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex items-center justify-center text-sm text-gray-600">
                        <span className="font-medium text-blue-600 hover:text-blue-500">点击上传文件</span>
                        <span className="pl-1">或拖拽文件到这里</span>
                      </div>
                    </div>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      multiple
                      onChange={handleFileUpload}
                    />
                  </label>
                </div>

                {/* 显示已上传的文件列表 */}
                {newTest.attachments.length > 0 && (
                  <div className="mt-4 space-y-2 max-h-[80px] overflow-y-auto pr-2">
                    {newTest.attachments.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-2 last:mb-0"
                      >
                        <div className="flex items-center">
                          <FileTextIcon className="w-4 h-4 text-gray-500 mr-2" />
                          <span className="text-sm text-gray-700">{file.name}</span>
                        </div>
                        <button
                          onClick={() => handleRemoveFile(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateTest}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看集成测试</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 第一行：项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>

                {/* 第二行：预置条件 */}

                {/* 第三行：集成测试名称和创建人 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试名称
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg">
                      {viewingPlan.name}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg">
                      {viewingPlan.creatorName}
                    </div>
                  </div>
                </div>

                {/* 第四行：集成测试类型和模块 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试类型
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg">
                      {getTypeDisplay(viewingPlan.type)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成用例编码
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.number || ''}
                    </div>
                  </div>
                </div>

                {/* 第五行：集成测试目的和输入数据 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.condition || ''}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.input}
                    </div>
                  </div>
                </div>

                {/* 第六行：测试步骤和预期结果 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试目的
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.purpose}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试步骤
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.step}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试模块
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg">
                      {viewingPlan.module}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预期结果
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.expect}
                    </div>
                  </div>


                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      实际结果类型
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {getResultTypeDisplay(viewingPlan.resultType)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      实际结果
                    </label>
                    <div className="text-sm text-gray-900 p-2 rounded-lg whitespace-pre-wrap">
                      {viewingPlan.result}
                    </div>
                  </div>
                </div>

                {/* 第七行：已上传文件 */}
                {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      已上传文件
                    </label>
                    <div className="space-y-2">
                      {viewingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleFileDownload(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="下载"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑集成测试</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-2">
                {/* 第一行：项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>

                {/* 第二行：预置条件 */}


                {/* 第三行：集成测试名称和创建人 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试名称
                    </label>
                    <input
                      type="text"
                      value={editingPlan.name}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="relative" ref={approverDropdownRef}>
                      <div
                        onClick={() => setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.creatorName ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.creatorName ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.creatorName || '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isApproverDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingPlan(prev => ({
                                    ...prev,
                                    creatorName: employee.name,
                                    creatorId: employee.id
                                  }));
                                  setIsApproverDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.creatorName === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 第四行：集成测试类型和模块 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试类型
                    </label>
                    <div className="relative" ref={typeDropdownRef}>
                      <div
                        onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.type && editingPlan.type !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.type !== undefined && editingPlan.type !== null ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.type !== undefined && editingPlan.type !== null ? testTypeOptions.find(t => t.value === editingPlan.type)?.label : '请选择测试类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTypeDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testTypeOptions.map(type => (
                              <div
                                key={type.value}
                                onClick={() => {
                                  setEditingPlan(prev => ({
                                    ...prev,
                                    type: type.value
                                  }));
                                  setIsTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.type === type.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {type.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试模块
                    </label>
                    <input
                      type="text"
                      value={editingPlan.module}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, module: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* 第五行：集成测试目的和输入数据 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <textarea
                      value={editingPlan.condition || ''}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, condition: e.target.value }))}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入预置条件..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      集成测试目的
                    </label>
                    <textarea
                      value={editingPlan.purpose}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, purpose: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={1}
                    />
                  </div>

                </div>

                {/* 第六行：测试步骤和预期结果 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <textarea
                      value={editingPlan.input}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, input: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={1}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试步骤
                    </label>
                    <textarea
                      value={editingPlan.step}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, step: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={1}
                    />
                  </div>

                </div>

                {/* 第七行：实际结果和结果类型 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预期结果
                    </label>
                    <textarea
                      value={editingPlan.expect}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, expect: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={1}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结果类型
                    </label>
                    <select
                      value={editingPlan.resultType}
                      onChange={(e) => setEditingPlan(prev => ({ ...prev, resultType: parseInt(e.target.value) }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">请选择结果类型</option>
                      {resultTypeOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    实际结果
                  </label>
                  <textarea
                    value={editingPlan.actualResult}
                    onChange={(e) => setEditingPlan(prev => ({ ...prev, actualResult: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={1}
                  />
                </div>
                {/* 第八行：已上传文件 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    已上传文件
                  </label>
                  {editingPlan.projectFiles && editingPlan.projectFiles.length > 0 && (
                    <div className="space-y-2 mb-4 max-h-[100px] overflow-y-auto pr-2">
                      {editingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                setEditingPlan(prev => ({
                                  ...prev,
                                  projectFiles: prev.projectFiles.filter((_, i) => i !== index)
                                }));
                              }}
                              className="text-red-500 hover:text-red-700"
                              title="删除"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 文件上传区域 */}
                  <div className="space-y-4">
                    <div
                      className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50"
                      onClick={() => document.getElementById('fileInput').click()}
                    >
                      <input
                        id="fileInput"
                        type="file"
                        multiple
                        className="hidden"
                        onChange={(e) => {
                          const files = Array.from(e.target.files || []);
                          setEditingPlan(prev => ({
                            ...prev,
                            newFiles: [...(prev.newFiles || []), ...files]
                          }));
                          e.target.value = '';
                        }}
                      />
                      <FileTextIcon className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                      <div className="text-gray-500">
                        点击上传文件 或 拖拽文件到此处
                      </div>
                    </div>

                    {/* 新上传的文件列表 */}
                    {editingPlan.newFiles && editingPlan.newFiles.length > 0 && (
                      <div className="space-y-2 max-h-[100px] overflow-y-auto pr-2">
                        {editingPlan.newFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center text-sm text-gray-900">
                              <FileTextIcon className="w-4 h-4 mr-2" />
                              {file.name}
                            </div>
                            <button
                              onClick={() => {
                                setEditingPlan(prev => ({
                                  ...prev,
                                  newFiles: prev.newFiles.filter((_, i) => i !== index)
                                }));
                              }}
                              className="text-red-500 hover:text-red-700"
                              title="删除"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleUpdatePlan}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">确认删除</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-600">
                确定要删除测试用例 &quot;{deletingRequirement.name}&quot; 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingRequirement(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={handleDeleteTestCase}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {successMessage && <SuccessMessage message={successMessage} />}

      {showTemplateUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">上传模版</h3>
              <button
                onClick={() => {
                  setShowTemplateUploadModal(false);
                  setTemplateFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="template-upload"
                    className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                  >
                    <div className="space-y-1 text-center">
                      <FileTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <span className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                          点击上传模版文件
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">支持.xlsx,.xls,.doc,.docx,.pdf格式</p>
                    </div>
                    <input
                      id="template-upload"
                      type="file"
                      className="hidden"
                      accept=".xlsx,.xls,.doc,.docx,.pdf"
                      onChange={(e) => setTemplateFile(e.target.files[0])}
                    />
                  </label>
                </div>

                {/* 显示已选择的文件 */}
                {templateFile && (
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div className="flex items-center text-sm text-gray-900">
                      <FileTextIcon className="w-4 h-4 mr-2" />
                      {templateFile.name}
                    </div>
                    <button
                      onClick={() => setTemplateFile(null)}
                      className="text-red-500 hover:text-red-700"
                      title="删除"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowTemplateUploadModal(false);
                  setTemplateFile(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={handleTemplateUpload}
                disabled={!templateFile}
                className={!templateFile ? 'opacity-50 cursor-not-allowed' : ''}
              >
                上传
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 添加模板列表弹窗 */}
      {showTemplateListModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">模板列表</h3>
              <button
                onClick={() => setShowTemplateListModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-2">
                {templateFiles.map((fileName, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center">
                      <FileTextIcon className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-gray-900">{fileName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleTemplatePreview(fileName)}
                        className="text-blue-500 hover:text-blue-700"
                        title="预览"
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleTemplateDownload(fileName)}
                        className="text-blue-500 hover:text-blue-700"
                        title="下载"
                      >
                        <DownloadIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
                {templateFiles.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    暂无模板文件
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowTemplateListModal(false)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

/**
 * 代码简化总结：
 *
 * 1. 状态管理优化：
 *    - 合并了相似的状态变量
 *    - 删除了未使用的状态（如 mockRequirements, testPlans 等）
 *    - 重新组织状态分类，提高可读性
 *
 * 2. 函数简化：
 *    - 合并了重复的函数（如 fetchTestCases 和 fetchTestCasesWithParams）
 *    - 删除了冗余的消息处理组件
 *    - 使用 useCallback 优化性能和依赖项管理
 *
 * 3. 代码结构优化：
 *    - 添加了详细的中文注释
 *    - 按功能模块重新组织代码
 *    - 简化了错误处理逻辑
 *
 * 4. 去除的冗余部分：
 *    - 删除了未使用的 mock 数据
 *    - 合并了重复的文件处理函数
 *    - 简化了搜索和重置逻辑
 *    - 删除了重复的状态声明
 */