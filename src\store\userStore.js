import { makeAutoObservable } from 'mobx';
import { navigationStore } from './navigationStore';
import { repositoryService } from '../services/repositoryService';

class UserStore {
  // 默认为未登录状态
  isAuthenticated = false;
  currentPage = '/';
  token = null;
  userData = null;
  user = {
    username: '',
    email: '',
    avatar: null,
    settings: {
      notifications: true,
      theme: 'light',
      language: 'zh-CN'
    }
  };

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
    
    // 检查初始认证状态
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('userData');
    const isAuth = localStorage.getItem('isAuthenticated');
    
    // 根据存储的状态初始化
    if (token && isAuth === 'true') {
      this.isAuthenticated = true;
      this.token = token;
      this.userData = userData ? JSON.parse(userData) : null;
    }
  }

  clearAuthState() {
    this.isAuthenticated = false;
    this.token = null;
    this.userData = null;
    localStorage.removeItem('token');
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userData');
    localStorage.removeItem('currentSystem');
    localStorage.removeItem('currentSubPage');
    localStorage.removeItem('lastPath');
    localStorage.removeItem('currentPage');
  }

  setIsAuthenticated(status) {
    this.isAuthenticated = status;
    if (!status) {
      this.clearAuthState();
    }
  }

  login(token, userData) {
    this.isAuthenticated = true;
    this.token = token;
    this.userData = userData;
    
    // 保存到本地存储
    localStorage.setItem('token', token);
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('userData', JSON.stringify(userData));
    
    // 设置 repository service 的 API Token
    // repositoryService.setApiToken(token);
    
    // 设置新的认证状态
    this.setIsAuthenticated(true);
    
    // 设置默认导航状态
    navigationStore.setCurrentSystem('项目管理');
    navigationStore.setCurrentSubPage('项目创建');
    
    // 保存到 localStorage
    localStorage.setItem('currentSystem', '项目管理');
    localStorage.setItem('currentSubPage', '项目创建');
  }

  logout() {
    this.setIsAuthenticated(false);
  }

  setCurrentPage(page) {
    this.currentPage = page;
    localStorage.setItem('currentPage', page);
  }

  updatePassword(oldPassword, newPassword) {
    // In a real application, this would make an API call
    console.log('Password updated');
  }

  updateSettings(settings) {
    this.user.settings = { ...this.user.settings, ...settings };
  }

  // 获取用户数据
  getUserData() {
    return this.userData;
  }
}

export const userStore = new UserStore();