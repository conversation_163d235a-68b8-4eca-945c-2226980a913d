import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0', // 绑定到所有网络接口
    port: 3000,      // 可以指定其他端口
    proxy: {
      '/api': {
        target: 'http://************:8085',
        changeOrigin: true,
      }
    },
    historyApiFallback: true
  },
  define: {
    'process.env.VITE_API_BASE_URL': JSON.stringify('http://************:8085')
  }
})

