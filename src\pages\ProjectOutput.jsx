import React, { useState, useEffect, useCallback, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  DownloadIcon,
  TrashIcon,
  EyeOpenIcon,
  FileTextIcon,
  Cross2Icon,
  PlusIcon,
  UploadIcon,
  Pencil1Icon
} from '@radix-ui/react-icons';
import { projectApi, projectOutputApi, fileApi, employeeApi } from '../services/projectOutputService';

/**
 * 项目输出管理页面
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 项目输出文件的增删改查
 * 3. 文件上传、下载、预览、打包
 * 4. 审批流程管理
 */

// 输出类型映射
const deliverableTypes = [
  { id: 0, name: '文档' },
  { id: 1, name: '代码' },
  { id: 2, name: '报告' },
  { id: 3, name: '设计' }
];

// 项目类型映射
const typeMap = {
  0: '软件开发',
  1: '市场调研',
  2: '产品设计',
  3: '服务项目'
};

// 项目状态映射
const projectStatusMap = {
  0: { text: '未开始', color: 'text-gray-600 bg-gray-100' },
  1: { text: '进行中', color: 'text-blue-600 bg-blue-100' },
  2: { text: '已结束', color: 'text-green-600 bg-green-100' }
};

// 错误提示样式
const errorMessageStyle = {
  position: 'fixed',
  left: '50%',
  top: '20px',
  transform: 'translateX(-50%)',
  display: 'flex',
  alignItems: 'center',
  padding: '12px 16px',
  backgroundColor: '#fff2f0',
  border: '1px solid #ffccc7',
  borderRadius: '4px',
  boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
  zIndex: 1000,
  minWidth: '240px',
  maxWidth: '480px'
};



export const ProjectOutput = observer(() => {
  // 基础状态
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);

  // 输出文件相关状态
  const [deliverables, setDeliverables] = useState([]);
  const [deliverablesLoading, setDeliverablesLoading] = useState(false);
  const [deliverableSearchQuery, setDeliverableSearchQuery] = useState('');

  // 模态框状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showProjectDetailModal, setShowProjectDetailModal] = useState(false);

  // 表单数据
  const [selectedDeliverable, setSelectedDeliverable] = useState(null);
  const [newDeliverable, setNewDeliverable] = useState({
    name: '',
    type: '',
    description: '',
    approver: '',
    deliveryDate: '',
    files: []
  });
  const [editingDeliverable, setEditingDeliverable] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState([]);

  // 其他数据
  const [approvers, setApprovers] = useState([]);
  const [projectDetail, setProjectDetail] = useState(null);
  const [allProjects, setAllProjects] = useState([]);
  const [deleteId, setDeleteId] = useState(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 表单验证和UI状态
  const [formErrors, setFormErrors] = useState({
    name: false,
    type: false,
    approver: false,
    deliveryDate: false
  });
  const [errorMessage, setErrorMessage] = useState(null);
  const [isApproverDropdownOpen, setIsApproverDropdownOpen] = useState(false);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  // DOM引用
  const typeDropdownRef = useRef(null);
  const approverDropdownRef = useRef(null);
  const editTypeDropdownRef = useRef(null);
  const editApproverDropdownRef = useRef(null);

  // 显示错误消息
  const showError = useCallback((message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(null), 3000);
  }, []);

  // 获取项目输出列表
  const fetchProjectDeliverables = useCallback(async (projectId, name = '') => {
    setDeliverablesLoading(true);
    try {
      const data = await projectOutputApi.getProjectOutputList(projectId, name);
      const formattedData = data.map(item => ({
        id: item.id,
        name: item.name,
        type: item.type,
        deliveryDate: item.payDate,
        status: item.status,
        approver: item.approverName,
        feedback: item.approvalComment,
        description: item.description,
        number: item.number,
        files: item.files || [],
        projectId: item.projectId,
        approverId: item.approverId
      }));
      setDeliverables(formattedData);
    } catch (err) {
      console.error('获取项目输出列表错误:', err);
      showError('获取项目输出列表失败');
    } finally {
      setDeliverablesLoading(false);
    }
  }, [showError]);

  // 获取项目列表
  const fetchProjects = useCallback(async (searchName = '') => {
    try {
      setLoading(true);
      const data = await projectApi.getProjectList(searchName);
      setProjects(data);
      setAllProjects(data);

      if (data.length > 0) {
        setSelectedProject(data[0]);
        fetchProjectDeliverables(data[0].id);
      }
    } catch (error) {
      console.error('获取项目列表错误:', error);
      showError('获取项目列表失败');
    } finally {
      setLoading(false);
    }
  }, [showError, fetchProjectDeliverables]);


  // 搜索防抖定时器
  const searchTimeoutRef = useRef(null);

  // 搜索框变化处理 - 带防抖
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);

    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器
    searchTimeoutRef.current = setTimeout(() => {
      fetchProjects(value);
    }, 300);
  };



  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    if (project) {
      fetchProjectDeliverables(project.id);
    } else {
      setDeliverables([]);
    }
  };

  // 获取审批人列表
  const fetchApprovers = useCallback(async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      setApprovers(data);
    } catch (error) {
      console.error('获取审批人列表错误:', error);
      showError('获取审批人列表失败');
    }
  }, [showError]);

  // 组件初始化时获取数据
  useEffect(() => {
    fetchProjects();
    fetchApprovers();
  }, [fetchProjects, fetchApprovers]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 新建弹窗的下拉选择
      if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
      if (approverDropdownRef.current && !approverDropdownRef.current.contains(event.target)) {
        setIsApproverDropdownOpen(false);
      }

      // 编辑弹窗的下拉选择
      if (editTypeDropdownRef.current && !editTypeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
      if (editApproverDropdownRef.current && !editApproverDropdownRef.current.contains(event.target)) {
        setIsApproverDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 组件卸载时清理搜索定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleViewDetail = async (deliverable) => {
    try {
      setDetailLoading(true);
      const data = await projectOutputApi.getProjectOutputDetail(deliverable.id);
      
      const formattedData = {
        ...data,
        files: data.projectFiles || [],
        approverName: data.approverName || '未指定',
        description: data.description || '暂无描述',
        approvalComment: data.approvalComment || '',
        status: data.status || 'PENDING',
        payDate: data.payDate || '',
        type: data.type
      };
      
      setSelectedDeliverable(formattedData);
      setShowDetailModal(true);
    } catch (err) {
      console.error('获取项目输出详情错误:', err);
      showError('获取详情失败：' + err.message);
    } finally {
      setDetailLoading(false);
    }
  };




  // 重置表单数据
  const resetFormData = () => {
    setSelectedFiles([]);
    setFormErrors({
      name: false,
      type: false,
      approver: false,
      deliveryDate: false
    });
  };

  // 关闭新建模态框
  const handleCloseNewModal = () => {
    setNewDeliverable({
      name: '',
      type: '',
      description: '',
      approver: '',
      deliveryDate: '',
      files: []
    });
    resetFormData();
    setShowNewModal(false);
  };

  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  // 表单验证
  const validateForm = (data) => {
    const errors = {
      name: !data.name,
      type: !data.type && data.type !== 0,
      approver: !data.approver,
      deliveryDate: !data.deliveryDate
    };
    setFormErrors(errors);
    return !Object.values(errors).some(error => error);
  };

  // 创建项目输出
  const handleCreateDeliverable = async () => {
    if (!validateForm(newDeliverable)) return;

    try {
      const formData = new FormData();

      const requestData = {
        name: newDeliverable.name,
        type: Number(newDeliverable.type),
        payDate: newDeliverable.deliveryDate,
        approverId: parseInt(newDeliverable.approver),
        approvalComment: newDeliverable.feedback || '',
        status: 'PENDING',
        description: newDeliverable.description || '',
        projectId: selectedProject.id,
        approverName: approvers.find(a => a.id === parseInt(newDeliverable.approver))?.name || ''
      };

      formData.append('projectIn', new Blob([JSON.stringify(requestData)], {
        type: 'application/json'
      }));

      selectedFiles.forEach(file => {
        formData.append('files', file);
      });

      await projectOutputApi.createProjectOutput(formData);
      await fetchProjectDeliverables(selectedProject.id);
      handleCloseNewModal();
    } catch (err) {
      console.error('创建项目输出错误:', err);
      showError('创建失败');
    }
  };


  // 确认删除输出文件
  const handleConfirmDelete = async () => {
    try {
      setDeliverablesLoading(true);
      const result = await projectOutputApi.deleteProjectOutput(deleteId);
      if (result === true) {
        await fetchProjectDeliverables(selectedProject.id);
      } else {
        showError('删除失败');
      }
    } catch (err) {
      console.error('删除项目输出错误:', err);
      showError('删除失败');
    } finally {
      setDeliverablesLoading(false);
      setShowConfirmDialog(false);
      setDeleteId(null);
    }
  };

  // 打开编辑模态框
  const handleEdit = async (deliverable) => {
    try {
      resetFormData();
      setShowEditModal(true);

      const data = await projectOutputApi.getProjectOutputDetail(deliverable.id);

      setEditingDeliverable({
        id: data.id,
        name: data.name,
        type: data.type,
        description: data.description || '',
        approver: data.approverId,
        deliveryDate: data.payDate,
        files: data.projectFiles || [],
        projectId: data.projectId,
        projectName: data.projectName
      });
    } catch (error) {
      console.error('获取编辑详情失败:', error);
      showError('获取编辑详情失败');
      setShowEditModal(false);
    }
  };

  // 关闭编辑模态框
  const handleCloseEditModal = () => {
    setEditingDeliverable(null);
    resetFormData();
    setShowEditModal(false);
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    if (!validateForm(editingDeliverable)) return;

    try {
      const formData = new FormData();

      const requestData = {
        id: editingDeliverable.id,
        name: editingDeliverable.name,
        number: editingDeliverable.number || '',
        payDate: editingDeliverable.deliveryDate,
        approverId: parseInt(editingDeliverable.approver),
        approvalComment: editingDeliverable.feedback || '',
        status: editingDeliverable.status || 'PENDING',
        description: editingDeliverable.description || '',
        projectId: editingDeliverable.projectId,
        type: Number(editingDeliverable.type),
        approverName: approvers.find(a => a.id === parseInt(editingDeliverable.approver))?.name || '',
        projectFiles: editingDeliverable.files?.map(file => ({
          id: file.id,
          name: file.name,
          path: file.path,
          type: file.type || 0,
          size: file.size || 0,
          otherId: file.otherId || 0,
          uploadTime: file.uploadTime || new Date().toISOString(),
          uploaderId: file.uploaderId || 0,
          description: file.description || '',
          module: file.module || ''
        })) || []
      };

      formData.append('projectOut', new Blob([JSON.stringify(requestData)], {
        type: 'application/json'
      }));

      selectedFiles.forEach(file => {
        formData.append('files', file);
      });

      await projectOutputApi.updateProjectOutput(editingDeliverable.id, formData);
      await fetchProjectDeliverables(selectedProject.id);
      handleCloseEditModal();
    } catch (err) {
      console.error('更新项目输出错误:', err);
      showError('更新失败：' + err.message);
    }
  };



  // 添加搜索处理函数
  const handleDeliverableSearch = () => {
    if (selectedProject) {
      fetchProjectDeliverables(selectedProject.id, deliverableSearchQuery);
    }
  };

  // 添加重置处理函数
  const handleReset = () => {
    setDeliverableSearchQuery('');
    if (selectedProject) {
      fetchProjectDeliverables(selectedProject.id, '');
    }
  };

  // 下载文件
  const handleDownload = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName);

      if (!response.ok) {
        throw new Error('下载文件失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载文件错误:', error);
      showError('下载文件失败');
    }
  };

  // 打包下载文件
  const handleArchive = async (deliverable) => {
    try {
      const blob = await projectOutputApi.archiveProjectOutput(deliverable.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${deliverable.name}_打包文件.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('打包文件错误:', error);
      showError('打包文件失败');
    }
  };

  // 全量打包所有文件
  const handleArchiveAll = async () => {
    try {
      const blob = await fileApi.archiveAllFiles();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = '项目输出文件.zip';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // 显示成功提示
      const successDiv = document.createElement('div');
      successDiv.className = 'fixed top-4 left-1/2 -translate-x-1/2 bg-white rounded px-3 py-1.5 shadow-md flex items-center z-50';
      successDiv.innerHTML = `
        <svg class="w-4 h-4 mr-1.5 text-green-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="currentColor"/>
          <path d="M8 12l3 3 5-5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="text-gray-800">全量打包成功</span>
      `;
      document.body.appendChild(successDiv);
      setTimeout(() => successDiv.remove(), 3000);
    } catch (error) {
      console.error('全量打包错误:', error);
      showError('全量打包失败');
    }
  };

  return (
    <>
      {/* 全局错误提示组件 */}
      {errorMessage && (
        <div style={errorMessageStyle}>
          <Cross2Icon className="w-4 h-4 text-red-500 mr-2" />
          <span>{errorMessage}</span>
        </div>
      )}

      {/* 主容器：左右分栏布局 */}
      <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-[calc(100vh)]" >
        {/* 左侧项目列表区域 */}
        <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
          {/* 项目搜索头部 */}
          <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
            <div className="flex-1 relative">
              <input
                type="text"
                placeholder="搜索项目..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
            </div>
          </div>
          {/* 项目列表内容区域 */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">加载中...</div>
            ) : projects.length === 0 ? (
              <div className="p-4 text-center text-gray-500">暂无项目数据</div>
            ) : (
              projects.map(project => (
                <div
                  key={project.id}
                  className={`p-2 cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between ${
                    selectedProject?.id === project.id ? 'bg-blue-50' : ''
                  }`}
                >
                  <div 
                    className="flex-1 flex flex-col"
                    onClick={() => handleProjectSelect(project)}
                  >
                    <span className="font-medium">{project.name}</span>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`text-xs px-2 py-0.5 rounded-full ${projectStatusMap[project.status]?.color}`}>
                        {projectStatusMap[project.status]?.text}
                      </span>
                    </div>
                  </div>

                </div>
              ))
            )}
          </div>
        </div>

        {/* 右侧输出文件管理区域 */}
        <div className="flex-1 flex flex-col">
          {/* 页面标题 */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">
              {selectedProject ? `${selectedProject.name} - 输出文件` : '项目输出文件'}
            </h2>
          </div>

          {/* 操作工具栏：搜索、重置、打包、新建 */}
          <div className="mb-4 flex items-center gap-2">
            <div className="w-96 relative">
              <input
                type="text"
                placeholder="搜索输出文件..."
                value={deliverableSearchQuery}
                onChange={(e) => setDeliverableSearchQuery(e.target.value)}
                className="w-full pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
            </div>
            <button
              onClick={handleDeliverableSearch}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center gap-1"
            >
              <MagnifyingGlassIcon className="w-4 h-4" />
              搜索
            </button>
            <button
              onClick={handleReset}
              className="px-4 py-2 border border-gray-300 hover:bg-gray-100 rounded-lg flex items-center gap-1"
            >
              重置
            </button>
            <button
              onClick={handleArchiveAll}
              className="px-4 py-2 border border-gray-300 hover:bg-gray-100 rounded-lg flex items-center gap-1"
            >
              <DownloadIcon className="w-4 h-4" />
              全量打包
            </button>
            <button
              onClick={() => setShowNewModal(true)}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg flex items-center gap-1"
            >
              <PlusIcon className="w-4 h-4" />
              新建输出
            </button>
            <div className="flex-1" />
          </div>

          {/* 输出文件网格列表 */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="grid grid-cols-3 gap-6 pb-4">
              {deliverablesLoading ? (
                <div className="col-span-3 text-center py-8 text-gray-500">加载中...</div>
              ) : deliverables.length === 0 ? (
                <div className="col-span-3 text-center py-8 text-gray-500">暂无输出文件</div>
              ) : (
                deliverables.map(deliverable => (
                  <div
                    key={deliverable.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    {/* 文件图标 */}
                    <div className="flex justify-center mb-4">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                        <FileTextIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    </div>

                    {/* 文件基本信息 */}
                    <div className="space-y-2">
                      {/* 名称 */}
                      <div className="font-medium truncate" title={deliverable.name}>
                        {deliverable.name}
                      </div>

                      {/* 审批人 */}
                      {deliverable.approver && (
                        <div className="text-sm text-gray-500">
                          审批人: {deliverable.approver}
                        </div>
                      )}

                      {/* 交付日期 */}
                      <div className="text-sm text-gray-500">
                        交付日期: {deliverable.deliveryDate}
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex justify-between items-center pt-2 gap-2">
                        {/* 查看按钮 */}
                        <button
                          onClick={() => handleViewDetail(deliverable)}
                          className="flex-1 text-blue-600 hover:text-blue-700 text-sm flex items-center justify-center p-2 hover:bg-blue-50 rounded"
                          title="查看详情"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        
                        {/* 编辑按钮 */}
                        <button
                          onClick={() => handleEdit(deliverable)}
                          className="flex-1 text-blue-600 hover:text-blue-700 text-sm flex items-center justify-center p-2 hover:bg-blue-50 rounded"
                          title="编辑"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        
                        {/* 打包下载按钮 */}
                        <button
                          onClick={() => handleArchive(deliverable)}
                          className="flex-1 text-yellow-600 hover:text-yellow-700 text-sm flex items-center justify-center p-2 hover:bg-yellow-50 rounded"
                          title="打包下载"
                        >
                          <DownloadIcon className="w-4 h-4" />
                        </button>
                        
                        {/* 删除按钮 */}
                        <button
                          onClick={() => {
                            setDeleteId(deliverable.id);
                            setShowConfirmDialog(true);
                          }}
                          className="flex-1 text-red-500 hover:text-red-600 text-sm flex items-center justify-center p-2 hover:bg-red-50 rounded"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* 查看详情模态框 */}
        {showDetailModal && selectedDeliverable && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[500px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">查看项目输出</h3>
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              {detailLoading ? (
                <div className="p-6 text-center">
                  <div className="text-gray-500">加载中...</div>
                </div>
              ) : (
                <div className="p-6">
                  <div className="space-y-4">
                    {/* 将所属项目和输出类型放在同一行 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-gray-500 mb-1">所属项目</div>
                        <div className="font-medium">{selectedProject?.name || '未知项目'}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500 mb-1">输出类型</div>
                        <div className="font-medium">
                          {deliverableTypes.find(t => t.id === selectedDeliverable.type)?.name || '未知类型'}
                        </div>
                      </div>
                    </div>
                    
                    {/* 将状态和交付日期放在同一行 */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-gray-500 mb-1">交付日期</div>
                        <div className="font-medium">{selectedDeliverable.payDate}</div>
                      </div>
                      <div>
                      <div className="text-sm text-gray-500 mb-1">审批人</div>
                      <div className="font-medium">{selectedDeliverable.approverName}</div>
                    </div>
                    </div>

                    {/* 其他字段保持原样 */}

                    <div>
                      <div className="text-sm text-gray-500 mb-1">描述</div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        {selectedDeliverable.description}
                      </div>
                    </div>
                    {selectedDeliverable.approvalComment && (
                      <div>
                        <div className="text-sm text-gray-500 mb-1">审批意见</div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          {selectedDeliverable.approvalComment}
                        </div>
                      </div>
                    )}
                    
                    {/* 显示现有文件 */}
                    {selectedDeliverable.files && selectedDeliverable.files.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <div className="text-sm text-gray-500 mb-1">附件文件</div>
                        {selectedDeliverable.files.map(file => (
                          <div key={file.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-5 h-5 text-blue-500" />
                              <div>
                                <div className="font-medium">{file.name}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                className="p-2 text-green-600 hover:text-green-700 hover:bg-green-50 rounded"
                                onClick={() => {
                                  // 调用预览文件接口
                                  fileApi.previewFile(file.name)
                                    .then(url => {
                                      // 在新窗口打开预览URL
                                      window.open(url, '_blank');
                                    })
                                    .catch(error => {
                                      console.error('预览文件错误:', error);
                                      setErrorMessage('预览文件失败');
                                      setTimeout(() => {
                                        setErrorMessage(null);
                                      }, 3000);
                                    });
                                }}
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </button>
                              <button
                                className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded"
                                onClick={() => handleDownload(file.name)}
                              >
                                <DownloadIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
              <div className="p-6 border-t bg-gray-50 flex justify-end">
                <button
                  onClick={() => setShowDetailModal(false)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 新建输出文件模态框 */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">添加项目输出</h3>
                <button
                  onClick={handleCloseNewModal}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      所属项目
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 bg-gray-50 border rounded-lg text-gray-500"
                    />
                  </div>
                  {/* 将输出名称和输出类型放在同一行 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        输出名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={newDeliverable.name}
                        onChange={(e) => {
                          setNewDeliverable({ ...newDeliverable, name: e.target.value });
                          setFormErrors({ ...formErrors, name: false });
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.name ? 'border-red-500' : ''
                        }`}
                        placeholder="请输入输出名称"
                      />
                      {formErrors.name && (
                        <p className="text-red-500 text-xs mt-1">
                          请输入输出名称
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        输出类型 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={typeDropdownRef}>
                        <div
                          onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            newDeliverable.type === undefined || newDeliverable.type === '' ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newDeliverable.type !== undefined && newDeliverable.type !== '' ? 'text-gray-900' : 'text-gray-400'}>
                            {newDeliverable.type !== undefined && newDeliverable.type !== '' ? deliverableTypes.find(t => t.id === Number(newDeliverable.type))?.name : '请选择输出类型'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isTypeDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliverableTypes.map(type => (
                                <div
                                  key={type.id}
                                  onClick={() => {
                                    setNewDeliverable({ ...newDeliverable, type: type.id });
                                    setFormErrors({ ...formErrors, type: false });
                                    setIsTypeDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    newDeliverable.type !== '' && newDeliverable.type !== undefined && Number(newDeliverable.type) === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {type.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.type && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择输出类型
                        </p>
                      )}
                    </div>
                  </div>
                  {/* 将审批人和交付日期放在同一行 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        审批人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={approverDropdownRef}>
                        <div
                          onClick={() => setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-blue-500"
                        >
                          <span className={newDeliverable.approver ? 'text-gray-900' : 'text-gray-400'}>
                            {approvers.find(a => a.id === Number(newDeliverable.approver))?.name || '请选择审批人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isApproverDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {approvers.map(approver => (
                                <div
                                  key={approver.id}
                                  onClick={() => {
                                    setNewDeliverable({ ...newDeliverable, approver: approver.id });
                                    setFormErrors({ ...formErrors, approver: false });
                                    setIsApproverDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(newDeliverable.approver) === approver.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {approver.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.approver && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择审批人
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付日期 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        value={newDeliverable.deliveryDate}
                        onChange={(e) => {
                          setNewDeliverable({ ...newDeliverable, deliveryDate: e.target.value });
                          setFormErrors({ ...formErrors, deliveryDate: false });
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.deliveryDate ? 'border-red-500' : ''
                        }`}
                      />
                      {formErrors.deliveryDate && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择交付日期
                        </p>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描述
                    </label>
                    <textarea
                      value={newDeliverable.description}
                      onChange={(e) => setNewDeliverable({ ...newDeliverable, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入输出描述..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      上传文件
                    </label>
                    <label
                      htmlFor="file-upload"
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                    >
                      <div className="flex flex-col items-center">
                        <UploadIcon className="w-6 h-6 text-gray-400 mb-1" />
                        <input
                          type="file"
                          onChange={handleFileSelect}
                          className="hidden"
                          id="file-upload"
                          multiple
                        />
                        <p className="text-sm text-gray-600 hover:text-blue-500">
                          点击选择文件
                        </p>
                        <p className="text-xs text-gray-500 mt-0.5">
                          支持任意文件格式，可多选
                        </p>
                      </div>
                    </label>
                    {selectedFiles.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {selectedFiles.map((file, index) => (
                          <div key={index} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-5 h-5 text-blue-500" />
                                <div>
                                  <div className="font-medium">{file.name}</div>
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
                                }}
                                className="p-1 hover:bg-gray-200 rounded-full"
                              >
                                <Cross2Icon className="w-4 h-4 text-gray-500" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <button
                  onClick={handleCloseNewModal}
                  className="bg-white hover:bg-blue-600 text-black px-4 py-2 rounded"
                >
                  取消
                </button>
                <button onClick={handleCreateDeliverable} className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                  创建
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 编辑输出文件模态框 */}
        {showEditModal && editingDeliverable && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">编辑项目输出</h3>
                <button
                  onClick={handleCloseEditModal}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      所属项目
                    </label>
                    <input
                      type="text"
                      value={allProjects.find(p => p.id === editingDeliverable.projectId)?.name || ''}
                      disabled
                      className="w-full px-3 py-2 bg-gray-50 border rounded-lg text-gray-500"
                    />
                  </div>
                  {/* 将输出名称和输出类型放在同一行 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        输出名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={editingDeliverable.name}
                        onChange={(e) => {
                          setEditingDeliverable({ 
                            ...editingDeliverable, 
                            name: e.target.value 
                          });
                          setFormErrors({
                            ...formErrors,
                            name: false
                          });
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.name ? 'border-red-500' : ''
                        }`}
                        placeholder="请输入输出名称"
                      />
                      {formErrors.name && (
                        <p className="text-red-500 text-xs mt-1">
                          请输入输出名称
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        输出类型 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={editTypeDropdownRef}>
                        <div
                          onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            editingDeliverable.type === undefined || editingDeliverable.type === '' ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingDeliverable.type !== undefined && editingDeliverable.type !== '' ? 'text-gray-900' : 'text-gray-400'}>
                            {editingDeliverable.type !== undefined && editingDeliverable.type !== '' ? deliverableTypes.find(t => t.id === Number(editingDeliverable.type))?.name : '请选择输出类型'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isTypeDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {deliverableTypes.map(type => (
                                <div
                                  key={type.id}
                                  onClick={() => {
                                    setEditingDeliverable({
                                      ...editingDeliverable,
                                      type: type.id
                                    });
                                    setFormErrors({
                                      ...formErrors,
                                      type: false
                                    });
                                    setIsTypeDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    editingDeliverable.type !== '' && editingDeliverable.type !== undefined && Number(editingDeliverable.type) === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {type.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.type && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择输出类型
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 将审批人和交付日期放在同一行 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        审批人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={editApproverDropdownRef}>
                        <div
                          onClick={() => setIsApproverDropdownOpen(!isApproverDropdownOpen)}
                          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-blue-500"
                        >
                          <span className={editingDeliverable.approver ? 'text-gray-900' : 'text-gray-400'}>
                            {approvers.find(a => a.id === Number(editingDeliverable.approver))?.name || '请选择审批人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isApproverDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isApproverDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {approvers.map(approver => (
                                <div
                                  key={approver.id}
                                  onClick={() => {
                                    setEditingDeliverable({
                                      ...editingDeliverable,
                                      approver: approver.id
                                    });
                                    setFormErrors({
                                      ...formErrors,
                                      approver: false
                                    });
                                    setIsApproverDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    Number(editingDeliverable.approver) === approver.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {approver.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {formErrors.approver && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择审批人
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        交付日期 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        value={editingDeliverable.deliveryDate}
                        onChange={(e) => {
                          setEditingDeliverable({
                            ...editingDeliverable,
                            deliveryDate: e.target.value
                          });
                          setFormErrors({
                            ...formErrors,
                            deliveryDate: false
                          });
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          formErrors.deliveryDate ? 'border-red-500' : ''
                        }`}
                      />
                      {formErrors.deliveryDate && (
                        <p className="text-red-500 text-xs mt-1">
                          请选择交付日期
                        </p>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描述
                    </label>
                    <textarea
                      value={editingDeliverable.description}
                      onChange={(e) => setEditingDeliverable({
                        ...editingDeliverable,
                        description: e.target.value
                      })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入输出描述..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      附件文件
                    </label>
                    <label
                      htmlFor="edit-file-upload"
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                    >
                      <div className="flex flex-col items-center">
                        <UploadIcon className="w-6 h-6 text-gray-400 mb-1" />
                        <input
                          type="file"
                          onChange={(e) => {
                            const files = Array.from(e.target.files);
                            setSelectedFiles(prev => [...prev, ...files]);
                          }}
                          className="hidden"
                          id="edit-file-upload"
                        />
                        <p className="text-sm text-gray-600 hover:text-blue-500">
                          点击选择文件
                        </p>
                        <p className="text-xs text-gray-500 mt-0.5">
                          支持任意文件格式
                        </p>
                      </div>
                    </label>
                    
                    {/* 新选择的文件 */}
                    {selectedFiles.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {selectedFiles.map((file, index) => (
                          <div key={index} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-5 h-5 text-blue-500" />
                                <div>
                                  <div className="font-medium">{file.name}</div>
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  setSelectedFiles(selectedFiles.filter((_, i) => i !== index));
                                }}
                                className="p-1 hover:bg-gray-200 rounded-full"
                              >
                                <Cross2Icon className="w-4 h-4 text-gray-500" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 显示现有文件 */}
                    {editingDeliverable.files && editingDeliverable.files.length > 0 && (
                      <div className="mt-4">
                        <div className="text-sm text-gray-500 mb-1">现有文件</div>
                        <div className="max-h-[120px] overflow-y-auto">
                          <div className="space-y-2">
                            {editingDeliverable.files.map(file => (
                              <div key={file.id} className="flex items-center justify-between bg-gray-50 p-2 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <FileTextIcon className="w-5 h-5 text-blue-500" />
                                  <div>
                                    <div className="font-medium text-sm">{file.name}</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1">
                                  <button
                                    className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded"
                                    onClick={() => {
                                      fileApi.previewFile(file.name)
                                        .then(url => {
                                          window.open(url, '_blank');
                                        })
                                        .catch(error => {
                                          console.error('预览文件错误:', error);
                                          setErrorMessage('预览文件失败');
                                          setTimeout(() => {
                                            setErrorMessage(null);
                                          }, 3000);
                                        });
                                    }}
                                  >
                                    <EyeOpenIcon className="w-4 h-4" />
                                  </button>
                                  <button
                                    onClick={() => {
                                      const updatedFiles = editingDeliverable.files.filter(f => f.id !== file.id);
                                      setEditingDeliverable({
                                        ...editingDeliverable,
                                        files: updatedFiles
                                      });
                                    }}
                                    className="p-1 hover:bg-gray-200 rounded-full"
                                  >
                                    <TrashIcon className="w-3 h-3 text-red-500" />
                                  </button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <button
                  onClick={handleCloseEditModal}
                  className="px-4 py-2 border border-gray-300 hover:bg-gray-100 rounded-lg"
                >
                  取消
                </button>
                <button 
                  onClick={handleSaveEdit}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 确认删除对话框 */}
        {showConfirmDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[320px]">
              <div className="p-6">
                <h3 className="text-lg font-medium mb-2">确认删除</h3>
                <p className="text-gray-500 text-sm">确定要删除此任务吗？</p>
              </div>
              <div className="border-t flex">
                <button
                  onClick={() => {
                    setShowConfirmDialog(false);
                    setDeleteId(null);
                  }}
                  className="flex-1 p-3 hover:bg-gray-100 text-gray-600 text-sm font-medium"
                >
                  取消
                </button>
                <div className="w-px bg-gray-200"></div>
                <button
                  onClick={handleConfirmDelete}
                  className="flex-1 p-3 hover:bg-red-50 text-red-500 text-sm font-medium"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 项目详情模态框 */}
        {showProjectDetailModal && projectDetail && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[500px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">项目详情</h3>
                <button
                  onClick={() => setShowProjectDetailModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm text-gray-500">项目名称</label>
                    <div className="font-medium">{projectDetail.name}</div>
                  </div>
                  {/* <div>
                    <label className="text-sm text-gray-500">项目编号</label>
                    <div className="font-medium">{projectDetail.number}</div>
                  </div> */}
                  <div>
                    <label className="text-sm text-gray-500">项目类型</label>
                    <div className="font-medium">{typeMap[projectDetail.type]}</div>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">项目状态</label>
                    <div className="font-medium">{projectDetail.status}</div>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">开始日期</label>
                    <div className="font-medium">{projectDetail.startDate}</div>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">结束日期</label>
                    <div className="font-medium">{projectDetail.endDate}</div>
                  </div>
                  <div>
                    <label className="text-sm text-gray-500">项目描述</label>
                    <div className="bg-gray-50 p-4 rounded-lg mt-1">
                      {projectDetail.description || '暂无描述'}
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end">
                <button
                  onClick={() => setShowProjectDetailModal(false)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
});