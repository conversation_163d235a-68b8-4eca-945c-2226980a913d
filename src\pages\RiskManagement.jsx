import { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ExclamationTriangleIcon,
  Pencil1Icon,
  TrashIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { riskApi, projectApi, employeeApi } from '../services/riskManagementService';

/**
 * 风险管理页面
 * 主要功能：
 * 1. 项目列表展示和切换
 * 2. 风险列表展示和管理
 * 3. 风险的增删改查操作
 * 4. 风险等级、类型、状态管理
 * 5. 风险概览数据统计
 * 6. 风险搜索和筛选功能
 */

// 风险等级映射
const riskLevels = [
  { id: '0', name: '低', color: 'bg-green-100 text-green-800' },
  { id: '1', name: '中', color: 'bg-yellow-100 text-yellow-800' },
  { id: '2', name: '高', color: 'bg-red-100 text-red-800' }
];

// 风险类型映射
const riskTypes = [
  { id: '0', name: '技术风险' },
  { id: '1', name: '进度风险' },
  { id: '2', name: '成本风险' },
  { id: '4', name: '质量风险' }
];

// 风险状态映射
const riskStatus = [
  { id: '0', name: '未解决' },
  { id: '1', name: '已解决' }
];

export const RiskManagement = observer(() => {
  // 基础状态
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [showError, setShowError] = useState(false);

  // 数据状态
  const [risks, setRisks] = useState([]);
  const [projects, setProjects] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [overviewData, setOverviewData] = useState({
    sum: 0,
    hightLevelRisk: 0,
    resolvedRisk: 0,
    riskAtBeforeWeek: 0
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);
  const pageSize = 10;

  // 模态框状态
  const [showNewRiskModal, setShowNewRiskModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingRiskId, setDeletingRiskId] = useState(null);
  const [editingRisk, setEditingRisk] = useState(null);

  // 表单数据
  const [newRisk, setNewRisk] = useState({
    name: '',
    type: '',
    level: '',
    status: '0',
    description: '',
    impact: '',
    probability: '',
    response: '',
    owner: ''
  });

  // 下拉框状态
  const [isOwnerDropdownOpen, setIsOwnerDropdownOpen] = useState(false);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);
  const [isLevelDropdownOpen, setIsLevelDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [isEditTypeDropdownOpen, setIsEditTypeDropdownOpen] = useState(false);
  const [isEditLevelDropdownOpen, setIsEditLevelDropdownOpen] = useState(false);
  const [isEditStatusDropdownOpen, setIsEditStatusDropdownOpen] = useState(false);
  const [isEditOwnerDropdownOpen, setIsEditOwnerDropdownOpen] = useState(false);

  // 添加refs
  const ownerDropdownRef = useRef(null);
  const typeDropdownRef = useRef(null);
  const levelDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const editTypeDropdownRef = useRef(null);
  const editLevelDropdownRef = useRef(null);
  const editStatusDropdownRef = useRef(null);
  const editOwnerDropdownRef = useRef(null);

  // 显示错误消息
  const showErrorMessage = useCallback((message) => {
    setErrorMessage(message);
    setShowError(true);
    setTimeout(() => {
      setShowError(false);
    }, 3000);
  }, []);

  // 获取概览数据
  const fetchOverviewData = useCallback(async () => {
    try {
      const data = await riskApi.getRiskOverview();
      setOverviewData(data);
    } catch (error) {
      console.error('获取概览数据失败:', error);
      showErrorMessage('获取概览数据失败');
    }
  }, [showErrorMessage]);

  // 获取初始数据
  const fetchInitialData = useCallback(async () => {
    try {
      // 并行获取项目列表和风险列表
      const [projectsData, risksData] = await Promise.all([
        projectApi.getProjectList(),
        riskApi.getAllRisks(0, 10)
      ]);

      setProjects(projectsData);
      setRisks(risksData.content || []);
      setTotalPages(risksData.totalPages || 1);
      setTotalElements(risksData.totalElements || 0);
      setCurrentPage(1);
    } catch (error) {
      console.error('初始化数据失败:', error);
      showErrorMessage('初始化数据失败');
      setRisks([]);
    }
  }, [showErrorMessage]);

  // 获取员工列表
  const fetchEmployees = useCallback(async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表失败:', error);
      showErrorMessage('获取员工列表失败');
    }
  }, [showErrorMessage]);

  // 初始化数据
  useEffect(() => {
    fetchInitialData();
    fetchEmployees();
  }, [fetchInitialData, fetchEmployees]);

  // 获取风险列表
  const fetchRiskList = useCallback(async (projectId, page = currentPage - 1) => {
    try {
      const data = await riskApi.getAllRisks(page, pageSize, projectId);
      setRisks(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
    } catch (error) {
      console.error('获取风险列表失败:', error);
      showErrorMessage('获取风险列表失败');
      setRisks([]);
    }
  }, [currentPage, pageSize, showErrorMessage]);

  // 选择项目
  const handleProjectSelect = useCallback((project) => {
    setSelectedProject(project);
    setCurrentPage(1);
    fetchRiskList(project.id, 0);
  }, [fetchRiskList]);

  // 创建风险
  const handleCreateRisk = useCallback(async () => {
    if (!newRisk.name || !newRisk.type || !newRisk.level || !newRisk.owner || !newRisk.status) {
      showErrorMessage('请填写必填项');
      return;
    }

    try {
      await riskApi.createRisk({
        projectId: selectedProject.id,
        name: newRisk.name,
        level: newRisk.level,
        type: newRisk.type,
        employeeId: newRisk.owner,
        description: newRisk.description || '',
        impact: newRisk.impact || '',
        measures: newRisk.response || '',
        status: newRisk.status
      });

      // 刷新数据
      await Promise.all([
        fetchRiskList(selectedProject.id),
        fetchOverviewData()
      ]);

      // 重置表单并关闭模态框
      setShowNewRiskModal(false);
      setNewRisk({
        name: '',
        type: '',
        level: '',
        status: '0',
        description: '',
        impact: '',
        probability: '',
        response: '',
        owner: ''
      });
    } catch (error) {
      console.error('创建风险失败:', error);
      showErrorMessage('创建风险失败，请重试');
    }
  }, [newRisk, selectedProject, fetchRiskList, fetchOverviewData, showErrorMessage]);

  // 编辑风险
  const handleEditClick = useCallback(async (risk) => {
    try {
      const data = await riskApi.getRiskDetail(risk.id);
      setEditingRisk({
        id: data.id,
        name: data.name,
        type: String(data.type),
        level: String(data.level),
        status: String(data.status),
        owner: data.employeeId,
        description: data.description || '',
        impact: data.impact || '',
        response: data.measures || '',
        projectId: data.projectId,
        projectName: data.projectName || ''
      });
      setShowEditModal(true);
    } catch (error) {
      console.error('获取风险详情失败:', error);
      showErrorMessage('获取风险详情失败，请重试');
    }
  }, [showErrorMessage]);

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    if (!editingRisk.name || !editingRisk.type || !editingRisk.level || !editingRisk.owner) {
      showErrorMessage('请填写必填项');
      return;
    }

    try {
      await riskApi.updateRisk(editingRisk.id, {
        projectId: editingRisk.projectId,
        name: editingRisk.name,
        level: editingRisk.level,
        type: editingRisk.type,
        status: editingRisk.status,
        employeeId: editingRisk.owner,
        description: editingRisk.description || '',
        impact: editingRisk.impact || '',
        measures: editingRisk.response || ''
      });

      // 刷新数据
      await Promise.all([
        fetchRiskList(selectedProject.id),
        fetchOverviewData()
      ]);

      setShowEditModal(false);
      setEditingRisk(null);
    } catch (error) {
      console.error('修改风险失败:', error);
      showErrorMessage('修改风险失败，请重试');
    }
  }, [editingRisk, selectedProject, fetchRiskList, fetchOverviewData, showErrorMessage]);

  // 删除风险
  const handleDeleteClick = useCallback((riskId) => {
    setDeletingRiskId(riskId);
    setShowDeleteConfirm(true);
  }, []);

  const handleDeleteConfirm = useCallback(async () => {
    try {
      await riskApi.deleteRisk(deletingRiskId);

      // 刷新数据
      await Promise.all([
        fetchRiskList(selectedProject.id),
        fetchOverviewData()
      ]);

      setShowDeleteConfirm(false);
      setDeletingRiskId(null);
    } catch (error) {
      console.error('删除风险失败:', error);
      showErrorMessage('删除风险失败，请重试');
    }
  }, [deletingRiskId, selectedProject, fetchRiskList, fetchOverviewData, showErrorMessage]);

  // 搜索风险
  const handleSearch = useCallback(async (value, page = 0, level = selectedLevel, type = selectedType) => {
    try {
      const data = await riskApi.getAllRisks(
        page,
        pageSize,
        selectedProject?.id || '',
        value || '',
        level || '',
        type || ''
      );

      setRisks(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
      setCurrentPage(page + 1);
    } catch (error) {
      console.error('搜索风险失败:', error);
      showErrorMessage('搜索风险失败');
      setRisks([]);
    }
  }, [selectedLevel, selectedType, selectedProject, pageSize, showErrorMessage]);

  // 分页处理
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
    if (searchQuery || selectedLevel || selectedType) {
      handleSearch(searchQuery, page - 1, selectedLevel, selectedType);
    } else {
      fetchRiskList(selectedProject?.id, page - 1);
    }
  }, [searchQuery, selectedLevel, selectedType, handleSearch, fetchRiskList, selectedProject]);

  // 错误消息组件
  const ErrorMessage = ({ message, onClose }) => (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-white border border-gray-200 text-gray-800 px-4 py-3 rounded-lg flex items-center gap-2 shadow-lg">
      <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
      <span>{message}</span>
      <button onClick={onClose} className="ml-2 text-gray-400 hover:text-gray-600">
        <Cross2Icon className="h-4 w-4" />
      </button>
    </div>
  );

  // 初始化概览数据
  useEffect(() => {
    fetchOverviewData();
  }, [fetchOverviewData]);

  // 添加点击外部区域处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ownerDropdownRef.current && !ownerDropdownRef.current.contains(event.target)) {
        setIsOwnerDropdownOpen(false);
      }
      if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
      if (levelDropdownRef.current && !levelDropdownRef.current.contains(event.target)) {
        setIsLevelDropdownOpen(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
      if (editTypeDropdownRef.current && !editTypeDropdownRef.current.contains(event.target)) {
        setIsEditTypeDropdownOpen(false);
      }
      if (editLevelDropdownRef.current && !editLevelDropdownRef.current.contains(event.target)) {
        setIsEditLevelDropdownOpen(false);
      }
      if (editStatusDropdownRef.current && !editStatusDropdownRef.current.contains(event.target)) {
        setIsEditStatusDropdownOpen(false);
      }
      if (editOwnerDropdownRef.current && !editOwnerDropdownRef.current.contains(event.target)) {
        setIsEditOwnerDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex">
      {/* 全局错误提示 */}
      {showError && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setShowError(false)}
        />
      )}

      {/* 左侧项目列表侧边栏 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          {/* 项目搜索框 */}
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={(e) => setProjectSearchQuery(e.target.value)}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        {/* 项目列表内容区域 */}
        <div className="flex-1 overflow-y-auto">
          {/* "所有项目"选项 */}
          <div
            className={`p-4 cursor-pointer hover:bg-gray-50 ${!selectedProject ? 'bg-blue-50 border-blue-200 border' : ''}`}
            onClick={async () => {
              setSelectedProject(null);
              setSearchQuery('');
              setSelectedType('');
              setSelectedLevel('');
              try {
                const data = await riskApi.getAllRisks(0, 10);
                setRisks(data.content || []);
                setTotalPages(data.totalPages || 1);
                setTotalElements(data.totalElements || 0);
                setCurrentPage(1);
              } catch (error) {
                console.error('获取风险列表失败:', error);
                setRisks([]);
              }
            }}
          >
            <div className="font-medium">所有项目</div>
          </div>

          {/* 具体项目列表 */}
          {projects
            .filter(project =>
              project.name.toLowerCase().includes(projectSearchQuery.toLowerCase())
            )
            .map(project => (
              <div
                key={project.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                  }`}
                onClick={() => handleProjectSelect(project)}
              >
                <div className="font-medium">{project.name}</div>
                <div className="mt-1">
                  <span className={`text-xs px-2 py-1 rounded-full ${project.status === 0 ? 'bg-gray-100 text-gray-600' :
                    project.status === 1 ? 'bg-blue-100 text-blue-600' :
                      'bg-green-100 text-green-600'
                    }`}>
                    {project.status === 0 ? '未开始' :
                      project.status === 1 ? '进行中' :
                        '已结束'}
                  </span>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* 右侧风险管理主要内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {selectedProject ? selectedProject.name : '所有项目'}
            </h1>
            <p className="text-gray-500">风险管理</p>
          </div>
        </div>

        {/* 风险概览统计卡片 */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-gray-500">风险总数</div>
            <div className="text-2xl font-bold text-blue-500">{overviewData.sum}</div>
            <div className="text-sm text-gray-400">当前项目风险数</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-gray-500">高风险</div>
            <div className="text-2xl font-bold text-red-500">{overviewData.hightLevelRisk}</div>
            <div className="text-sm text-gray-400">需要重点关注</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-gray-500">已解决</div>
            <div className="text-2xl font-bold text-green-500">{overviewData.resolvedRisk}</div>
            <div className="text-sm text-gray-400">已消除的风险</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-gray-500">本周新增风险</div>
            <div className="text-2xl font-bold text-purple-500">{overviewData.riskAtBeforeWeek}</div>
            <div className="text-sm text-gray-400">最近一周</div>
          </div>
        </div>

        {/* 搜索和操作工具栏 */}
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex items-center gap-4 mb-4">
            {/* 风险搜索框 */}
            <div className="relative w-64">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                }}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="搜索风险..."
              />
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            </div>

            {/* 风险状态筛选 */}
            <select
              value={selectedType}
              onChange={(e) => {
                setSelectedType(e.target.value);
              }}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部状态</option>
              {riskStatus.map(status => (
                <option key={status.id} value={status.id}>{status.name}</option>
              ))}
            </select>

            {/* 风险等级筛选 */}
            <select
              value={selectedLevel}
              onChange={(e) => {
                setSelectedLevel(e.target.value);
              }}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">全部等级</option>
              {riskLevels.map(level => (
                <option key={level.id} value={level.id}>{level.name}</option>
              ))}
            </select>

            {/* 搜索按钮 */}
            <Button
              className="flex items-center gap-1"
              onClick={() => {
                setCurrentPage(1);
                handleSearch(searchQuery, 0, selectedLevel, selectedType);
              }}
              style={{ backgroundColor: '#007bff', color: 'white' }}
            >
              <MagnifyingGlassIcon className="w-4 h-4" />
              搜索
            </Button>

            {/* 重置按钮 */}
            <Button
              className="flex items-center gap-1"
              onClick={async () => {
                setSearchQuery('');
                setSelectedType('');
                setSelectedLevel('');
                await handleSearch('', 0, '', '');
              }}
              variant="outline"
            >
              重置
            </Button>

            {/* 新增风险按钮（仅在选择项目时显示） */}
            {selectedProject && (
              <Button
                className="flex items-center gap-1"
                onClick={() => setShowNewRiskModal(true)}
                style={{ backgroundColor: '#007bff', color: 'white' }}
              >
                <PlusIcon className="w-4 h-4" />
                新增风险
              </Button>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm flex-1 flex flex-col min-h-[760px] overflow-hidden">
          <div className="flex-1 overflow-y-auto">
            {/* 表头 */}
            <div className="bg-gray-50 border-b sticky top-0">
              <div className="flex px-6 py-3 text-sm font-medium text-gray-500">
                <div className="w-[10%]">风险名称</div>
                <div className="w-[10%]">风险状态</div>
                <div className="w-[15%]">风险级别</div>
                <div className="w-[15%]">风险类型</div>
                <div className="w-[15%]">所属项目</div>
                <div className="w-[15%]">描述</div>
                <div className="w-[10%]">创建时间</div>
                <div className="w-[10%] pl-2">操作</div>
              </div>
            </div>

            {/* 列表内容 */}
            <div className="divide-y">
              {risks.length > 0 ? (
                risks
                  .filter(risk => !selectedProject || risk.projectId === selectedProject.id)
                  .map(risk => (
                    <div key={risk.id} className="flex px-6 py-4 hover:bg-gray-50 items-center">
                      <div className="w-[10%]">
                        <div className="font-medium">{risk.name}</div>
                      </div>
                      <div className="w-[10%]">
                        <span className={`px-3 py-1 rounded-full text-sm ${risk.status === 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                          }`}>
                          {risk.status === 0 ? '未解决' : '已解决'}
                        </span>
                      </div>
                      <div className="w-[15%]">
                        <span className={`px-3 py-1 rounded-full text-sm ${riskLevels.find(l => l.id === String(risk.level))?.color
                          }`}>
                          {riskLevels.find(l => l.id === String(risk.level))?.name}级风险
                        </span>
                      </div>
                      <div className="w-[15%]">
                        <div className="truncate">
                          {riskTypes.find(t => t.id === String(risk.status))?.name || '-'}
                        </div>
                      </div>
                      <div className="w-[15%]">
                        {risk.projectName || '-'}
                      </div>
                      <div className="w-[15%]">
                        <div className="truncate">{risk.description}</div>
                      </div>
                      <div className="w-[10%]">
                        <div className="text-sm text-gray-500">{risk.employee?.startTime || '-'}</div>
                      </div>
                      <div className="w-[10%] pl-2">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditClick(risk)}>
                            <Pencil1Icon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600"
                            onClick={() => handleDeleteClick(risk.id)}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
              ) : (
                <div className="h-[200px] flex items-center justify-center text-gray-500">
                  暂无风险数据
                </div>
              )}
            </div>
          </div>

          <div className="border-t px-6 py-4 flex items-center justify-end">
            <div className="flex items-center gap-4 text-sm">
              <span className="text-gray-500">
                共 {totalElements} 条记录
              </span>
              <div className="flex items-center">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 ${currentPage === 1
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-600 hover:text-blue-600'
                    }`}
                >
                  上一页
                </button>

                {/* 页码显示部分 */}
                <div className="flex mx-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-8 h-8 mx-1 flex items-center justify-center rounded ${pageNum === currentPage
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-50 text-gray-600 hover:text-blue-600'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 ${currentPage === totalPages
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-600 hover:text-blue-600'
                    }`}
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* New Risk Modal */}
      {showNewRiskModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新增风险</h3>
              <button
                onClick={() => setShowNewRiskModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 项目名称单独一行 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>

                {/* 风险名称和负责人同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newRisk.name}
                      onChange={(e) => setNewRisk({ ...newRisk, name: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入风险名称"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      负责人 <span className="text-red-500">*</span>
                    </label>
                    <div ref={ownerDropdownRef} className="relative">
                      <div
                        onClick={() => setIsOwnerDropdownOpen(!isOwnerDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!newRisk.owner ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={newRisk.owner ? 'text-gray-900' : 'text-gray-400'}>
                          {employees.find(e => e.id === parseInt(newRisk.owner))?.name || '请选择负责人'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isOwnerDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isOwnerDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewRisk({ ...newRisk, owner: employee.id });
                                  setIsOwnerDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${Number(newRisk.owner) === employee.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 风险类型和风险等级同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险类型 <span className="text-red-500">*</span>
                    </label>
                    <div ref={typeDropdownRef} className="relative">
                      <div
                        onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!newRisk.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={newRisk.type ? 'text-gray-900' : 'text-gray-400'}>
                          {newRisk.type ? riskTypes.find(t => t.id === newRisk.type)?.name : '请选择风险类型'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isTypeDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskTypes.map(type => (
                              <div
                                key={type.id}
                                onClick={() => {
                                  setNewRisk({ ...newRisk, type: type.id });
                                  setIsTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${newRisk.type === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {type.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险等级 <span className="text-red-500">*</span>
                    </label>
                    <div ref={levelDropdownRef} className="relative">
                      <div
                        onClick={() => setIsLevelDropdownOpen(!isLevelDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!newRisk.level ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={newRisk.level ? 'text-gray-900' : 'text-gray-400'}>
                          {newRisk.level ? riskLevels.find(l => l.id === newRisk.level)?.name : '请选择风险等级'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isLevelDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isLevelDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskLevels.map(level => (
                              <div
                                key={level.id}
                                onClick={() => {
                                  setNewRisk({ ...newRisk, level: level.id });
                                  setIsLevelDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${newRisk.level === level.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {level.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                </div>

                {/* 风险描述和影响分析同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险状态 <span className="text-red-500">*</span>
                    </label>
                    <div ref={statusDropdownRef} className="relative">
                      <div
                        onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!newRisk.status ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={newRisk.status ? 'text-gray-900' : 'text-gray-400'}>
                          {newRisk.status !== undefined ? riskStatus.find(s => s.id === newRisk.status)?.name : '请选择风险状态'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isStatusDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isStatusDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskStatus.map(status => (
                              <div
                                key={status.id}
                                onClick={() => {
                                  setNewRisk({ ...newRisk, status: status.id });
                                  setIsStatusDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${newRisk.status === status.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {status.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险描述
                    </label>
                    <textarea
                      value={newRisk.description}
                      onChange={(e) => setNewRisk({ ...newRisk, description: e.target.value })}
                      rows={1}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请描述风险的具体情况..."
                    />
                  </div>

                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      影响分析
                    </label>
                    <textarea
                      value={newRisk.impact}
                      onChange={(e) => setNewRisk({ ...newRisk, impact: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请分析风险可能造成的影响..."
                    />
                  </div>
                  {/* 应对措施单独一行 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      应对措施
                    </label>
                    <textarea
                      value={newRisk.response}
                      onChange={(e) => setNewRisk({ ...newRisk, response: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请描述风险的应对措施..."
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewRiskModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateRisk}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingRisk && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑风险</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={editingRisk.projectName || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingRisk.name}
                      onChange={(e) => setEditingRisk({ ...editingRisk, name: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入风险名称"
                    />
                  </div>

                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险类型 <span className="text-red-500">*</span>
                    </label>
                    <div ref={editTypeDropdownRef} className="relative">
                      <div
                        onClick={() => setIsEditTypeDropdownOpen(!isEditTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!editingRisk.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={editingRisk.type ? 'text-gray-900' : 'text-gray-400'}>
                          {editingRisk.type ? riskTypes.find(t => t.id === editingRisk.type)?.name : '请选择风险类型'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditTypeDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isEditTypeDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskTypes.map(type => (
                              <div
                                key={type.id}
                                onClick={() => {
                                  setEditingRisk({ ...editingRisk, type: type.id });
                                  setIsEditTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${editingRisk.type === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {type.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险等级 <span className="text-red-500">*</span>
                    </label>
                    <div ref={editLevelDropdownRef} className="relative">
                      <div
                        onClick={() => setIsEditLevelDropdownOpen(!isEditLevelDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!editingRisk.level ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={editingRisk.level ? 'text-gray-900' : 'text-gray-400'}>
                          {editingRisk.level ? riskLevels.find(l => l.id === editingRisk.level)?.name : '请选择风险等级'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditLevelDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isEditLevelDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskLevels.map(level => (
                              <div
                                key={level.id}
                                onClick={() => {
                                  setEditingRisk({ ...editingRisk, level: level.id });
                                  setIsEditLevelDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${editingRisk.level === level.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {level.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险状态 <span className="text-red-500">*</span>
                    </label>
                    <div ref={editStatusDropdownRef} className="relative">
                      <div
                        onClick={() => setIsEditStatusDropdownOpen(!isEditStatusDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!editingRisk.status ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={editingRisk.status ? 'text-gray-900' : 'text-gray-400'}>
                          {editingRisk.status ? riskStatus.find(s => s.id === editingRisk.status)?.name : '请选择风险状态'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditStatusDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isEditStatusDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {riskStatus.map(status => (
                              <div
                                key={status.id}
                                onClick={() => {
                                  setEditingRisk({ ...editingRisk, status: status.id });
                                  setIsEditStatusDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${editingRisk.status === status.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {status.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      负责人 <span className="text-red-500">*</span>
                    </label>
                    <div ref={editOwnerDropdownRef} className="relative">
                      <div
                        onClick={() => setIsEditOwnerDropdownOpen(!isEditOwnerDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${!editingRisk.owner ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                      >
                        <span className={editingRisk.owner ? 'text-gray-900' : 'text-gray-400'}>
                          {editingRisk.owner ? employees.find(e => e.id === parseInt(editingRisk.owner))?.name : '请选择负责人'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditOwnerDropdownOpen ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {isEditOwnerDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingRisk({ ...editingRisk, owner: employee.id });
                                  setIsEditOwnerDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${Number(editingRisk.owner) === employee.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      风险描述
                    </label>
                    <textarea
                      value={editingRisk.description}
                      onChange={(e) => setEditingRisk({ ...editingRisk, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请描述风险的具体情况..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      影响分析
                    </label>
                    <textarea
                      value={editingRisk.impact}
                      onChange={(e) => setEditingRisk({ ...editingRisk, impact: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请分析风险可能造成的影响..."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    应对措施
                  </label>
                  <textarea
                    value={editingRisk.response}
                    onChange={(e) => setEditingRisk({ ...editingRisk, response: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请描述风险的应对措施..."
                  />
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleSaveEdit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">确认删除</h3>
              <p className="text-gray-500">确定要删除该资源吗？</p>
            </div>
            <div className="p-4 border-t flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeletingRiskId(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteConfirm}
                style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
              >
                确认删除
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});