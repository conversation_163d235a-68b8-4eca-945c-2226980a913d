import axios from 'axios';

import { fetchData } from './fetch';

// 项目相关接口
export const projectApi = {
  // 获取所有项目列表
  getAllProjects: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),
};

// 评审相关接口
export const reviewApi = {
  // 获取评审列表（分页）
  getReviewList: (projectId, page = 0, size = 10, employeeId) => {
    const params = new URLSearchParams({ projectId, page, size, employeeId });
    return fetch(`${fetchData["PROJECT_URL"]}/api/reviews/find/page?${params}`).then(res => res.json());
  },

  // 搜索评审
  searchReviews: (params) => {
    const searchParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/reviews/find/page?${searchParams}`).then(res => res.json());
  },

  // 获取单个评审详情
  getReviewDetail: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/reviews/one/${id}`).then(res => res.json()),

  // 创建评审
  createReview: (reviewData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/reviews/create/review`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewData)
    }).then(res => res.json()),

  // 确认评审
  confirmReview: (id, status, advice) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/reviews/confirm?id=${id}&status=${status}&advice=${advice}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ advice: advice || '' })
    }).then(res => res.json()),

  // 删除评审
  deleteReview: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/reviews/delete/${id}`, {
      method: 'DELETE'
    }).then(res => res.json()),
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName = 'schemedesign') => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 下载文件
  downloadFile: (fileName, bucketName = 'schemedesign') => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getAllEmployees: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
};

// 方案设计相关接口
export const schemeDesignApi = {
  // 获取方案设计列表（分页）
  getSchemeDesigns: (projectId, page = 0, size = 50, name = '') => {
    const params = new URLSearchParams({
      projectId,
      page,
      size,
      ...(name && { name })
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/page?${params}`).then(res => res.json());
  },
}; 