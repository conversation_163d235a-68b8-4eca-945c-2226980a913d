import React, { useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { navigationStore } from '../store/navigationStore';
import { useNavigate } from 'react-router-dom';
import {
  FileTextIcon, UploadIcon, DownloadIcon, TimerIcon, LayersIcon,
  ExclamationTriangleIcon, PersonIcon, CalendarIcon, FileIcon,
  MagnifyingGlassIcon, CodeIcon, ListBulletIcon, GitHubLogoIcon,
  CommitIcon, ViewGridIcon, MixIcon, CheckboxIcon, CheckCircledIcon,
  GroupIcon, BoxIcon, ArrowRightIcon, StackIcon, BellIcon, TableIcon,
  BarChartIcon, ClipboardIcon, CrossCircledIcon, TargetIcon, GearIcon,
  HeartIcon, StarIcon, LockClosedIcon, ArchiveIcon, CardStackIcon,
  DrawingPinIcon, LightningBoltIcon, GridIcon, ResetIcon
} from '@radix-ui/react-icons';
import { Modal, Input, message } from 'antd';
import { userStore } from '../store/userStore';
import { employeeApi } from '../services/hrService';

// 菜单配置
const menuConfigs = {
  '项目管理': {
    icon: LayersIcon,
    submenu: [
      { icon: UploadIcon, text: '项目创建', isTopLevel: true },
      { icon: DownloadIcon, text: '项目输入', isTopLevel: true },
      { icon: DownloadIcon, text: '项目输出', isTopLevel: true },
      { icon: TimerIcon, text: '进度管理', isTopLevel: true },
      { icon: LayersIcon, text: '资源管理', isTopLevel: true },
      { icon: ExclamationTriangleIcon, text: '风险管理', isTopLevel: true }
    ]
  },
  '人事管理': {
    icon: PersonIcon,
    submenu: [
      { icon: FileTextIcon, text: '组织架构', isTopLevel: true },
      { icon: GroupIcon, text: '人员管理', isTopLevel: true }
    ]
  },
  '研发管理': {
    icon: CodeIcon,
    submenu: [
      {
        icon: ClipboardIcon,
        text: '项目策划',
        children: [
          { text: '设计输入' },
          { text: '输入评审' }
        ]
      },
      {
        icon: ListBulletIcon,
        text: '需求管理',
        children: [
          { text: '需求分解' },
          { text: '配置基线' }
        ]
      },
      {
        icon: ViewGridIcon,
        text: '设计管理',
        children: [
          { text: '方案设计' },
          { text: '方案评审' },
          { text: '设计基线' }
        ]
      },
      {
        icon: GitHubLogoIcon,
        text: '开发管理',
        children: [
          { text: '代码库' },
          { text: '合并请求' },
          { text: '代码评审' },
          { text: '开发基线' }
        ]
      },
      {
        icon: MixIcon,
        text: '集成',
        children: [
          { text: '集成测试' },
          { text: '集成基线' }
        ]
      },
      {
        icon: CheckboxIcon,
        text: '测试',
        children: [
          { text: '测试计划' },
          { text: '测试用例' },
          { text: '测试结果' }
        ]
      },
      {
        icon: CheckCircledIcon,
        text: '产品验收',
        children: [
          { text: '验收' },
          { text: '成果交付' },
          { text: '项目结项' }
        ]
      }
    ]
  },
  '财务管理': {
    icon: BarChartIcon,
    submenu: [
      { icon: BarChartIcon, text: '财务报表管理', isTopLevel: true },
      { icon: FileTextIcon, text: '账务处理', isTopLevel: true },
      { icon: TargetIcon, text: '预算与规划', isTopLevel: true },
      { icon: CardStackIcon, text: '收入与支出管理', isTopLevel: true },
      { icon: ArchiveIcon, text: '资产管理', isTopLevel: true },
      { icon: CardStackIcon, text: '应收与应付管理', isTopLevel: true },
      { icon: DrawingPinIcon, text: '税务管理', isTopLevel: true },
      { icon: LightningBoltIcon, text: '资金管理', isTopLevel: true },
      { icon: LockClosedIcon, text: '财务审计与合规', isTopLevel: true },
      { icon: BarChartIcon, text: '财务分析与决策支持', isTopLevel: true }
    ]
  },
  '生产管理': {
    icon: GearIcon,
    submenu: [
      { icon: GroupIcon, text: '班组管理', isTopLevel: true },
      { icon: CalendarIcon, text: '班次管理', isTopLevel: true },
      { icon: GridIcon, text: '工作站', isTopLevel: true },
      { icon: BoxIcon, text: '车间管理', isTopLevel: true },
      { icon: GearIcon, text: '工序管理', isTopLevel: true },
      { icon: ArrowRightIcon, text: '工艺路线', isTopLevel: true },
      { icon: StackIcon, text: '产品BOM', isTopLevel: true },
      { icon: FileTextIcon, text: '销售订单', isTopLevel: true },
      { icon: ClipboardIcon, text: '生产计划', isTopLevel: true },
      { icon: FileIcon, text: '产品工单', isTopLevel: true },
      { icon: ListBulletIcon, text: '工单任务', isTopLevel: true },
      { icon: CheckboxIcon, text: '报工记录', isTopLevel: true },
      { icon: CheckCircledIcon, text: '产出质检', isTopLevel: true }
    ]
  },
  '销售管理': {
    icon: BarChartIcon,
    submenu: [
      { icon: FileTextIcon, text: '销售订单', isTopLevel: true },
      { icon: TableIcon, text: '销售报表', isTopLevel: true },
      { icon: PersonIcon, text: '客户管理', isTopLevel: true },
      { icon: BellIcon, text: '销售跟进', isTopLevel: true }
    ]
  },
  '质量管理': {
    icon: LockClosedIcon,
    submenu: [
      {
        icon: TargetIcon,
        text: '质量计划与控制',
        children: [
          { text: '质量方针管理' },
          { text: '质量计划制定' },
          { text: '质量标准定义' }
        ]
      },
      {
        icon: CheckCircledIcon,
        text: '产品质量检验',
        children: [
          { text: '原材料检验' },
          { text: '生产过程检验' },
          { text: '成品检验' },
          { text: '检验报告与数据记录' }
        ]
      },
      {
        icon: CrossCircledIcon,
        text: '不合格品管理',
        children: [
          { text: '不合格品登记' },
          { text: '不合格品隔离' },
          { text: '根因分析' },
          { text: '纠正与预防措施管理' }
        ]
      },
      {
        icon: MagnifyingGlassIcon,
        text: '质量审核',
        children: [
          { text: '内部质量审核' },
          { text: '外部质量审核' },
          { text: '审核报告生成与整改追踪' }
        ]
      },
      {
        icon: GearIcon,
        text: '供应商质量管理',
        children: [
          { text: '供应商评审与选择' },
          { text: '供应商质量审核' },
          { text: '供应商质量问题处理' },
          { text: '供应商绩效跟踪与反馈' }
        ]
      },
      {
        icon: HeartIcon,
        text: '客户质量管理',
        children: [
          { text: '客户质量要求定义' },
          { text: '客户投诉管理' },
          { text: '客户满意度调查' },
          { text: '客户质量报告' }
        ]
      },
      {
        icon: StarIcon,
        text: '质量数据分析与报告',
        children: [
          { text: '质量趋势分析' },
          { text: '质量报告生成' },
          { text: '质量KPI跟踪' },
          { text: '统计分析工具' }
        ]
      }
    ]
  },
  '采购管理': {
    icon: BoxIcon,
    submenu: [
      {
        icon: GroupIcon,
        text: '供应商管理',
        children: [
          { text: '供应商信息' },
          { text: '资质审核' },
          { text: '评估分级' },
          { text: '合作关系' }
        ]
      },
      {
        icon: ClipboardIcon,
        text: '采购需求',
        children: [
          { text: '需求收集' },
          { text: '需求分析' },
          { text: '采购申请' }
        ]
      },
      {
        icon: CalendarIcon,
        text: '采购计划',
        children: [
          { text: '计划制定' },
          { text: '计划审批' },
          { text: '计划执行' }
        ]
      },
      {
        icon: FileTextIcon,
        text: '采购订单',
        children: [
          { text: '订单创建' },
          { text: '订单审核' },
          { text: '订单跟踪' },
          { text: '异常处理' }
        ]
      },
      {
        icon: FileIcon,
        text: '合同管理',
        children: [
          { text: '合同起草' },
          { text: '合同审批' },
          { text: '合同执行' },
          { text: '付款管理' }
        ]
      },
      {
        icon: ArchiveIcon,
        text: '库存管理',
        children: [
          { text: '库存监控' },
          { text: '库存预警' },
          { text: '库存分析' }
        ]
      },
      {
        icon: BarChartIcon,
        text: '价格管理',
        children: [
          { text: '价格采集' },
          { text: '价格分析' },
          { text: '价格策略' }
        ]
      },
      {
        icon: CheckCircledIcon,
        text: '质量检验',
        children: [
          { text: '检验标准' },
          { text: '检验流程' },
          { text: '不合格处理' }
        ]
      },
      {
        icon: CardStackIcon,
        text: '付款管理',
        children: [
          { text: '付款申请' },
          { text: '付款审核' },
          { text: '付款记录' }
        ]
      },
      {
        icon: TableIcon,
        text: '统计分析',
        children: [
          { text: '成本分析' },
          { text: '进度报表' },
          { text: '绩效分析' },
          { text: '决策支持' }
        ]
      }
    ]
  },
  '仓储管理': {
    icon: ArchiveIcon,
    submenu: [
      {
        icon: BoxIcon,
        text: '物料管理',
        children: [
          { text: '物料信息' },
          { text: '物料分类' },
          { text: '物料编码' },
          { text: '规格型号' }
        ]
      },
      {
        icon: StackIcon,
        text: '产品BOM',
        children: [
          { text: 'BOM创建' },
          { text: 'BOM维护' },
          { text: 'BOM版本' },
          { text: 'BOM查询' }
        ]
      },
      {
        icon: DownloadIcon,
        text: '采购点收',
        children: [
          { text: '到货登记' },
          { text: '收货确认' },
          { text: '单据管理' }
        ]
      },
      {
        icon: CheckCircledIcon,
        text: '入厂检验',
        children: [
          { text: '检验标准' },
          { text: '检验记录' },
          { text: '不合格处理' }
        ]
      },
      {
        icon: ResetIcon,
        text: '退货换货',
        children: [
          { text: '退货申请' },
          { text: '换货处理' },
          { text: '退换记录' }
        ]
      },
      {
        icon: ClipboardIcon,
        text: '生产备料',
        children: [
          { text: '备料计划' },
          { text: '备料单' },
          { text: '备料进度' }
        ]
      },
      {
        icon: FileTextIcon,
        text: '物料领用',
        children: [
          { text: '领料申请' },
          { text: '领料审核' },
          { text: '领料记录' }
        ]
      },
      {
        icon: ArchiveIcon,
        text: '库存管理',
        children: [
          { text: '库存查询' },
          { text: '库存预警' },
          { text: '库存调拨' },
          { text: '库存冻结' }
        ]
      },
      {
        icon: MagnifyingGlassIcon,
        text: '盘点库存',
        children: [
          { text: '盘点计划' },
          { text: '盘点执行' },
          { text: '差异处理' },
          { text: '盘点报告' }
        ]
      },
      {
        icon: CommitIcon,
        text: '物料追溯',
        children: [
          { text: '入库追溯' },
          { text: '出库追溯' },
          { text: '库存追溯' },
          { text: '质量追溯' }
        ]
      },
      {
        icon: TableIcon,
        text: '报表统计',
        children: [
          { text: '入库报表' },
          { text: '出库报表' },
          { text: '库存报表' },
          { text: '周转分析' }
        ]
      }
    ]
  }
};

export const Sidebar = observer(() => {
  const navigate = useNavigate();
  const [expandedSubmenu, setExpandedSubmenu] = React.useState(() => {
    const saved = localStorage.getItem('expandedSubmenu');
    return saved ? parseInt(saved) : null;
  });

  const { currentSystem, currentSubPage, setCurrentSubPage } = navigationStore;
  const [isTokenModalVisible, setIsTokenModalVisible] = useState(false);
  const [accessToken, setAccessToken] = useState('');

  // 简化菜单获取逻辑
  const activeMenu = useMemo(() => {
    return currentSystem ? {
      icon: menuConfigs[currentSystem]?.icon,
      text: currentSystem,
      submenu: menuConfigs[currentSystem]?.submenu || []
    } : null;
  }, [currentSystem]);

  const handleSubmenuClick = (index) => {
    const newState = expandedSubmenu === index ? null : index;
    setExpandedSubmenu(newState);
    if (newState === null) {
      localStorage.removeItem('expandedSubmenu');
    } else {
      localStorage.setItem('expandedSubmenu', newState);
    }
  };

  // 路由映射 - 保持与面包屑导航一致的结构
  const routeMap = {
    '项目管理': {
      '项目创建': '/project-management/create',
      '项目输入': '/project-management/input',
      '项目输出': '/project-management/output',
      '进度管理': '/project-management/progress',
      '资源管理': '/project-management/resource',
      '风险管理': '/project-management/risk'
    },
    '研发管理': {
      '设计输入': '/dev-management/design-input',
      '输入评审': '/dev-management/input-review',
      '需求分解': '/dev-management/requirements',
      '配置基线': '/dev-management/config-baseline',
      '方案设计': '/dev-management/design-proposal',
      '方案评审': '/dev-management/design-review',
      '设计基线': '/dev-management/design-baseline',
      '代码库': '/dev-management/repository',
      '合并请求': '/dev-management/merge-requests',
      '代码评审': '/dev-management/code-review',
      '开发基线': '/dev-management/dev-baseline',
      '集成测试': '/dev-management/integration-test',
      '集成基线': '/dev-management/integration-baseline',
      '测试计划': '/dev-management/test-plan',
      '测试用例': '/dev-management/test-case',
      '测试结果': '/dev-management/test-result',
      '验收': '/dev-management/product-acceptance',
      '成果交付': '/dev-management/product-delivery',
      '项目结项': '/dev-management/project-closure'
    },
    '人事管理': {
      '组织架构': '/hr-management/organization',
      '人员管理': '/hr-management/personnel'
    },
    '财务管理': {
      '财务报表管理': '/finance/reports',
      '账务处理': '/finance/accounting',
      '预算与规划': '/finance/budget',
      '收入与支出管理': '/finance/income-expense',
      '资产管理': '/finance/assets',
      '应收与应付管理': '/finance/receivables-payables',
      '税务管理': '/finance/tax',
      '资金管理': '/finance/funds',
      '财务审计与合规': '/finance/audit',
      '财务分析与决策支持': '/finance/analysis'
    },
    '生产管理': {
      '班组管理': '/production/teams',
      '班次管理': '/production/shifts',
      '工作站': '/production/workstations',
      '车间管理': '/production/workshops',
      '工序管理': '/production/processes',
      '工艺路线': '/production/routes',
      '产品BOM': '/production/bom',
      '销售订单': '/production/sales-orders',
      '生产计划': '/production/plans',
      '产品工单': '/production/work-orders',
      '工单任务': '/production/tasks',
      '报工记录': '/production/work-reports',
      '产出质检': '/production/quality-check'
    },
    '销售管理': {
      '销售订单': '/sales/orders',
      '销售报表': '/sales/reports',
      '客户管理': '/sales/customers',
      '销售跟进': '/sales/follow-up'
    },
    '质量管理': {
      '质量方针管理': '/quality/policy',
      '质量计划制定': '/quality/planning',
      '质量标准定义': '/quality/standards',
      '原材料检验': '/quality/material-inspection',
      '生产过程检验': '/quality/process-inspection',
      '成品检验': '/quality/product-inspection',
      '检验报告与数据记录': '/quality/inspection-reports',
      '不合格品登记': '/quality/nonconforming-registration',
      '不合格品隔离': '/quality/nonconforming-isolation',
      '根因分析': '/quality/root-cause-analysis',
      '纠正与预防措施管理': '/quality/corrective-preventive',
      '内部质量审核': '/quality/internal-audit',
      '外部质量审核': '/quality/external-audit',
      '审核报告生成与整改追踪': '/quality/audit-reports',
      '供应商评审与选择': '/quality/supplier-evaluation',
      '供应商质量审核': '/quality/supplier-audit',
      '供应商质量问题处理': '/quality/supplier-issues',
      '供应商绩效跟踪与反馈': '/quality/supplier-performance',
      '客户质量要求定义': '/quality/customer-requirements',
      '客户投诉管理': '/quality/customer-complaints',
      '客户满意度调查': '/quality/customer-satisfaction',
      '客户质量报告': '/quality/customer-reports',
      '质量趋势分析': '/quality/trend-analysis',
      '质量报告生成': '/quality/report-generation',
      '质量KPI跟踪': '/quality/kpi-tracking',
      '统计分析工具': '/quality/statistical-analysis'
    },
    '采购管理': {
      '供应商信息': '/procurement/supplier-info',
      '资质审核': '/procurement/qualification-review',
      '评估分级': '/procurement/evaluation-grading',
      '合作关系': '/procurement/cooperation',
      '需求收集': '/procurement/requirement-collection',
      '需求分析': '/procurement/requirement-analysis',
      '采购申请': '/procurement/purchase-application',
      '计划制定': '/procurement/plan-formulation',
      '计划审批': '/procurement/plan-approval',
      '计划执行': '/procurement/plan-execution',
      '订单创建': '/procurement/order-creation',
      '订单审核': '/procurement/order-review',
      '订单跟踪': '/procurement/order-tracking',
      '异常处理': '/procurement/exception-handling',
      '合同起草': '/procurement/contract-drafting',
      '合同审批': '/procurement/contract-approval',
      '合同执行': '/procurement/contract-execution',
      '付款管理': '/procurement/payment-management',
      '库存监控': '/procurement/inventory-monitoring',
      '库存预警': '/procurement/inventory-warning',
      '库存分析': '/procurement/inventory-analysis',
      '价格采集': '/procurement/price-collection',
      '价格分析': '/procurement/price-analysis',
      '价格策略': '/procurement/price-strategy',
      '检验标准': '/procurement/inspection-standards',
      '检验流程': '/procurement/inspection-process',
      '不合格处理': '/procurement/nonconforming-handling',
      '付款申请': '/procurement/payment-application',
      '付款审核': '/procurement/payment-review',
      '付款记录': '/procurement/payment-records',
      '成本分析': '/procurement/cost-analysis',
      '进度报表': '/procurement/progress-reports',
      '绩效分析': '/procurement/performance-analysis',
      '决策支持': '/procurement/decision-support'
    },
    '仓储管理': {
      '物料信息': '/warehouse/material-info',
      '物料分类': '/warehouse/material-classification',
      '物料编码': '/warehouse/material-coding',
      '规格型号': '/warehouse/specifications',
      'BOM创建': '/warehouse/bom-creation',
      'BOM维护': '/warehouse/bom-maintenance',
      'BOM版本': '/warehouse/bom-version',
      'BOM查询': '/warehouse/bom-query',
      '到货登记': '/warehouse/arrival-registration',
      '收货确认': '/warehouse/receipt-confirmation',
      '单据管理': '/warehouse/document-management',
      '检验标准': '/warehouse/inspection-standards',
      '检验记录': '/warehouse/inspection-records',
      '不合格处理': '/warehouse/nonconforming-handling',
      '退货申请': '/warehouse/return-application',
      '换货处理': '/warehouse/exchange-handling',
      '退换记录': '/warehouse/return-exchange-records',
      '备料计划': '/warehouse/material-preparation-plan',
      '备料单': '/warehouse/material-preparation-order',
      '备料进度': '/warehouse/material-preparation-progress',
      '领料申请': '/warehouse/material-requisition-application',
      '领料审核': '/warehouse/material-requisition-review',
      '领料记录': '/warehouse/material-requisition-records',
      '库存查询': '/warehouse/inventory-query',
      '库存预警': '/warehouse/inventory-warning',
      '库存调拨': '/warehouse/inventory-transfer',
      '库存冻结': '/warehouse/inventory-freeze',
      '盘点计划': '/warehouse/stocktaking-plan',
      '盘点执行': '/warehouse/stocktaking-execution',
      '差异处理': '/warehouse/variance-handling',
      '盘点报告': '/warehouse/stocktaking-report',
      '入库追溯': '/warehouse/inbound-traceability',
      '出库追溯': '/warehouse/outbound-traceability',
      '库存追溯': '/warehouse/inventory-traceability',
      '质量追溯': '/warehouse/quality-traceability',
      '入库报表': '/warehouse/inbound-reports',
      '出库报表': '/warehouse/outbound-reports',
      '库存报表': '/warehouse/inventory-reports',
      '周转分析': '/warehouse/turnover-analysis'
    }
  };

  const handleSubPageClick = (subPage) => {
    setCurrentSubPage(subPage);
    const route = routeMap[currentSystem]?.[subPage];
    if (route) {
      navigate(route);
    }
  };

  const handleDevManagementClick = () => {
    const userData = userStore.getUserData();
    if (!userData?.accessToken) {
      setIsTokenModalVisible(true);
    }
  };

  const handleModalClose = () => {
    setIsTokenModalVisible(false);
    setAccessToken('');
  };

  const handleSaveToken = async () => {
    if (!accessToken.trim()) {
      message.warning('请输入有效的访问令牌');
      return;
    }

    const userData = userStore.getUserData() || {};
    if (!userData.id) {
      message.error('用户ID不存在，请重新登录');
      return;
    }

    try {
      const response = await employeeApi.uploadSshToken(userData.id, accessToken.trim());
      const updatedUserData = {
        ...userData,
        accessToken: response.data?.accessToken || accessToken.trim()
      };

      localStorage.setItem('userData', JSON.stringify(updatedUserData));
      userStore.userData = updatedUserData;

      setIsTokenModalVisible(false);
      setAccessToken('');
      message.success('访问令牌保存成功');
    } catch (error) {
      console.error('上传访问令牌失败:', error);
      message.error('访问令牌保存失败，请重试');
    }
  };

  // 根据当前页面自动展开对应的菜单
  React.useEffect(() => {
    if (currentSubPage && activeMenu) {
      const menuIndex = activeMenu.submenu.findIndex(item =>
        item.children?.some(child => child.text === currentSubPage)
      );
      if (menuIndex !== -1) {
        setExpandedSubmenu(menuIndex);
        localStorage.setItem('expandedSubmenu', menuIndex);
      }
    }
  }, [currentSubPage, activeMenu]);

  // 渲染菜单项的辅助函数
  const renderMenuItem = (subItem, subIndex) => {
    const isActive = currentSubPage === subItem.text;
    const baseClasses = "flex items-center gap-3 p-2 rounded-lg cursor-pointer hover:bg-gray-100";
    const activeClasses = isActive ? "bg-blue-50 text-blue-600" : "";

    if (subItem.isTopLevel) {
      return (
        <div
          className={`${baseClasses} ${activeClasses}`}
          onClick={() => handleSubPageClick(subItem.text)}
        >
          <subItem.icon className="w-4 h-4" />
          <span className="text-sm">{subItem.text}</span>
        </div>
      );
    }

    if (subItem.children) {
      return (
        <>
          <div
            className={baseClasses}
            onClick={() => {
              if (subItem.text === '开发管理') {
                handleDevManagementClick();
              }
              handleSubmenuClick(subIndex);
            }}
          >
            <subItem.icon className="w-4 h-4" />
            <span className="text-sm">{subItem.text}</span>
          </div>
          {expandedSubmenu === subIndex && (
            <div className="ml-6 mt-1 space-y-1">
              {subItem.children.map((child, childIndex) => (
                <div
                  key={childIndex}
                  className={`flex items-center gap-2 p-2 text-sm hover:bg-gray-100 rounded-lg cursor-pointer ${
                    currentSubPage === child.text ? 'bg-blue-50 text-blue-600' : 'text-gray-600'
                  }`}
                  onClick={() => handleSubPageClick(child.text)}
                >
                  {child.text}
                </div>
              ))}
            </div>
          )}
        </>
      );
    }

    return (
      <div
        className={`${baseClasses} ${activeClasses}`}
        onClick={() => handleSubPageClick(subItem.text)}
      >
        <subItem.icon className="w-4 h-4" />
        <span className="text-sm">{subItem.text}</span>
      </div>
    );
  };

  return (
    <>
      <div className="w-48 bg-[#f0f3fa] h-screen overflow-y-auto scrollbar-none flex-shrink-0 z-[1]">
        <div className="p-4">
          <div className="flex items-center gap-2 mb-8">
            <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
            <span className="font-medium">{currentSystem}</span>
          </div>
          <nav>
            {activeMenu && (
              <>
                <div className="flex items-center gap-3 p-3 rounded-lg cursor-pointer bg-blue-500 text-white">
                  <activeMenu.icon className="w-5 h-5" />
                  <span>{activeMenu.text}</span>
                </div>
                <div className="ml-4 mt-2 space-y-1">
                  {activeMenu.submenu.map((subItem, subIndex) => (
                    <div key={subIndex}>
                      {renderMenuItem(subItem, subIndex)}
                    </div>
                  ))}
                </div>
              </>
            )}
          </nav>
        </div>
      </div>

      <Modal
        title="添加访问令牌"
        open={isTokenModalVisible}
        onCancel={handleModalClose}
        onOk={handleSaveToken}
        okText="保存"
        cancelText="取消"
      >
        <div className="mb-4">
          <Input
            placeholder="请输入项目访问令牌"
            value={accessToken}
            onChange={(e) => setAccessToken(e.target.value)}
            onPressEnter={handleSaveToken}
          />
        </div>
        <p>请添加项目访问令牌以访问开发管理功能</p>
      </Modal>
    </>
  );
});