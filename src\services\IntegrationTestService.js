import axios from 'axios';

import { fetchData } from './fetch';

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (searchName = '') => {
    const params = new URLSearchParams({ name: searchName });
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${params}`).then(res => res.json());
  }
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json())
};

// 集成测试相关接口
export const integrationTestApi = {
  // 获取集成测试列表（分页）
  getTestList: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-integration/page?${queryParams}`).then(res => res.json());
  },

  // 获取单个集成测试详情
  getTestDetail: (testId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-integration/one/${testId}`).then(res => res.json()),

  // 创建集成测试
  createTest: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-integration/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新集成测试
  updateTest: (testId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-integration/update/${testId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除集成测试
  deleteTest: (testId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-integration/${testId}`, {
      method: 'DELETE'
    }).then(res => res.json()),
};

// 文件管理相关接口
export const fileApi = {
  // 文件预览
  previewFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 文件下载
  downloadFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },

  // 文件上传
  uploadFile: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/upload`, {
      method: 'POST',
      body: formData
    }).then(res => res.text()),

  // 获取所有文件名
  getAllFileNames: (bucketName) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/getAllFileName?bucketName=${bucketName}`).then(res => res.json()),

  // 删除文件
  deleteFile: (fileId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/files/delete/${fileId}`, {
      method: 'DELETE'
    }).then(res => res.json()),
}; 