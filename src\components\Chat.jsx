import React, { useState, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { chatStore } from '../store/chatStore';
import { Button } from './ui/button';
import {
  Cross2Icon,
  PersonIcon,
  PaperPlaneIcon,
  ChatBubbleIcon,
  RocketIcon,
  ImageIcon,
  MagnifyingGlassIcon,
  MinusIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@radix-ui/react-icons';
import Dialog from './Dialog';

export const Chat = observer(() => {
  const [message, setMessage] = useState('');
  const [expandedDepartments, setExpandedDepartments] = useState({});
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const { isOpen, departments, searchQuery } = chatStore;
  const [position, setPosition] = useState({ x: undefined, y: undefined });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const chatRef = useRef(null);
  const [isMinimized, setIsMinimized] = useState(false);

  const toggleDepartment = (deptId) => {
    setExpandedDepartments(prev => ({
      ...prev,
      [deptId]: !prev[deptId]
    }));
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatStore.currentConversation?.messages]);

  const handleDragStart = (e) => {
    if (e.target.closest('.chat-header')) {
      setIsDragging(true);
      const rect = chatRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleDrag = (e) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;
      
      const maxX = window.innerWidth - chatRef.current.offsetWidth;
      const maxY = window.innerHeight - chatRef.current.offsetHeight;
      
      setPosition({
        x: Math.min(Math.max(0, newX), maxX),
        y: Math.min(Math.max(0, newY), maxY)
      });
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleDrag);
      document.addEventListener('mouseup', handleDragEnd);
      return () => {
        document.removeEventListener('mousemove', handleDrag);
        document.removeEventListener('mouseup', handleDragEnd);
      };
    }
  }, [isDragging]);

  useEffect(() => {
    if (position.x === undefined && position.y === undefined && chatRef.current) {
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const chatWidth = 800; // 聊天窗口宽度
      const chatHeight = 600; // 聊天窗口高度
      
      setPosition({
        x: (windowWidth - chatWidth) / 2,
        y: (windowHeight - chatHeight) / 2
      });
    }
  }, []);

  if (!isOpen) return null;

  const handleSendMessage = () => {
    if (message.trim()) {
      chatStore.addMessage({
        content: message.trim(),
        isUser: true,
        timestamp: new Date()
      });
      setMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        chatStore.addMessage({
          content: e.target.result,
          isUser: true,
          isImage: true,
          timestamp: new Date()
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const filteredContacts = chatStore.getFilteredContacts();

  const handleMinimize = () => {
    setIsMinimized(true);
  };

  const handleRestore = () => {
    setIsMinimized(false);
  };

  const handleClose = () => {
    chatStore.toggleChat();
  };

  return (
    <Dialog
      isMinimized={isMinimized}
      onMinimize={handleMinimize}
      onRestore={handleRestore}
    >
      <div
        ref={chatRef}
        className={`fixed bg-white rounded-lg shadow-lg flex flex-col ${
          isDragging ? 'select-none' : ''
        }`}
        style={{
          width: '800px',
          height: '600px',
          top: `${position.y}px`,
          left: `${position.x}px`,
          zIndex: 1000,
        }}
      >
        <div className="flex h-full">
          {/* Contacts List */}
          <div className="w-64 border-r flex flex-col">
            <div className="p-3 border-b">
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索部门或联系人"
                  value={searchQuery}
                  onChange={(e) => chatStore.setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-1.5 bg-gray-100 rounded-lg text-sm focus:outline-none"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2 w-4 h-4 text-gray-500" />
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              {/* AI Assistant */}
              <div 
                className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 ${
                  chatStore.currentConversation?.id === 'ai-assistant' ? 'bg-blue-50' : ''
                }`}
                onClick={() => chatStore.startConversation('ai-assistant')}
              >
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <RocketIcon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-medium">AI助手</div>
                  <div className="text-sm text-green-500">在线</div>
                </div>
              </div>

              {/* Departments */}
              {!searchQuery && departments.map(dept => (
                <div key={dept.id}>
                  <div
                    className="p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-2"
                    onClick={() => toggleDepartment(dept.id)}
                  >
                    {expandedDepartments[dept.id] ? (
                      <ChevronDownIcon className="w-4 h-4" />
                    ) : (
                      <ChevronRightIcon className="w-4 h-4" />
                    )}
                    <span className="font-medium">{dept.name}</span>
                    <span className="text-sm text-gray-500">({dept.employees.length})</span>
                  </div>
                  {expandedDepartments[dept.id] && dept.employees.map(emp => (
                    <div
                      key={emp.id}
                      className={`pl-8 pr-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center gap-3 ${
                        chatStore.currentConversation?.id === emp.id ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => chatStore.startConversation(emp.id)}
                    >
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <PersonIcon className="w-4 h-4 text-gray-600" />
                      </div>
                      <div>
                        <div className="font-medium">{emp.name}</div>
                        <div className={`text-sm ${emp.online ? 'text-green-500' : 'text-gray-500'}`}>
                          {emp.online ? '在线' : '离线'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}

              {/* Search Results */}
              {searchQuery && filteredContacts.map(contact => (
                <div
                  key={contact.id}
                  className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3 ${
                    chatStore.currentConversation?.id === contact.id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => chatStore.startConversation(contact.id)}
                >
                  <div className={`w-10 h-10 ${contact.isAI ? 'bg-blue-500' : 'bg-gray-200'} rounded-full flex items-center justify-center`}>
                    {contact.isAI ? (
                      <RocketIcon className="w-5 h-5 text-white" />
                    ) : (
                      <PersonIcon className="w-5 h-5 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{contact.name}</div>
                    {contact.departmentName && (
                      <div className="text-sm text-gray-500">{contact.departmentName}</div>
                    )}
                    <div className={`text-sm ${contact.online ? 'text-green-500' : 'text-gray-500'}`}>
                      {contact.online ? '在线' : '离线'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Chat Area */}
          <div className="flex-1 flex flex-col">
            {/* Chat Header */}
            <div 
              className="p-4 border-b flex justify-between items-center chat-header cursor-move"
              onMouseDown={handleDragStart}
            >
              <div className="font-semibold">
                {chatStore.currentConversation?.name}
                {chatStore.currentConversation?.departmentName && (
                  <span className="text-sm text-gray-500 ml-2">
                    ({chatStore.currentConversation.departmentName})
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleMinimize}
                  className="p-2 hover:bg-gray-100 rounded-lg text-gray-600"
                  title="最小化"
                >
                  <MinusIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={handleClose}
                  className="p-2 hover:bg-gray-100 rounded-lg text-gray-600"
                  title="关闭"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {chatStore.currentConversation?.messages.map((msg, index) => (
                <div
                  key={index}
                  className={`flex ${msg.isUser ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start gap-2 max-w-[80%] ${msg.isUser ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      msg.isUser ? 'bg-blue-500' : 'bg-gray-200'
                    }`}>
                      {msg.isUser ? (
                        <PersonIcon className="w-4 h-4 text-white" />
                      ) : chatStore.currentConversation.isAI ? (
                        <RocketIcon className="w-4 h-4 text-gray-600" />
                      ) : (
                        <PersonIcon className="w-4 h-4 text-gray-600" />
                      )}
                    </div>
                    <div className={`rounded-lg p-3 ${
                      msg.isUser ? 'bg-blue-500 text-white' : 'bg-gray-100'
                    }`}>
                      {msg.isImage ? (
                        <img src={msg.content} alt="Uploaded" className="max-w-xs rounded" />
                      ) : (
                        msg.content
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <div className="p-4 border-t">
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  accept="image/*"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                  title="发送图片"
                >
                  <ImageIcon className="w-5 h-5 text-gray-600" />
                </button>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入消息..."
                  className="flex-1 resize-none p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="1"
                />
                <Button
                  onClick={handleSendMessage}
                  className="bg-blue-500 hover:bg-blue-600"
                  title="发送消息"
                >
                  <PaperPlaneIcon className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
});