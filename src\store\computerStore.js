import { makeAutoObservable } from 'mobx';

class ComputerStore {
  computerInfo = {
    name: 'OptiPlex 3010',
    cpu: {
      model: '59B35X1',
      status: '正在读取中...'
    },
    memory: '4 GB 未知',
    storage: '465 GB 固态硬盘'
  };

  constructor() {
    makeAutoObservable(this);
  }

  updateComputerInfo(info) {
    this.computerInfo = { ...this.computerInfo, ...info };
  }
}

export const computerStore = new ComputerStore();