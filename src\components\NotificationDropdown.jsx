import React from 'react';
import { observer } from 'mobx-react-lite';
import { notificationStore } from '../store/notificationStore';
import { Button } from './ui/button';
import {
  Cross2Icon,
  CheckIcon,
  BellIcon,
  ExclamationTriangleIcon,
  ChatBubbleIcon,
  RocketIcon
} from '@radix-ui/react-icons';

const NotificationIcon = ({ type }) => {
  switch (type) {
    case 'project':
      return <RocketIcon className="w-4 h-4 text-blue-500" />;
    case 'system':
      return <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />;
    case 'message':
      return <ChatBubbleIcon className="w-4 h-4 text-green-500" />;
    default:
      return <BellIcon className="w-4 h-4 text-gray-500" />;
  }
};

export const NotificationDropdown = observer(({ onClose }) => {
  const { notifications, unreadCount, selectedNotification } = notificationStore;

  const formatTime = (date) => {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}天前`;
    if (hours > 0) return `${hours}小时前`;
    if (minutes > 0) return `${minutes}分钟前`;
    return '刚刚';
  };

  if (selectedNotification) {
    return (
      <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="font-medium">通知详情</h3>
          <button
            onClick={() => notificationStore.clearSelectedNotification()}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <Cross2Icon className="w-4 h-4" />
          </button>
        </div>
        <div className="p-4">
          <div className="flex items-center gap-3 mb-4">
            <NotificationIcon type={selectedNotification.type} />
            <div className="font-medium">{selectedNotification.title}</div>
          </div>
          <p className="text-gray-600 mb-4">{selectedNotification.content}</p>
          <div className="text-sm text-gray-500">
            {formatTime(selectedNotification.timestamp)}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      <div className="p-4 border-b flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">通知</h3>
          {unreadCount > 0 && (
            <span className="px-2 py-0.5 text-xs bg-red-100 text-red-600 rounded-full">
              {unreadCount}条未读
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-sm"
              onClick={() => notificationStore.markAllAsRead()}
            >
              <CheckIcon className="w-4 h-4 mr-1" />
              全部已读
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="text-sm"
            onClick={() => notificationStore.clearAll()}
          >
            <Cross2Icon className="w-4 h-4 mr-1" />
            清空
          </Button>
        </div>
      </div>
      
      <div className="max-h-[400px] overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            暂无通知
          </div>
        ) : (
          <div className="divide-y">
            {notifications.map(notification => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 cursor-pointer ${
                  !notification.read ? 'bg-blue-50' : ''
                }`}
                onClick={() => notificationStore.selectNotification(notification)}
              >
                <div className="flex items-start gap-3">
                  <NotificationIcon type={notification.type} />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium mb-1">{notification.title}</div>
                    <div className="text-sm text-gray-600 mb-1 truncate">
                      {notification.content}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatTime(notification.timestamp)}
                    </div>
                  </div>
                  {!notification.read && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});