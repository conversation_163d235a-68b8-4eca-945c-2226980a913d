import axios from 'axios';

import { fetchData } from './fetch';


export const testBaselineService = {
  // 获取项目列表
  getProjects: async (searchName = '') => {
    try {
      const params = new URLSearchParams({ name: searchName });
      const response = await axios.get(`${fetchData["BASE_URL"]}/api/projects/list?${params}`);
      return response.data;
    } catch (error) {
      throw new Error('获取项目列表失败');
    }
  },

  // 获取测试用例列表
  getTestCases: async (params) => {
    try {
      const response = await axios.get(`${fetchData["BASE_URL"]}/api/test-results/page`, { params });
      return response.data;
    } catch (error) {
      throw new Error('获取测试用例列表失败');
    }
  },

  // 创建测试用例
  createTestCase: async (data) => {
    try {
      const response = await axios.post(`${fetchData["BASE_URL"]}/api/test-results`, data);
      return response.data;
    } catch (error) {
      throw new Error('创建测试用例失败');
    }
  },

  // 更新测试用例
  updateTestCase: async (id, data) => {
    try {
      const response = await axios.put(`${fetchData["BASE_URL"]}/api/test-results/${id}`, data);
      return response.data;
    } catch (error) {
      throw new Error('更新测试用例失败');
    }
  },

  // 删除测试用例
  deleteTestCase: async (id) => {
    try {
      const response = await axios.delete(`${fetchData["BASE_URL"]}/api/test-results/${id}`);
      return response.data;
    } catch (error) {
      throw new Error('删除测试用例失败');
    }
  },

  // 获取员工列表
  getEmployees: async () => {
    try {
      const response = await axios.get(`${fetchData["BASE_URL"]}/api/employees/list`);
      return response.data;
    } catch (error) {
      throw new Error('获取员工列表失败');
    }
  },

  // 上传文件
  uploadFile: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await axios.post(`${fetchData["BASE_URL"]}/api/files/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw new Error('文件上传失败');
    }
  },

  // 下载文件
  downloadFile: async (fileId) => {
    try {
      const response = await axios.get(`${fetchData["BASE_URL"]}/api/files/download/${fileId}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error('文件下载失败');
    }
  }
}; 