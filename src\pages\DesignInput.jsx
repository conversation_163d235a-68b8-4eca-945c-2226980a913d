/**
 * 设计输入管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和选择
 * 2. 设计输入文件的增删改查
 * 3. 文件搜索和筛选
 * 4. 文件上传和下载
 * 5. 从项目输入提取文件
 * 6. 文件评审流程管理
 */

import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  UploadIcon,
  DownloadIcon,
  TrashIcon,
  EyeOpenIcon,
  Cross2Icon,
  ExclamationTriangleIcon,
  Pencil1Icon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { userStore } from '../store/userStore';
import {
  designInputApi,
  projectApi,
  projectInputApi,
  employeeApi,
  fileApi
} from '../services/designInputService';

// 错误提示组件 - 3秒后自动消失
const ErrorMessage = ({ message, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[100] flex items-center gap-2 bg-red-50 text-red-600 px-4 py-2 rounded-md shadow-lg">
      <ExclamationTriangleIcon className="w-4 h-4" />
      <span>{message}</span>
    </div>
  );
};

export const DesignInput = observer(() => {
  // 基础数据状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [designInputFiles, setDesignInputFiles] = useState([]);
  const [employees, setEmployees] = useState([]);

  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // 文件上传相关状态
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [uploadFileName, setUploadFileName] = useState('');
  const [selectedFiles, setSelectedFiles] = useState([]);

  // 从项目输入提取相关状态
  const [showUploadFromDesignDialog, setShowUploadFromDesignDialog] = useState(false);
  const [projectInFiles, setProjectInFiles] = useState([]);
  const [showFileSelect, setShowFileSelect] = useState(false);
  const [selectedProjectInFiles, setSelectedProjectInFiles] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState('');

  // 文件操作相关状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [viewingFile, setViewingFile] = useState(null);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingFile, setEditingFile] = useState(null);
  const [editUploadFiles, setEditUploadFiles] = useState([]);

  // 评审相关状态
  const [selectedForReview, setSelectedForReview] = useState([]);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [reviewLeader, setReviewLeader] = useState('');
  const [reviewers, setReviewers] = useState([]);
  const [reviewType, setReviewType] = useState('');
  const [reviewDate, setReviewDate] = useState('');
  const [showReviewerSelect, setShowReviewerSelect] = useState(false);
  const [reviewName, setReviewName] = useState('');

  // 下拉框状态
  const [isEmployeeDropdownOpen, setIsEmployeeDropdownOpen] = useState(false);
  const [isReviewLeaderDropdownOpen, setIsReviewLeaderDropdownOpen] = useState(false);
  const [isReviewTypeDropdownOpen, setIsReviewTypeDropdownOpen] = useState(false);

  // 错误提示状态
  const [fileUploadError, setFileUploadError] = useState('');

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = [
        { dropdown: 'employee-dropdown', select: 'employee-select', setter: setIsEmployeeDropdownOpen },
        { dropdown: 'review-leader-dropdown', select: 'review-leader-select', setter: setIsReviewLeaderDropdownOpen },
        { dropdown: 'review-type-dropdown', select: 'review-type-select', setter: setIsReviewTypeDropdownOpen }
      ];

      dropdowns.forEach(({ dropdown, select, setter }) => {
        const dropdownEl = document.getElementById(dropdown);
        const selectEl = document.getElementById(select);
        if (dropdownEl && !dropdownEl.contains(event.target) &&
            selectEl && !selectEl.contains(event.target)) {
          setter(false);
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 初始化项目列表
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const data = await projectApi.getProjects();
        setProjects(data);
        if (data.length > 0) {
          setSelectedProject(data[0]);
          fetchDesignInputs(data[0].id);
        }
      } catch (error) {
        console.error('获取项目列表出错:', error);
      }
    };
    fetchProjects();
  }, []);

  // 获取设计输入文件列表
  const fetchDesignInputs = async (projectId) => {
    try {
      const data = await designInputApi.getAllDesignInputs(projectId);
      setDesignInputFiles(data);
    } catch (error) {
      console.error('获取设计输入文件出错:', error);
    }
  };

  // 项目选择处理
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    fetchDesignInputs(project.id);
  };

  // 搜索处理
  const handleSearchSubmit = async () => {
    if (!selectedProject) return;
    try {
      const searchParams = {
        fileName: searchQuery,
        startDate: startDate ? `${startDate}T00:00:00` : '',
        endDate: endDate ? `${endDate}T23:59:59` : ''
      };
      const data = await designInputApi.getAllDesignInputs(selectedProject.id, searchParams);
      setDesignInputFiles(data);
    } catch (error) {
      console.error('搜索文件出错:', error);
    }
  };

  // 重置搜索
  const handleResetSearch = async () => {
    setSearchQuery('');
    setStartDate('');
    setEndDate('');
    if (selectedProject) {
      try {
        const data = await designInputApi.getAllDesignInputs(selectedProject.id, {});
        setDesignInputFiles(data);
      } catch (error) {
        console.error('重置搜索出错:', error);
        setFileUploadError('重置搜索失败');
      }
    }
  };

  // 文件上传处理
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setSelectedFiles(prev => [...prev, ...files]);
    }
  };

  // 创建设计输入
  const handleCreateDesignInput = async () => {
    if (!uploadFileName.trim()) {
      setFileUploadError('请输入文件名称');
      return;
    }

    try {
      const userData = userStore.getUserData();
      if (!userData?.id) {
        setFileUploadError('获取用户信息失败');
        return;
      }

      const formData = new FormData();
      const designInput = {
        id: 0,
        name: uploadFileName,
        fileSize: selectedFiles.reduce((total, file) => total + file.size, 0),
        uploadId: userData.id,
        uploadTime: new Date().toISOString(),
        projectId: selectedProject.id,
        status: 0
      };

      formData.append('designInput', new Blob([JSON.stringify(designInput)], {
        type: 'application/json'
      }));
      selectedFiles.forEach(file => formData.append('files', file));

      await designInputApi.createDesignInput(formData);

      // 重置状态并刷新列表
      fetchDesignInputs(selectedProject.id);
      setShowUploadDialog(false);
      setUploadFileName('');
      setFileUploadError('');
      setSelectedFiles([]);
    } catch (error) {
      console.error('创建设计输入出错:', error);
      setFileUploadError("创建设计输入失败");
    }
  };

  // 删除文件处理
  const handleDeleteClick = (file) => {
    setFileToDelete(file);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await designInputApi.deleteDesignInput(fileToDelete.id);
      fetchDesignInputs(selectedProject.id);
      setShowDeleteDialog(false);
      setFileToDelete(null);
    } catch (error) {
      console.error('删除文件出错:', error);
    }
  };

  // 查看文件详情
  const handleViewClick = async (file) => {
    try {
      const data = await designInputApi.getDesignInputDetail(file.id);
      setViewingFile(data);
      setShowViewDialog(true);
    } catch (error) {
      console.error('获取文件详情出错:', error);
    }
  };

  // 预览文件
  const handlePreviewFile = async (fileName) => {
    try {
      const previewUrl = await fileApi.previewFile(fileName, 'designinput');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('预览文件失败:', error);
      setFileUploadError('预览文件失败');
    }
  };

  // 格式化时间显示
  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    return new Date(dateTimeString).toLocaleString('zh-CN');
  };

  // 清除错误提示
  const clearError = () => setFileUploadError('');

  // 关闭上传对话框
  const handleCloseUploadDialog = () => {
    setShowUploadDialog(false);
    setUploadFileName('');
    setFileUploadError('');
    setSelectedFiles([]);
  };

  // 下载单个文件
  const handleDownload = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName, 'designinput');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载文件失败:', error);
      setFileUploadError('下载文件失败');
    }
  };

  // 批量下载文件
  const handleBatchDownload = async () => {
    if (!selectedProject) {
      setFileUploadError('请先选择项目');
      return;
    }
    try {
      const response = await fileApi.downloadArchive(4);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${selectedProject.name}_设计输入文件.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('打包下载失败:', error);
      setFileUploadError('打包下载失败');
    }
  };

  // 获取项目输入文件列表
  const fetchProjectInFiles = async (projectId) => {
    try {
      const data = await projectInputApi.getProjectInFiles(projectId);
      setProjectInFiles(data);
    } catch (error) {
      console.error('获取项目输入文件出错:', error);
    }
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployees();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表出错:', error);
      setFileUploadError('获取员工列表失败');
    }
  };

  // 打开从项目输入提取对话框
  const handleUploadFromDesignDialog = () => {
    if (selectedProject) {
      fetchProjectInFiles(selectedProject.id);
      fetchEmployees();
      setShowUploadFromDesignDialog(true);
    } else {
      setFileUploadError('请先选择项目');
    }
  };

  // 项目输入文件选择处理
  const handleProjectInFileSelect = (file) => {
    setSelectedProjectInFiles(prev => {
      const isSelected = prev.some(f => f.name === file.name);
      return isSelected
        ? prev.filter(f => f.name !== file.name)
        : [...prev, file];
    });
  };

  // 从项目输入创建设计输入
  const handleCreateFromProjectInput = async () => {
    // 验证输入
    if (!uploadFileName.trim()) {
      setFileUploadError('请输入文件名称');
      return;
    }
    if (!selectedEmployee) {
      setFileUploadError('请选择创建人');
      return;
    }
    if (selectedProjectInFiles.length === 0) {
      setFileUploadError('请选择项目输入文件');
      return;
    }

    try {
      const employee = employees.find(emp => emp.name === selectedEmployee);
      if (!employee) {
        throw new Error('未找到选中的员工');
      }

      const requestBody = selectedProjectInFiles.map(file => file.id);
      await designInputApi.createFromProjectInput(
        selectedProject.id,
        uploadFileName,
        employee.id,
        requestBody
      );

      // 重置状态并刷新列表
      fetchDesignInputs(selectedProject.id);
      setShowUploadFromDesignDialog(false);
      setUploadFileName('');
      setSelectedEmployee('');
      setSelectedProjectInFiles([]);
      setShowFileSelect(false);
    } catch (error) {
      console.error('创建设计输入出错:', error);
      setFileUploadError('创建设计输入失败');
    }
  };

  // 编辑文件处理
  const handleEditClick = async (file) => {
    try {
      setEditingFile(null);
      setEditUploadFiles([]);
      setShowEditDialog(true);
      const data = await designInputApi.getDesignInputDetail(file.id);
      setEditingFile(data);
    } catch (error) {
      console.error('获取文件详情出错:', error);
      setFileUploadError('获取文件详情失败');
    }
  };

  // 提交编辑
  const handleEditSubmit = async () => {
    if (!editingFile) return;
    try {
      const formData = new FormData();
      formData.append('designInput', new Blob([JSON.stringify(editingFile)], {
        type: 'application/json'
      }));
      editUploadFiles.forEach(file => formData.append('files', file));

      await designInputApi.updateDesignInput(editingFile.id, formData);
      fetchDesignInputs(selectedProject.id);
      setShowEditDialog(false);
      setEditingFile(null);
      setEditUploadFiles([]);
    } catch (error) {
      console.error('更新文件出错:', error);
      setFileUploadError('更新文件失败');
    }
  };

  // 编辑时文件上传处理
  const handleFileUploadInEdit = (files) => {
    const fileArray = Array.from(files);
    setEditUploadFiles(prev => [...prev, ...fileArray]);
  };

  // 移除编辑中的文件
  const handleRemoveEditFile = (index) => {
    setEditUploadFiles(prev => prev.filter((_, i) => i !== index));
  };

  // 删除已有文件
  const handleExistingFileDelete = (fileId) => {
    setEditingFile({
      ...editingFile,
      projectFiles: editingFile.projectFiles.filter(f => f.id !== fileId)
    });
  };

  // 打开上传对话框
  const handleOpenUploadDialog = () => {
    setUploadFileName('');
    setSelectedFiles([]);
    setFileUploadError('');
    setShowUploadDialog(true);
  };

  // 文件多选处理
  const handleSelectFile = (fileId, status) => {
    // 已提交或已通过的文件不允许选择
    if (status === 1 || status === 2) return;

    setSelectedForReview(prev =>
      prev.includes(fileId)
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  // 提交评审处理
  const handleSubmitReview = async () => {
    if (selectedForReview.length === 0) {
      setFileUploadError('请选择要提交评审的文件');
      return;
    }

    await fetchEmployees();
    setReviewLeader('');
    setReviewers([]);
    setReviewType('');
    setShowReviewerSelect(false);
    setShowReviewDialog(true);
  };

  // 评审组长选择处理
  const handleReviewLeaderSelect = (employeeId) => {
    const id = Number(employeeId);
    const employee = employees.find(emp => emp.id === id);
    if (employee) {
      setReviewLeader(id);
      // 如果评审组长在评审人员中，则移除
      setReviewers(prev => prev.filter(rid => rid !== id));
    }
  };

  // 提交评审表单
  const handleReviewFormSubmit = async () => {
    // 表单验证
    const validations = [
      { condition: !reviewName.trim(), message: '请输入评审名称' },
      { condition: !reviewLeader, message: '请选择评审组长' },
      { condition: !reviewers || reviewers.length === 0, message: '请选择评审人员' },
      { condition: !reviewType, message: '请选择评审类型' },
      { condition: !reviewDate, message: '请选择评审时间' }
    ];

    for (const { condition, message } of validations) {
      if (condition) {
        setFileUploadError(message);
        return;
      }
    }

    try {
      const reviewTypeMap = { design: 0, code: 1, document: 2 };
      const reviewTypeNumber = reviewTypeMap[reviewType];

      if (reviewTypeNumber === undefined) {
        throw new Error('无效的评审类型');
      }

      const requestBody = {
        designInputIds: selectedForReview,
        reviewType: reviewTypeNumber,
        reviewLeader: reviewLeader,
        reviewers: reviewers,
        name: reviewName,
        reviewDate: reviewDate
      };

      await designInputApi.submitForReview(requestBody);

      // 重置状态并刷新数据
      if (selectedProject) {
        fetchDesignInputs(selectedProject.id);
      }

      setShowReviewDialog(false);
      setReviewLeader('');
      setReviewers([]);
      setReviewType('');
      setSelectedForReview([]);
      setReviewName('');
      setReviewDate('');

      setFileUploadError('提交评审成功');
      setTimeout(() => setFileUploadError(''), 3000);
    } catch (error) {
      console.error('提交评审失败:', error);
      setFileUploadError('提交评审失败');
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 bg-gray-50 flex h-screen">
      {/* 错误提示组件 */}
      {fileUploadError && <ErrorMessage message={fileUploadError} onClose={clearError} />}

      {/* 左侧项目列表区域 */}
      <div className="w-80 bg-white rounded-lg shadow-sm p-6 mr-4">
        <h3 className="text-lg font-semibold mb-4">项目列表</h3>
        <div className="space-y-3">
          {projects.map((project) => (
            <div
              key={project.id}
              className={`p-3 rounded-lg cursor-pointer transition-colors ${selectedProject?.id === project.id
                  ? 'bg-blue-50 border-blue-200 border'
                  : 'hover:bg-gray-50 border border-transparent'
                }`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className={`text-sm mt-1 px-2 py-0.5 inline-block rounded ${
                project.status === 0 ? 'text-gray-600 bg-gray-100' :
                project.status === 1 ? 'text-blue-600 bg-blue-50' :
                project.status === 2 ? 'text-green-600 bg-green-50' : ''
              }`}>
                {project.status === 0 ? '未开始' :
                 project.status === 1 ? '进行中' :
                 project.status === 2 ? '已结束' : '未知状态'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 页面标题区域 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">{selectedProject?.name}</h1>
            <p className="text-gray-500">设计输入文件</p>
          </div>
        </div>

        {/* 搜索和操作按钮区域 */}
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="relative w-36">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索文件..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
                <span className="text-gray-400">至</span>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
              <Button
                onClick={handleSearchSubmit}
                className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
              >
                <MagnifyingGlassIcon className="w-4 h-4" />
                搜索
              </Button>
              <Button
                onClick={handleResetSearch}
                variant="outline"
                className="flex items-center gap-2"
              >
                重置
              </Button>
              <Button
                onClick={handleOpenUploadDialog}
                className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
              >
                <UploadIcon className="w-4 h-4" />
                上传文件
              </Button>
              <Button
                onClick={handleUploadFromDesignDialog}
                variant="outline"
                className="flex items-center gap-2"
              >
                <UploadIcon className="w-4 h-4" />
                从项目输入提取
              </Button>
              <Button
                onClick={() => handleBatchDownload()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <DownloadIcon className="w-4 h-4" />
                全量打包
              </Button>
              <Button
                onClick={handleSubmitReview}
                disabled={selectedForReview.length === 0}
                className={`flex items-center gap-2 ${
                  selectedForReview.length > 0
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                }`}
              >
                提交评审
              </Button>
            </div>
          </div>
        </div>

        {/* 设计输入文件列表区域 */}
        <div className="bg-white rounded-lg shadow-sm flex-1 overflow-hidden">
          {/* 表格头部 */}
          <div className="p-4 bg-gray-50 border-b">
            <div className="flex items-center justify-between">
              <div className="grid grid-cols-5 gap-8 flex-1">
                <div className="text-sm font-medium text-gray-500">文件名称</div>
                <div className="text-sm font-medium text-gray-500">文件状态</div>
                <div className="text-sm font-medium text-gray-500">上传时间</div>
                <div className="text-sm font-medium text-gray-500">上传人</div>
              </div>
              <div className="w-[120px] text-sm font-medium text-gray-500 text-right">操作</div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            {designInputFiles.map((file) => (
              <div
                key={file.id}
                className="p-4 border-b hover:bg-gray-50 group"
              >
                <div className="flex items-center justify-between">
                  <div className="grid grid-cols-5 gap-8 flex-1">
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedForReview.includes(file.id)}
                        onChange={() => handleSelectFile(file.id, file.status)}
                        disabled={file.status === 1 || file.status === 2}
                        className={`w-4 h-4 rounded border-gray-300 ${
                          file.status === 1 || file.status === 2 
                            ? 'bg-gray-100 cursor-not-allowed' 
                            : 'text-blue-600 focus:ring-blue-500'
                        }`}
                        title={file.status === 1 || file.status === 2 ? '已提交或已通过的文件不可选择' : ''}
                      />
                      <div className="text-sm font-medium">
                        {file.name?.replace(/\.txt$/, '') || '未命名文件'}
                      </div>
                    </div>
                    <div className="text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        file.status === 0 ? 'bg-gray-100 text-gray-600' :
                        file.status === 1 ? 'bg-blue-100 text-blue-600' :
                        file.status === 2 ? 'bg-green-100 text-green-600' :
                        file.status === 3 ? 'bg-red-100 text-red-600' :
                        'bg-gray-100 text-gray-600'
                      }`}>
                        {file.status === 0 ? '未提交' :
                         file.status === 1 ? '已提交' :
                         file.status === 2 ? '已通过' :
                         file.status === 3 ? '未通过' :
                         '未知状态'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatDateTime(file.uploadTime)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {file.uploaderName || '-'}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleViewClick(file)}
                      className="p-1 rounded hover:bg-gray-100"
                    >
                      <EyeOpenIcon className="w-4 h-4 text-blue-600" />
                    </button>
                    <button
                      onClick={() => handleEditClick(file)}
                      className="p-1 rounded hover:bg-gray-100"
                    >
                      <Pencil1Icon className="w-4 h-4 text-blue-600" />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(file)}
                      className="p-1 rounded hover:bg-gray-100"
                    >
                      <TrashIcon className="w-4 h-4 text-red-600" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 文件上传对话框 */}
      {showUploadDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96">
            <h3 className="text-lg font-semibold mb-4">上传设计输入文件</h3>

            {/* 项目名称 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                项目名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={selectedProject?.name || ''}
                disabled
                className="w-full px-3 py-2 border rounded-lg bg-gray-50"
              />
            </div>

            {/* 文件名称 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                文件名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={uploadFileName}
                onChange={(e) => setUploadFileName(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入文件名称"
              />
            </div>

            {/* 文件上传 */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                上传文件
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  className="hidden"
                  id="file-upload"
                  onChange={handleFileUpload}
                  multiple
                />
                <label
                  htmlFor="file-upload"
                  className="cursor-pointer flex flex-col items-center"
                >
                  <UploadIcon className="w-6 h-6 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-600">
                    点击或拖拽文件到此处上传（支持多文件）
                  </span>
                </label>
              </div>

              {selectedFiles.length > 0 && (
                <div className="mt-2 space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">文件 {index + 1}：</span>
                            {file.name}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-600"
                          onClick={() => {
                            setSelectedFiles(files => files.filter((_, i) => i !== index));
                          }}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCloseUploadDialog}
              >
                取消
              </Button>
              <Button onClick={handleCreateDesignInput}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      {showDeleteDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-xl w-80">
            <h3 className="text-lg font-medium mb-2">确认删除</h3>
            <p className="text-gray-600">
              确定要删除项目 &ldquo;{fileToDelete?.name}&rdquo; 吗？此操作不可恢复。
            </p>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                className="px-6"
                onClick={() => {
                  setShowDeleteDialog(false);
                  setFileToDelete(null);
                }}
              >
                取消
              </Button>
              <Button
                size="sm"
                className="px-6 bg-red-500 hover:bg-red-600 text-white border-none"
                onClick={handleDeleteConfirm}
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 文件详情查看对话框 */}
      {showViewDialog && viewingFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-[600px] max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">文件详情</h3>

            <div className="space-y-4">
              <div>
                <label className="text-sm text-gray-500">所属项目</label>
                <div className="font-medium">{selectedProject?.name}</div>
              </div>

              <div>
                <label className="text-sm text-gray-500">文件名称</label>
                <div className="font-medium">
                  {viewingFile.name?.replace(/\.txt$/, '') || '未命名文件'}
                </div>
              </div>

              <div>
                <label className="text-sm text-gray-500">上传时间</label>
                <div className="font-medium">{formatDateTime(viewingFile.uploadTime)}</div>
              </div>

              {/* 添加附件区域 */}
              {viewingFile.projectFiles && viewingFile.projectFiles.length > 0 && (
                <div>
                  <label className="text-sm text-gray-500 block mb-2">附件列表</label>
                  <div className="space-y-2">
                    {viewingFile.projectFiles.map((projectFile, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-lg hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-gray-900">{projectFile.name}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            type="button"
                            className="text-blue-600 hover:text-blue-800"
                            onClick={() => handlePreviewFile(projectFile.name)}
                          >
                            <EyeOpenIcon className="w-4 h-4" />
                          </button>
                          <button
                            type="button"
                            className="text-blue-600 hover:text-blue-800"
                            onClick={() => handleDownload(projectFile.name)}
                          >
                            <DownloadIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewDialog(false);
                  setViewingFile(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}



      {/* 从项目输入提取文件对话框 */}
      {showUploadFromDesignDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-[600px]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">从项目输入提取</h3>
              <button
                onClick={() => setShowUploadFromDesignDialog(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <Cross2Icon className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* 项目名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  项目名称
                </label>
                <input
                  type="text"
                  value={selectedProject?.name || ''}
                  disabled
                  className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                />
              </div>

              {/* 文件名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  文件名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={uploadFileName}
                  onChange={(e) => setUploadFileName(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入文件名称"
                />
              </div>

              {/* 添加创建人选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  创建人 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    id="employee-select"
                    onClick={() => setIsEmployeeDropdownOpen(!isEmployeeDropdownOpen)}
                    className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                      !selectedEmployee ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                    }`}
                  >
                    <span className={selectedEmployee ? 'text-gray-900' : 'text-gray-400'}>
                      {selectedEmployee ? employees.find(emp => emp.name === selectedEmployee)?.name : '请选择创建人'}
                    </span>
                    <svg 
                      className={`h-5 w-5 text-gray-400 transform transition-transform ${isEmployeeDropdownOpen ? 'rotate-180' : ''}`} 
                      viewBox="0 0 20 20" 
                      fill="currentColor"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  </div>
                  
                  {isEmployeeDropdownOpen && (
                    <div 
                      id="employee-dropdown"
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="py-1 max-h-60 overflow-auto">
                        {employees.map(employee => (
                          <div
                            key={employee.id}
                            onClick={() => {
                              setSelectedEmployee(employee.name);
                              setIsEmployeeDropdownOpen(false);
                            }}
                            className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                          >
                            {employee.name}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 项目输入文件 */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  项目输入文件 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto"
                    onClick={() => setShowFileSelect(!showFileSelect)}
                  >
                    {selectedProjectInFiles.length > 0 ? (
                      <div className="flex flex-wrap">
                        {selectedProjectInFiles.map((file, index) => (
                          <div key={index} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                            <span className="text-sm">{file.name}</span>
                            <button
                              type="button"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                setSelectedProjectInFiles(prev => prev.filter(f => f.name !== file.name));
                                setShowFileSelect(true);
                              }}
                              className="text-gray-400 hover:text-red-500 ml-1"
                            >
                              <Cross2Icon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="text-gray-400">请选择项目输入文件</span>
                    )}
                  </div>
                  {showFileSelect && (
                    <div
                      className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="flex flex-wrap p-2 gap-2">
                        {projectInFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-2 cursor-pointer"
                            onClick={() => handleProjectInFileSelect(file)}
                          >
                            <input
                              type="checkbox"
                              checked={selectedProjectInFiles.some(f => f.name === file.name)}
                              onChange={() => { }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <div className={`px-2 py-1 rounded whitespace-nowrap ${selectedProjectInFiles.some(f => f.name === file.name) ? 'bg-blue-50' : ''
                              }`}>
                              <span className="text-sm">{file.name}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowUploadFromDesignDialog(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateFromProjectInput}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 文件编辑对话框 */}
      {showEditDialog && editingFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-[600px] max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">编辑文件</h3>
              <button
                onClick={() => {
                  setShowEditDialog(false);
                  setEditingFile(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <Cross2Icon className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  所属项目
                </label>
                <input
                  type="text"
                  value={selectedProject?.name || ''}
                  disabled
                  className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  文件名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={editingFile.name}
                  onChange={(e) => setEditingFile({ ...editingFile, name: e.target.value })}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 添加附件列表 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  附件列表
                </label>
                <div className="space-y-2">
                  {editingFile.projectFiles && editingFile.projectFiles.map((projectFile, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-lg hover:bg-gray-100"
                    >
                      <div className="flex items-center gap-2">
                        <div className="text-sm text-gray-900">{projectFile.name}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          type="button"
                          className="text-blue-600 hover:text-blue-800"
                          onClick={() => handlePreviewFile(projectFile.name)}
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          type="button"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleExistingFileDelete(projectFile.id)}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 文件上传框 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">上传新文件</label>
                  <label
                    htmlFor="file-upload-edit"
                    className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors duration-200"
                    onDrop={(e) => {
                      e.preventDefault();
                      const droppedFiles = e.dataTransfer.files;
                      handleFileUploadInEdit(droppedFiles);
                    }}
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.currentTarget.classList.add('border-blue-400');
                    }}
                    onDragLeave={(e) => {
                      e.preventDefault();
                      e.currentTarget.classList.remove('border-blue-400');
                    }}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-8 w-8 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="text-sm text-gray-600">
                        <span className="text-blue-600 hover:text-blue-500">选择文件</span>
                        <span className="pl-1">或拖拽文件到此处</span>
                      </div>
                    </div>
                  </label>
                </div>

                {/* 添加隐藏的文件输入框 */}
                <input
                  id="file-upload-edit"
                  type="file"
                  multiple
                  className="hidden"
                  onChange={(e) => handleFileUploadInEdit(e.target.files)}
                />

                {/* 显示新上传的文件列表 */}
                {editUploadFiles.length > 0 && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">新上传的文件</label>
                    <div className="space-y-2">
                      {editUploadFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{file.name}</span>
                          </div>
                          <button
                            onClick={() => handleRemoveEditFile(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditDialog(false);
                  setEditingFile(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleEditSubmit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 评审提交对话框 */}
      {showReviewDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-[500px]">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">提交评审</h3>
              <button
                onClick={() => setShowReviewDialog(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <Cross2Icon className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* 评审名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评审名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={reviewName}
                  onChange={(e) => setReviewName(e.target.value)}
                  placeholder="请输入评审名称"
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 评审组长 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评审组长 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    id="review-leader-select"
                    onClick={() => setIsReviewLeaderDropdownOpen(!isReviewLeaderDropdownOpen)}
                    className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                      !reviewLeader ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                    }`}
                  >
                    <span className={reviewLeader ? 'text-gray-900' : 'text-gray-400'}>
                      {reviewLeader ? employees.find(emp => emp.id === Number(reviewLeader))?.name : '请选择评审组长'}
                    </span>
                    <svg 
                      className={`h-5 w-5 text-gray-400 transform transition-transform ${isReviewLeaderDropdownOpen ? 'rotate-180' : ''}`} 
                      viewBox="0 0 20 20" 
                      fill="currentColor"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  </div>
                  
                  {isReviewLeaderDropdownOpen && (
                    <div 
                      id="review-leader-dropdown"
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="py-1 max-h-60 overflow-auto">
                        {employees.map(employee => (
                          <div
                            key={employee.id}
                            onClick={() => {
                              handleReviewLeaderSelect(employee.id);
                              setIsReviewLeaderDropdownOpen(false);
                            }}
                            className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                          >
                            {employee.name}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 评审人员 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评审人员 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div 
                    className="reviewer-select w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto"
                    onClick={() => setShowReviewerSelect(!showReviewerSelect)}
                  >
                    {reviewers.length > 0 ? (
                      <div className="flex flex-wrap">
                        {reviewers.map((id) => {
                          // 确保使用数字类型进行查找
                          const employee = employees.find(e => e.id === Number(id));
                          return (
                            <div key={id} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                              <span className="text-sm">{employee?.name}</span>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setReviewers(prev => prev.filter(rid => rid !== id));
                                }}
                                className="text-gray-400 hover:text-red-500 ml-1"
                              >
                                <Cross2Icon className="w-4 h-4" />
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <span className="text-gray-400">请选择评审人员</span>
                    )}
                  </div>
                  {showReviewerSelect && (
                    <div 
                      className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto reviewer-select"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="flex flex-wrap p-2 gap-2">
                        {employees
                          .filter(emp => emp.id.toString() !== reviewLeader)
                          .map((employee) => (
                            <div
                              key={employee.id}
                              className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                              onClick={() => {
                                // 使用数字类型的 ID
                                const id = employee.id;
                                setReviewers(prev => {
                                  if (prev.includes(id)) {
                                    return prev.filter(rid => rid !== id);
                                  } else {
                                    return [...prev, id];
                                  }
                                });
                              }}
                            >
                              <input
                                type="checkbox"
                                // 使用数字类型进行比较
                                checked={reviewers.includes(employee.id)}
                                onChange={() => {}}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <div className={`flex-1 px-2 py-1 rounded ${
                                reviewers.includes(employee.id) ? 'bg-blue-50' : ''
                              }`}>
                                <span className="text-sm">{employee.name}</span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 评审时间 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评审时间 <span className="text-red-500">*</span>
                </label>
                <input
                  type="datetime-local"
                  value={reviewDate}
                  onChange={(e) => setReviewDate(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  step="1"
                />
              </div>

              {/* 评审类型 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  评审类型 <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    id="review-type-select"
                    onClick={() => setIsReviewTypeDropdownOpen(!isReviewTypeDropdownOpen)}
                    className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                      !reviewType ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                    }`}
                  >
                    <span className={reviewType ? 'text-gray-900' : 'text-gray-400'}>
                      {reviewType ? 
                        (reviewType === 'design' ? '设计评审' : 
                         reviewType === 'code' ? '代码评审' : 
                         reviewType === 'document' ? '文档评审' : '请选择评审类型') 
                        : '请选择评审类型'}
                    </span>
                    <svg 
                      className={`h-5 w-5 text-gray-400 transform transition-transform ${isReviewTypeDropdownOpen ? 'rotate-180' : ''}`} 
                      viewBox="0 0 20 20" 
                      fill="currentColor"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  </div>
                  
                  {isReviewTypeDropdownOpen && (
                    <div 
                      id="review-type-dropdown"
                      className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="py-1 max-h-60 overflow-auto">
                        {[
                          { value: 'design', label: '设计评审' },
                          { value: 'code', label: '代码评审' },
                          { value: 'document', label: '文档评审' }
                        ].map(type => (
                          <div
                            key={type.value}
                            onClick={() => {
                              setReviewType(type.value);
                              setIsReviewTypeDropdownOpen(false);
                            }}
                            className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                          >
                            {type.label}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowReviewDialog(false)}
              >
                取消
              </Button>
              <Button 
                onClick={handleReviewFormSubmit}
                style={{ backgroundColor: '#0070f3', color: 'white' }}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});