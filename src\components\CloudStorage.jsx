import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { 
  Cross2Icon, 
  MagnifyingGlassIcon,
  BoxIcon, // Changed from FolderIcon to BoxIcon
  FileTextIcon, // Changed from FileIcon to FileTextIcon
  PlusIcon,
  TrashIcon,
  DownloadIcon,
  ChevronRightIcon
} from '@radix-ui/react-icons';
import { fileStore } from '../store/fileStore';
import { Button } from './ui/button';

const tabs = [
  { id: 'my-files', name: '我的文件' },
  { id: 'shared-by-me', name: '我的分享' },
  { id: 'shared-with-me', name: '他人分享' },
  { id: 'transfers', name: '传输' }
];

export const CloudStorage = observer(() => {
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const { isOpen, currentTab, currentFolder, searchQuery } = fileStore;

  if (!isOpen) return null;

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      fileStore.createFolder(newFolderName.trim());
      setNewFolderName('');
      setShowNewFolderDialog(false);
    }
  };

  const files = fileStore.getCurrentFiles();

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white w-[1000px] h-[600px] rounded-lg shadow-xl flex flex-col">
        {/* Header */}
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold">云盘存储</h2>
            {currentFolder && (
              <>
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
                <span>{currentFolder.name}</span>
              </>
            )}
          </div>
          <button
            onClick={() => fileStore.toggleModal()}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Cross2Icon className="w-4 h-4" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`px-4 py-2 ${
                currentTab === tab.id 
                  ? 'text-blue-600 border-b-2 border-blue-600' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => fileStore.setCurrentTab(tab.id)}
            >
              {tab.name}
            </button>
          ))}
        </div>

        {/* Toolbar */}
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex gap-2">
            {currentTab === 'my-files' && (
              <>
                <Button
                  variant="outline"
                  className="flex items-center gap-1"
                  onClick={() => setShowNewFolderDialog(true)}
                >
                  <PlusIcon className="w-4 h-4" />
                  新建文件夹
                </Button>
                <Button variant="outline" className="flex items-center gap-1">
                  <PlusIcon className="w-4 h-4" />
                  上传文件
                </Button>
              </>
            )}
          </div>
          <div className="relative w-64">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => fileStore.setSearchQuery(e.target.value)}
              placeholder="搜索文件..."
              className="w-full pl-8 pr-4 py-1.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        {/* File Grid */}
        <div className="flex-1 p-4 overflow-y-auto">
          {currentFolder && (
            <button
              className="mb-4 text-blue-600 hover:text-blue-700 flex items-center gap-1"
              onClick={() => fileStore.setCurrentFolder(null)}
            >
              <ChevronRightIcon className="w-4 h-4 rotate-180" />
              返回上级
            </button>
          )}
          
          <div className="grid grid-cols-4 gap-4">
            {files.map((file) => (
              <div
                key={file.id}
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer group"
                onDoubleClick={() => file.type === 'folder' && fileStore.setCurrentFolder(file)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {file.type === 'folder' ? (
                      <BoxIcon className="w-8 h-8 text-blue-500" />
                    ) : (
                      <FileTextIcon className="w-8 h-8 text-gray-400" />
                    )}
                    <div>
                      <div className="font-medium truncate" title={file.name}>
                        {file.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {file.size && `大小：${file.size}`}
                      </div>
                    </div>
                  </div>
                  {file.isNew && (
                    <span className="px-1.5 py-0.5 text-xs bg-green-100 text-green-800 rounded">
                      新
                    </span>
                  )}
                </div>
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>{file.modifiedTime}</span>
                  <div className="opacity-0 group-hover:opacity-100 flex gap-1">
                    <button 
                      className="p-1 hover:bg-gray-200 rounded"
                      onClick={(e) => {
                        e.stopPropagation();
                        fileStore.deleteFile(file.id);
                      }}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                    {file.type === 'file' && (
                      <button className="p-1 hover:bg-gray-200 rounded">
                        <DownloadIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* New Folder Dialog */}
      {showNewFolderDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96">
            <h3 className="text-lg font-semibold mb-4">新建文件夹</h3>
            <input
              type="text"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="请输入文件夹名称"
              className="w-full px-3 py-2 border rounded-lg mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewFolderDialog(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateFolder}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});