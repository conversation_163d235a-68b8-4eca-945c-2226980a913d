import { makeAutoObservable } from 'mobx';

class NotificationStore {
  notifications = [
    {
      id: 1,
      title: '项目进度提醒',
      content: '企业门户网站重构项目已完成75%',
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      read: false,
      type: 'project'
    },
    {
      id: 2,
      title: '系统通知',
      content: '系统将于今晚22:00进行例行维护',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
      read: false,
      type: 'system'
    },
    {
      id: 3,
      title: '消息提醒',
      content: '张工在项目群中@了你',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
      read: true,
      type: 'message'
    }
  ];

  selectedNotification = null;

  constructor() {
    makeAutoObservable(this);
  }

  get unreadCount() {
    return this.notifications.filter(n => !n.read).length;
  }

  markAsRead(id) {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
    }
  }

  markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
  }

  clearAll() {
    this.notifications = [];
    this.selectedNotification = null;
  }

  selectNotification(notification) {
    this.selectedNotification = notification;
    this.markAsRead(notification.id);
  }

  clearSelectedNotification() {
    this.selectedNotification = null;
  }

  addNotification(notification) {
    this.notifications.unshift({
      id: Date.now(),
      timestamp: new Date(),
      read: false,
      ...notification
    });
  }
}

export const notificationStore = new NotificationStore();