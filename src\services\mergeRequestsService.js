// GitLab API 服务配置
// const fetchData["API_BASE_URL"] = 'http://172.27.1.61/api/v4';
import { userStore } from '../store/userStore';

import { fetchData } from './fetch';

const headers = {
  'Private-Token': userStore.getUserData()?.accessToken || '',
  'Content-Type': 'application/json',
};

export const mergeRequestsService = {
  // 通过SSH Key获取用户信息
  async getUserBySSHKey(sshKey) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/user`, {
      headers,
    });
    const userData = await response.json();
    return userData;
  },

  // 获取用户的项目列表
  async getUserProjects(userId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/users/${userId}/projects`, {
      headers,
    });
    const projectsData = await response.json();
    return projectsData.map(project => ({
      id: project.id,
      name: project.name
    }));
  },

  // 获取项目的合并请求列表
  async getProjectMergeRequests(projectId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests`, {
      headers,
    });
    const mrData = await response.json();
    
    return mrData.map(mr => ({
      id: `MR-${mr.iid}`,
      projectId: mr.project_id,
      title: mr.title,
      sourceBranch: mr.source_branch,
      targetBranch: mr.target_branch,
      author: mr.author.name,
      reviewers: mr.reviewers?.map(r => r.name) || [],
      status: mr.state === 'opened' || mr.state === 'open' || mr.state === 'pending' ? 'open' :
              mr.state === 'merged' ? 'merged' : 
              mr.state === 'closed' ? 'closed' : 'reviewing',
      createdAt: new Date(mr.created_at).toLocaleString(),
      updatedAt: new Date(mr.updated_at).toLocaleString(),
      description: mr.description,
      changes: {
        added: mr.changes_count || 0,
        modified: 0,
        deleted: 0
      },
      commits: [],
      comments: []
    }));
  },

  // 获取项目的分支列表
  async getProjectBranches(projectId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/repository/branches`, {
      headers,
    });
    const branchData = await response.json();
    return branchData.map(branch => ({
      name: branch.name,
      default: branch.default
    }));
  },

  // 创建新的合并请求
  async createMergeRequest(projectId, data) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        source_branch: data.sourceBranch,
        target_branch: data.targetBranch,
        title: data.title,
        description: data.description
      })
    });
    return response.json();
  },

  // 接受合并请求
  async acceptMergeRequest(projectId, mrId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests/${mrId}/merge`, {
      method: 'PUT',
      headers,
    });
    return response.json();
  },

  // 拒绝（关闭）合并请求
  async rejectMergeRequest(projectId, mrId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests/${mrId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({
        state_event: 'close'
      })
    });
    return response.json();
  },

  // 获取合并请求的差异
  async getMergeRequestDiff(projectId, mrId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests/${mrId}/changes`, {
      headers,
    });
    const data = await response.json();
    return data.changes.map(change => ({
      ...change,
      diff: change.diff || '',
      old_path: change.old_path || change.path || '',
      new_path: change.new_path || change.path || ''
    }));
  },

  // 获取项目的所有文件
  async getProjectFiles(projectId, ref = 'main') {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/repository/tree?ref=${ref}&recursive=true&per_page=100`, {
      headers,
    });
    const filesData = await response.json();
    return filesData.filter(item => item.type === 'blob').map(file => ({
      path: file.path,
      name: file.name,
      id: file.id
    }));
  },

  // 获取文件内容
  async getFileContent(projectId, filePath, ref = 'main') {
    const encodedPath = encodeURIComponent(filePath);
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/repository/files/${encodedPath}/raw?ref=${ref}`, {
      headers,
    });
    if (!response.ok) {
      return null;
    }
    const content = await response.text();
    return content;
  },

  // 删除合并请求
  async deleteMergeRequest(projectId, mrId) {
    const response = await fetch(`${fetchData["API_BASE_URL"]}/projects/${projectId}/merge_requests/${mrId}`, {
      method: 'DELETE',
      headers,
    });
    return response.ok;
  }
}; 