import axios from 'axios';

import { fetchData } from './fetch';

const STAFF_URL = 'http://172.27.1.153:8081';

// 风险管理相关接口
export const riskApi = {
  // 获取所有风险列表
  getAllRisks: (page = 0, size = 10, projectId = '', name = '', level = '', status = '') => {
    let url = `${fetchData["STAFF_URL"]}/api/risk/all?page=${page}&size=${size}`;
    if (projectId) url += `&projectId=${projectId}`;
    if (name) url += `&name=${encodeURIComponent(name)}`;
    if (level) url += `&level=${level}`;
    if (status) url += `&status=${status}`;
    return fetch(url).then(res => res.json());
  },

  // 获取单个风险详情
  getRiskDetail: (riskId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/risk/get/${riskId}`).then(res => res.json()),

  // 创建新风险
  createRisk: (riskData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/risk/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(riskData)
    }).then(res => res.json()),

  // 更新风险信息
  updateRisk: (riskId, riskData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/risk/update/${riskId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(riskData)
    }).then(res => res.json()),

  // 删除风险
  deleteRisk: (riskId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/risk/delete/${riskId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 获取风险概览数据
  getRiskOverview: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/risk/overview`).then(res => res.json()),
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),

  // 搜索项目
  searchProjects: (keyword) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/search?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
}; 