import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  ChevronDownIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { projectApi, designProposalApi, fileApi, employeeApi } from '../services/designProposalService';

// 方案类型配置
const proposalTypes = [
  { id: '0', name: '架构设计' },
  { id: '1', name: '数据库设计' },
  { id: '2', name: '接口设计' },
  { id: '3', name: '界面设计' }
];

// 方案状态配置
const proposalStatuses = [
  { id: '0', name: '草稿', color: 'bg-gray-100 text-gray-800' },
  { id: '1', name: '评审中', color: 'bg-yellow-100 text-yellow-800' },
  { id: '2', name: '已通过', color: 'bg-green-100 text-green-800' },
  { id: '3', name: '未通过', color: 'bg-red-100 text-red-800' }
];

export const DesignProposal = observer(() => {
  // 搜索和筛选状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // 弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showTemplateListModal, setShowTemplateListModal] = useState(false);

  // 数据状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [employees, setEmployees] = useState([]);
  const [proposals, setProposals] = useState([]);
  const [templateList, setTemplateList] = useState([]);

  // 表单数据状态
  const [selectedProposal, setSelectedProposal] = useState(null);
  const [newProposal, setNewProposal] = useState({
    name: '',
    type: '',
    description: '',
    content: '',
    attachments: []
  });
  const [editingProposal, setEditingProposal] = useState(null);
  const [deletingProposal, setDeletingProposal] = useState(null);
  const [submittingProposal, setSubmittingProposal] = useState(null);
  const [templateFile, setTemplateFile] = useState(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);

  // 评审相关状态
  const [selectedReviewers, setSelectedReviewers] = useState([]);
  const [reviewDeadline, setReviewDeadline] = useState('');
  const [selectedReviewLeader, setSelectedReviewLeader] = useState('');
  const [showReviewLeaderSelect, setShowReviewLeaderSelect] = useState(false);
  const [showReviewerSelect, setShowReviewerSelect] = useState(false);

  // 下拉框状态
  const [isNewTypeDropdownOpen, setIsNewTypeDropdownOpen] = useState(false);
  const [isEditTypeDropdownOpen, setIsEditTypeDropdownOpen] = useState(false);
  const [isNewEmployeeDropdownOpen, setIsNewEmployeeDropdownOpen] = useState(false);
  const [isEditEmployeeDropdownOpen, setIsEditEmployeeDropdownOpen] = useState(false);
  const [isReviewLeaderDropdownOpen, setIsReviewLeaderDropdownOpen] = useState(false);

  // 其他状态
  const [projectSearchQuery, setProjectSearchQuery] = useState('');

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 并行加载项目和员工数据
        const [projectsData, employeesData] = await Promise.all([
          projectApi.getProjectList(),
          employeeApi.getEmployeeList()
        ]);

        setProjects(projectsData);
        setEmployees(employeesData);

        // 如果有项目，默认选择第一个并加载其方案
        if (projectsData.length > 0) {
          setSelectedProject(projectsData[0].id.toString());
          await loadProposals(projectsData[0].id);
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
        toast.error('加载数据失败');
      }
    };

    initializeData();
  }, []);

  // 加载方案列表
  const loadProposals = async (projectId, page = 0, params = {}) => {
    try {
      const data = await designProposalApi.getProposalList(projectId, page, 10, params);
      setProposals(data.content || []);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
      if (page === 0) setCurrentPage(1);
    } catch (error) {
      console.error('获取方案列表失败:', error);
      toast.error('获取方案列表失败');
    }
  };

  // 查看方案详情
  const handleViewDetail = async (proposal) => {
    try {
      const data = await designProposalApi.getProposalDetail(proposal.id);
      setSelectedProposal({
        ...data,
        attachments: data.projectFiles?.map(file => ({
          id: file.id,
          name: file.name,
          size: `${(file.size / 1024).toFixed(2)} KB`,
          path: file.path
        })) || []
      });
      setShowDetailModal(true);
    } catch (error) {
      console.error('获取方案详情失败:', error);
      toast.error('获取方案详情失败');
    }
  };

  // 搜索方案列表
  const fetchProposalList = async () => {
    const params = {};
    if (searchQuery) params.name = searchQuery;
    if (selectedType) params.type = parseInt(selectedType);
    if (selectedStatus) params.status = parseInt(selectedStatus);

    await loadProposals(selectedProject, currentPage - 1, params);
  };

  // 关闭新建弹窗并重置状态
  const handleCloseNewModal = async () => {
    setShowNewModal(false);
    resetNewProposalForm();
    await loadProposals(selectedProject);
  };

  // 重置新建表单
  const resetNewProposalForm = () => {
    setNewProposal({
      name: '',
      type: '',
      description: '',
      content: '',
      attachments: []
    });
    setIsNewTypeDropdownOpen(false);
    setIsNewEmployeeDropdownOpen(false);
  };

  // 表单验证
  const validateProposalForm = (proposal) => {
    const errors = {};
    if (!proposal.name) errors.name = '请输入方案名称!';
    if (!proposal.type) errors.type = '请选择方案类型!';
    if (!proposal.author) errors.author = '请选择创建人!';
    return errors;
  };

  // 显示表单错误
  const showFormErrors = (errors) => {
    // 清除之前的错误信息
    document.querySelectorAll('.error-message').forEach(el => el.remove());

    Object.entries(errors).forEach(([field, message]) => {
      const input = document.querySelector(`[name="${field}"]`);
      if (input) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
      }
    });
  };

  // 创建方案
  const handleCreateProposal = async () => {
    const errors = validateProposalForm(newProposal);
    if (Object.keys(errors).length > 0) {
      showFormErrors(errors);
      return;
    }

    try {
      const formData = new FormData();

      // 添加附件文件
      newProposal.attachments.forEach(file => {
        formData.append('files', file.originalFile);
      });

      // 构建方案数据
      const schemeDesign = {
        name: newProposal.name,
        type: parseInt(newProposal.type),
        status: 0, // 默认为草稿状态
        author: parseInt(newProposal.author),
        description: newProposal.description,
        content: newProposal.content,
        projectId: parseInt(selectedProject)
      };

      formData.append('schemeDesign', new Blob([JSON.stringify(schemeDesign)], {
        type: 'application/json'
      }));

      await designProposalApi.createProposal(formData);

      setShowNewModal(false);
      resetNewProposalForm();
      await fetchProposalList();
      toast.success('创建方案成功');

    } catch (error) {
      console.error('创建方案失败:', error);
      toast.error('创建方案失败: ' + error.message);
    }
  };

  // 文件上传处理
  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const newAttachments = files.map(file => ({
      id: file.name,
      name: file.name,
      size: (file.size / 1024).toFixed(2) + ' KB',
      originalFile: file
    }));
    setNewProposal(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments]
    }));
  };

  // 删除附件
  const handleFileDelete = (fileId) => {
    setNewProposal(prev => ({
      ...prev,
      attachments: prev.attachments.filter(file => file.id !== fileId)
    }));
  };

  // 项目选择处理
  const handleProjectSelect = async (project) => {
    setSelectedProject(project.id.toString());
    setCurrentPage(1);
    await loadProposals(project.id);
  };

  // 编辑方案
  const handleEdit = async (proposal) => {
    try {
      const data = await designProposalApi.getProposalDetail(proposal.id);
      setEditingProposal({
        ...data,
        type: data.type.toString(),
        attachments: data.projectFiles?.map(file => ({
          id: file.id,
          name: file.name,
          size: `${(file.size / 1024).toFixed(2)} KB`,
          path: file.path,
          originalFile: null
        })) || []
      });
      setShowEditModal(true);
    } catch (error) {
      console.error('获取方案详情失败:', error);
      toast.error('获取方案详情失败');
    }
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    try {
      const formData = new FormData();

      // 添加新上传的文件
      editingProposal.attachments
        .filter(file => file.originalFile)
        .forEach(file => formData.append('files', file.originalFile));

      // 构建更新数据
      const schemeDesign = {
        id: editingProposal.id,
        name: editingProposal.name,
        type: parseInt(editingProposal.type),
        status: parseInt(editingProposal.status),
        author: parseInt(editingProposal.author),
        description: editingProposal.description,
        content: editingProposal.content,
        projectId: parseInt(selectedProject),
        createTime: editingProposal.createTime,
        fileIds: editingProposal.attachments
          .filter(file => !file.originalFile)
          .map(file => file.id)
      };

      formData.append('schemeDesign', new Blob([JSON.stringify(schemeDesign)], {
        type: 'application/json'
      }));

      await designProposalApi.updateProposal(editingProposal.id, formData);

      setShowEditModal(false);
      setEditingProposal(null);
      await fetchProposalList();
      toast.success('更新方案成功');

    } catch (error) {
      console.error('更新方案失败:', error);
      toast.error('更新方案失败: ' + error.message);
    }
  };

  const handleEditFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const updatedAttachments = [...editingProposal.attachments, ...files.map(file => ({
      id: file.name,
      name: file.name,
      size: (file.size / 1024).toFixed(2) + ' KB',
      originalFile: file
    }))];
    setEditingProposal({ ...editingProposal, attachments: updatedAttachments });
  };

  const handleEditFileDelete = (fileId) => {
    const updatedAttachments = editingProposal.attachments.filter(file => file.id !== fileId);
    setEditingProposal({ ...editingProposal, attachments: updatedAttachments });
  };

  const handleDelete = (proposal) => {
    setDeletingProposal(proposal);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    try {
      await designProposalApi.deleteProposal(deletingProposal.id);

      setShowDeleteModal(false);
      setDeletingProposal(null);
      setCurrentPage(1);

      // 重新获取方案列表
      await fetchProposalList();
      toast.success('删除方案成功');

    } catch (error) {
      console.error('删除方案失败:', error);
      toast.error('删除方案失败: ' + error.message);
    }
  };

  const handlePreviewFile = async (fileName, bucketName = 'schemedesign') => {
    try {
      const previewUrl = await fileApi.previewFile(fileName, bucketName);
      const newWindow = window.open(previewUrl, '_blank');
      if (newWindow === null) {
        throw new Error('无法打开新窗口，请检查浏览器是否阻止弹出窗口');
      }
    } catch (error) {
      console.error('预览文件失败:', error);
      toast.error(error.message || '预览文件失败，请重试');
    }
  };

  const handleDownloadFile = async (file) => {
    try {
      const response = await fileApi.downloadFile(file.name, 'schemedesign');
      if (!response.ok) {
        throw new Error('下载文件失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      toast.success('文件下载成功');
    } catch (error) {
      console.error('下载文件失败:', error);
      toast.error('下载文件失败，请重试');
    }
  };

  const handleCloseEditModal = async () => {
    setShowEditModal(false);
    setEditingProposal(null);
    setIsEditTypeDropdownOpen(false);
    setIsEditEmployeeDropdownOpen(false);
    setCurrentPage(1);

    try {
      const data = await designProposalApi.getProposalList(selectedProject, 0, 10);
      setProposals(data.content || []);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
    } catch (error) {
      console.error('获取方案列表失败:', error);
      toast.error('获取方案列表失败: ' + error.message);
    }
  };

  const handleSearch = async () => {
    setCurrentPage(1);
    await fetchProposalList();
  };

  const handleReset = () => {
    setSearchQuery('');
    setSelectedType('');
    setSelectedStatus('');
    setCurrentPage(1);
    fetchProposalList();
  };

  const handlePageChange = async (newPage) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
    try {
      const params = {};
      
      if (searchQuery) {
        params.name = searchQuery;
      }
      
      if (selectedType) {
        params.type = parseInt(selectedType);
      }
      
      if (selectedStatus) {
        params.status = parseInt(selectedStatus);
      }

      const data = await designProposalApi.getProposalList(
        selectedProject, 
        newPage - 1,
        10,
        params
      );
      
      setProposals(data.content || []);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
    } catch (error) {
      console.error('获取方案列表失败:', error);
      toast.error('获取方案列表失败: ' + error.message);
    }
  };

  // 提交评审
  const handleSubmitReview = (proposal) => {
    setSubmittingProposal(proposal);
    setShowSubmitModal(true);
    setSelectedReviewers([]);
  };

  // 移除评审人员
  const handleRemoveReviewer = (reviewerId) => {
    setSelectedReviewers(prev => prev.filter(reviewer => reviewer.id !== reviewerId));
  };

  // 确认提交评审
  const handleConfirmSubmit = async () => {
    if (!selectedReviewLeader) {
      toast.error('请选择评审组长');
      return;
    }
    if (selectedReviewers.length === 0) {
      toast.error('请选择评审人员');
      return;
    }
    if (!reviewDeadline) {
      toast.error('请选择评审时间');
      return;
    }

    try {
      const reviewerIds = selectedReviewers.map(reviewer => reviewer.id);

      await designProposalApi.submitReview(
        submittingProposal.id,
        selectedProject,
        new Date(reviewDeadline).toISOString().split('.')[0],
        selectedReviewLeader,
        reviewerIds
      );

      setShowSubmitModal(false);
      setSubmittingProposal(null);
      setSelectedReviewers([]);
      setSelectedReviewLeader('');
      setReviewDeadline('');
      setCurrentPage(1);

      await fetchProposalList();
      toast.success('提交评审成功');

    } catch (error) {
      console.error('提交评审失败:', error);
      toast.error('提交评审失败: ' + error.message);
    }
  };

  // 关闭提交评审弹窗
  const handleCloseSubmitModal = () => {
    setShowSubmitModal(false);
    setSubmittingProposal(null);
    setSelectedReviewers([]);
    setSelectedReviewLeader('');
    setReviewDeadline('');
  };

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isNewTypeDropdownOpen && !event.target.closest('.new-type-dropdown')) {
        setIsNewTypeDropdownOpen(false);
      }
      if (isEditTypeDropdownOpen && !event.target.closest('.edit-type-dropdown')) {
        setIsEditTypeDropdownOpen(false);
      }
      if (isNewEmployeeDropdownOpen && !event.target.closest('.new-employee-dropdown')) {
        setIsNewEmployeeDropdownOpen(false);
      }
      if (isEditEmployeeDropdownOpen && !event.target.closest('.edit-employee-dropdown')) {
        setIsEditEmployeeDropdownOpen(false);
      }
      if (isReviewLeaderDropdownOpen && !event.target.closest('.review-leader-dropdown')) {
        setIsReviewLeaderDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isNewTypeDropdownOpen, isEditTypeDropdownOpen, isNewEmployeeDropdownOpen, isEditEmployeeDropdownOpen, isReviewLeaderDropdownOpen]);

  const handleTemplateUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setTemplateFile(file);
    }
  };

  const handleTemplateDelete = () => {
    setTemplateFile(null);
  };

  // 提交模板上传
  const handleTemplateSubmit = async () => {
    if (!templateFile) {
      toast.error('请选择要上传的模板文件');
      return;
    }

    try {
      await fileApi.uploadFile(templateFile, 'model');
      setShowTemplateModal(false);
      setTemplateFile(null);
      toast.success('上传模板成功');
    } catch (error) {
      console.error('上传模板失败:', error);
      toast.error('上传模板失败: ' + error.message);
    }
  };



  const handleShowTemplateList = async () => {
    try {
      const data = await fileApi.getAllTemplateFiles();
      setTemplateList(data);
      setShowTemplateListModal(true);
    } catch (error) {
      console.error('获取模板列表失败:', error);
      toast.error('获取模板列表失败: ' + error.message);
    }
  };

  const handlePreviewTemplate = async (fileName) => {
    await handlePreviewFile(fileName, 'model');
  };

  const handleDownloadTemplateFile = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName, 'model');
      if (!response.ok) {
        throw new Error('下载文件失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('文件下载成功');
    } catch (error) {
      console.error('下载文件失败:', error);
      toast.error('下载文件失败: ' + error.message);
    }
  };

  // 项目搜索处理
  const handleProjectSearch = (e) => {
    setProjectSearchQuery(e.target.value);
  };

  // 类型选择处理
  const handleTypeSelect = (type) => {
    setNewProposal(prev => ({ ...prev, type: type.id.toString() }));
    setIsNewTypeDropdownOpen(false);
  };

  // 员工选择处理
  const handleEmployeeSelect = (employee) => {
    if (showNewModal) {
      setNewProposal(prev => ({
        ...prev,
        author: employee.id.toString()
      }));
      setIsNewEmployeeDropdownOpen(false);
    } else if (showEditModal) {
      setEditingProposal(prev => ({
        ...prev,
        author: employee.id.toString()
      }));
      setIsEditEmployeeDropdownOpen(false);
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject === project.id.toString() ? 'bg-blue-50 border-blue-200 border' : ''}`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-2 text-sm">
                <span className={`px-2 py-1 rounded-full text-xs ${project.status === 0 ? 'bg-gray-100 text-gray-600' :
                    project.status === 1 ? 'bg-blue-100 text-blue-600' :
                      'bg-green-100 text-green-600'
                  }`}>
                  {project.status === 0 ? '未开始' :
                    project.status === 1 ? '进行中' :
                      '已结束'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容区域 */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">方案设计</p>
            </div>
          </div>

          {/* 搜索和筛选区域 */}
          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 justify-between">
              <div className="flex flex-wrap gap-4">
                <div className="relative w-[200px]">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索方案..."
                    className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                </div>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">状态（全部）</option>
                  <option value="draft">未提交</option>
                  <option value="reviewing">已提交</option>
                  <option value="approved">已通过</option>
                  <option value="rejected">已拒绝</option>
                </select>
                <div className="flex gap-2">
                  <Button
                    onClick={handleSearch}
                    className="w-[100px] whitespace-nowrap flex items-center justify-center"
                    style={{ backgroundColor: '#007bff', color: 'white' }}
                  >
                    <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                    搜索
                  </Button>
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  >
                    重置
                  </Button>
                  <Button
                    onClick={() => setShowTemplateModal(true)}
                    variant="outline"
                    className="flex items-center gap-1"
                    style={{ backgroundColor: '#007bff', color: 'white' }}
                  >
                    <FileTextIcon className="w-4 h-4" />
                    上传模版
                  </Button>
                  <Button
                    onClick={handleShowTemplateList}
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    模版列表
                  </Button>
                  <Button
                    className="flex items-center gap-1"
                    onClick={() => setShowNewModal(true)}
                    style={{ backgroundColor: '#007bff', color: 'white' }}
                  >
                    <PlusIcon className="w-4 h-4" />
                    添加方案
                  </Button>
                </div>
              </div>

            </div>
          </div>

          {/* 方案列表 */}
          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <div className="divide-y">
              <div className="p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
                    <div className="text-sm font-medium text-gray-500">方案名称</div>
                    <div className="text-sm font-medium text-gray-500">设计人</div>
                    <div className="text-sm font-medium text-gray-500">创建时间</div>
                    <div className="text-sm font-medium text-gray-500">状态</div>
                    <div className="text-sm font-medium text-gray-500">描述</div>
                  </div>
                  <div className="w-[200px] text-sm font-medium text-gray-500">操作</div>
                </div>
              </div>

              {proposals.map(proposal => (
                <div key={proposal.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
                      <div className="flex items-center gap-2">
                        <FileTextIcon className="w-5 h-5 text-blue-500" />
                        <div className="text-sm">
                          <div className="font-medium text-gray-900">{proposal.name}</div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {employees.find(e => e.id === proposal.author)?.name || proposal.author}
                      </div>
                      <div className="text-sm text-gray-600">
                        {new Date(proposal.createTime).toLocaleString()}
                      </div>
                      <div>
                        <div className={`px-3 py-1 rounded-full text-sm inline-block ${proposal.status === 0 ? 'bg-gray-100 text-gray-800' :
                            proposal.status === 1 ? 'bg-yellow-100 text-yellow-800' :
                              proposal.status === 2 ? 'bg-green-100 text-green-800' :
                                'bg-red-100 text-red-800'
                          }`}>
                          {proposal.status === 0 ? '未提交' :
                            proposal.status === 1 ? '已提交' :
                              proposal.status === 2 ? '已通过' :
                               proposal.status === 3 ? '已拒绝' : '未知状态'}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600 truncate max-w-[200px]">
                        {proposal.description || '-'}
                      </div>
                    </div>
                    <div className="flex gap-2 w-[200px] justify-end">
                      <Button
                        variant="outline"
                        onClick={() => handleViewDetail(proposal)}
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleEdit(proposal)}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="text-red-600"
                        onClick={() => handleDelete(proposal)}
                      >
                        <TrashIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleSubmitReview(proposal)}
                        style={{ backgroundColor: '#007bff', color: 'white' }}
                      >
                        提交审核
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 分页 */}
          <div className="flex items-center justify-end mt-4">
            <div className="flex items-center gap-2">
              <span>共 {totalElements} 条记录</span>
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                上一页
              </Button>

              {Array.from({ length: totalPages }, (_, index) => (
                <Button
                  key={index}
                  onClick={() => handlePageChange(index + 1)}
                  className={`px-3 py-1 border rounded ${currentPage === index + 1 ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                >
                  {index + 1}
                </Button>
              ))}

              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看方案</p>
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {showDetailModal && selectedProposal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看设计方案</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">项目名称</div>
                    <div className="font-medium">
                      {projects.find(p => p.id === selectedProposal.projectId)?.name || '-'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">设计名称</div>
                    <div className="font-medium">{selectedProposal.name}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">方案类型</div>
                    <div className="font-medium">
                      {proposalTypes.find(t => t.id === selectedProposal.type.toString())?.name}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">创建人</div>
                    <div className="font-medium">
                      {employees.find(e => e.id === selectedProposal.author)?.name || selectedProposal.author}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">状态</div>
                    <div className={`px-3 py-1 rounded-full text-sm inline-block ${proposalStatuses.find(s => s.id === selectedProposal.status.toString())?.color
                      }`}>
                      {proposalStatuses.find(s => s.id === selectedProposal.status.toString())?.name}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">创建时间</div>
                    <div className="font-medium">
                      {new Date(selectedProposal.createTime).toLocaleString()}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">更新时间</div>
                    <div className="font-medium">
                      {selectedProposal.updateTime ? new Date(selectedProposal.updateTime).toLocaleString() : '-'}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">方案描述</div>
                    <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-line">
                      {selectedProposal.description || '-'}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500 mb-1">方案内容</div>
                    <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-line">
                      {selectedProposal.content || '-'}
                    </div>
                  </div>
                </div>
                {selectedProposal.attachments && selectedProposal.attachments.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-500 mb-2">附件</div>
                    <div className="space-y-2">
                      {selectedProposal.attachments.map(file => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4 text-gray-400" />
                            <span>{file.name}</span>
                            <span className="text-sm text-gray-500">({file.size})</span>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePreviewFile(file.name)}
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownloadFile(file)}
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* New Proposal Modal */}
      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建设计方案</h3>
              <button
                onClick={handleCloseNewModal}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={projects.find(p => p.id.toString() === selectedProject)?.name || ''}
                      readOnly
                      className="w-full px-3 py-2 border rounded-lg bg-gray-100"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      name="name"
                      type="text"
                      value={newProposal.name}
                      onChange={(e) => setNewProposal({ ...newProposal, name: e.target.value })}
                      className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入方案名称"
                    />
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        onClick={() => setIsNewTypeDropdownOpen(!isNewTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between new-type-dropdown ${
                          !newProposal.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                        name="type"
                      >
                        <span className={newProposal.type ? 'text-gray-900' : 'text-gray-400'}>
                          {newProposal.type !== '' ? proposalTypes.find(t => t.id === newProposal.type)?.name : '请选择方案类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isNewTypeDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isNewTypeDropdownOpen && (
                        <div 
                          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg new-type-dropdown"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="py-1 max-h-60 overflow-auto">
                            {proposalTypes.map(type => (
                              <div
                                key={type.id}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleTypeSelect(type);
                                  setIsNewTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newProposal.type === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {type.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between new-employee-dropdown"
                        onClick={() => setIsNewEmployeeDropdownOpen(!isNewEmployeeDropdownOpen)}
                        name="author"
                      >
                        <span className={newProposal.author ? 'text-gray-900' : 'text-gray-400'}>
                          {newProposal.author ? employees.find(emp => emp.id.toString() === newProposal.author)?.name : '请选择创建人'}
                        </span>
                        <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${isNewEmployeeDropdownOpen ? 'transform rotate-180' : ''}`} />
                      </div>
                      {isNewEmployeeDropdownOpen && (
                        <div 
                          className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg new-employee-dropdown"
                          onClick={(e) => {
                            console.log('点击创建人下拉菜单容器');
                            e.stopPropagation();
                          }}
                        >
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={(e) => {
                                  console.log('选择创建人:', employee);
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleEmployeeSelect(employee);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newProposal.author === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    方案描述
                  </label>
                  <textarea
                    value={newProposal.description}
                    onChange={(e) => setNewProposal({ ...newProposal, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入方案描述..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    方案内容
                  </label>
                  <textarea
                    value={newProposal.content}
                    onChange={(e) => setNewProposal({ ...newProposal, content: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入方案内容..."
                  />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    附件
                  </label>
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer"
                    onClick={() => document.getElementById('fileInput').click()}
                  >
                    <input
                      type="file"
                      id="fileInput"
                      multiple
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                    <div className="flex flex-col items-center">
                      <FileTextIcon className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600">
                        点击或拖拽文件到此处上传
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        支持 Word、PDF、图片等格式
                      </p>
                    </div>
                  </div>
                  {newProposal.attachments.length > 0 && (
                    <div className="mt-4">
                      <div className="text-sm text-gray-500 mb-2">已上传文件</div>
                      <div className="space-y-2">
                        {newProposal.attachments.map(file => (
                          <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-4 h-4 text-gray-400" />
                              <span>{file.name}</span>
                              <span className="text-sm text-gray-500">({file.size})</span>
                            </div>
                            <button
                              onClick={() => handleFileDelete(file.id)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Cross2Icon className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCloseNewModal}
              >
                取消
              </Button>
              <Button onClick={handleCreateProposal}>
                创建方案
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Proposal Modal */}
      {showEditModal && editingProposal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑设计方案</h3>
              <button
                onClick={handleCloseEditModal}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={projects.find(p => p.id === editingProposal.projectId)?.name || ''}
                      readOnly
                      className="w-full px-3 py-2 border rounded-lg bg-gray-100"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingProposal.name}
                      onChange={(e) => setEditingProposal({ ...editingProposal, name: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入方案名称"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        onClick={() => setIsEditTypeDropdownOpen(!isEditTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between edit-type-dropdown ${
                          !editingProposal.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingProposal.type ? 'text-gray-900' : 'text-gray-400'}>
                          {editingProposal.type !== '' ? proposalTypes.find(t => t.id === editingProposal.type)?.name : '请选择方案类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditTypeDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isEditTypeDropdownOpen && (
                        <div 
                          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg edit-type-dropdown"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="py-1 max-h-60 overflow-auto">
                            {proposalTypes.map(type => (
                              <div
                                key={type.id}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingProposal({ ...editingProposal, type: type.id });
                                  setIsEditTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingProposal.type === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {type.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between edit-employee-dropdown"
                        onClick={() => setIsEditEmployeeDropdownOpen(!isEditEmployeeDropdownOpen)}
                      >
                        <span className={editingProposal.author ? 'text-gray-900' : 'text-gray-400'}>
                          {editingProposal.author ? employees.find(e => e.id === Number(editingProposal.author))?.name : '请选择创建人'}
                        </span>
                        <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${isEditEmployeeDropdownOpen ? 'transform rotate-180' : ''}`} />
                      </div>
                      {isEditEmployeeDropdownOpen && (
                        <div 
                          className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg edit-employee-dropdown"
                          onClick={(e) => {
                            console.log('点击创建人下拉菜单容器');
                            e.stopPropagation();
                          }}
                        >
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={(e) => {
                                  console.log('选择创建人:', employee);
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleEmployeeSelect(employee);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingProposal.author === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案描述
                    </label>
                    <textarea
                      value={editingProposal.description}
                      onChange={(e) => setEditingProposal({ ...editingProposal, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入方案描述..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案内容
                    </label>
                    <textarea
                      value={editingProposal.content}
                      onChange={(e) => setEditingProposal({ ...editingProposal, content: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入方案内容..."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    附件
                  </label>
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer"
                    onClick={() => document.getElementById('editFileInput').click()}
                  >
                    <input
                      type="file"
                      id="editFileInput"
                      multiple
                      onChange={handleEditFileUpload}
                      className="hidden"
                    />
                    <div className="flex flex-col items-center">
                      <FileTextIcon className="w-8 h-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600">
                        点击或拖拽文件到此处上传
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        支持 Word、PDF、图片等格式
                      </p>
                    </div>
                  </div>
                  {editingProposal.attachments.length > 0 && (
                    <div className="mt-4">
                      <div className="text-sm text-gray-500 mb-2">已上传文件</div>
                      <div className="space-y-2">
                        {editingProposal.attachments.map(file => (
                          <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-4 h-4 text-gray-400" />
                              <span>{file.name}</span>
                              <span className="text-sm text-gray-500">({file.size})</span>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreviewFile(file.name)}
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </Button>
                              <button
                                onClick={() => handleEditFileDelete(file.id)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Cross2Icon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCloseEditModal}
              >
                取消
              </Button>
              <Button onClick={handleSaveEdit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && deletingProposal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-2">确认删除</h3>
              <p className="text-gray-600">
                确定要删除方案 &ldquo;{deletingProposal.name}&rdquo; 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingProposal(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
              >
                确认删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Submit Review Modal */}
      {showSubmitModal && submittingProposal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">提交评审</h3>
              <button
                onClick={handleCloseSubmitModal}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    方案名称
                  </label>
                  <input
                    type="text"
                    value={submittingProposal.name}
                    readOnly
                    className="w-full px-3 py-2 border rounded-lg bg-gray-100"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评审组长 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div
                      className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between"
                      onClick={() => setShowReviewLeaderSelect(!showReviewLeaderSelect)}
                    >
                      <span className={selectedReviewLeader ? 'text-gray-900' : 'text-gray-400'}>
                        {selectedReviewLeader ? employees.find(emp => emp.id.toString() === selectedReviewLeader)?.name : '请选择评审组长'}
                      </span>
                      <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${showReviewLeaderSelect ? 'transform rotate-180' : ''}`} />
                    </div>
                    {showReviewLeaderSelect && (
                      <div 
                        className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg"
                        onClick={(e) => {
                          console.log('点击评审组长下拉菜单容器');
                          e.stopPropagation();
                        }}
                      >
                        <div className="py-1 max-h-60 overflow-auto">
                          {employees.map(employee => (
                            <div
                              key={employee.id}
                              onClick={(e) => {
                                console.log('点击评审组长选项，employee:', employee);
                                e.preventDefault();
                                e.stopPropagation();
                                setSelectedReviewLeader(employee.id.toString());
                                setShowReviewLeaderSelect(false);
                              }}
                              className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                selectedReviewLeader === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {employee.name}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评审人 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div
                      className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                      onClick={() => setShowReviewerSelect(!showReviewerSelect)}
                    >
                      {selectedReviewers.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                          {selectedReviewers.map(reviewer => (
                            <div key={reviewer.id} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1">
                              <span className="text-sm">{reviewer.name}</span>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveReviewer(reviewer.id);
                                }}
                                className="text-gray-400 hover:text-red-500"
                              >
                                <Cross2Icon className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-gray-400">请选择评审人员</span>
                      )}
                    </div>
                    {showReviewerSelect && (
                      <div
                        className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto reviewer-select"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex flex-wrap p-2 gap-2">
                          {employees.map((employee) => (
                            <div
                              key={employee.id}
                              className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                              onClick={() => {
                                const reviewer = {
                                  id: employee.id,
                                  name: employee.name
                                };
                                if (!selectedReviewers.find(r => r.id === employee.id)) {
                                  setSelectedReviewers([...selectedReviewers, reviewer]);
                                }
                              }}
                            >
                              <input
                                type="checkbox"
                                checked={selectedReviewers.some(r => r.id === employee.id)}
                                onChange={() => { }}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <div className={`flex-1 px-2 py-1 rounded ${selectedReviewers.some(r => r.id === employee.id) ? 'bg-blue-50' : ''
                                }`}>
                                <span className="text-sm">{employee.name}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评审时间 <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    {!reviewDeadline && (
                      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                        请选择评审时间
                      </div>
                    )}
                    <input
                      type="datetime-local"
                      value={reviewDeadline}
                      onChange={(e) => setReviewDeadline(e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${!reviewDeadline ? 'text-transparent' : ''
                        }`}
                      min={new Date().toISOString().slice(0, 16)}
                      step="1"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCloseSubmitModal}
              >
                取消
              </Button>
              <Button onClick={handleConfirmSubmit}>
                确认提交
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Template Upload Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">上传模版</h3>
              <button
                onClick={() => {
                  setShowTemplateModal(false);
                  setTemplateFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer"
                  onClick={() => document.getElementById('templateFileInput').click()}
                >
                  <input
                    type="file"
                    id="templateFileInput"
                    onChange={handleTemplateUpload}
                    className="hidden"
                  />
                  <div className="flex flex-col items-center">
                    <FileTextIcon className="w-8 h-8 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">
                      点击或拖拽文件到此处上传
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      支持 Word、PDF、图片等格式
                    </p>
                  </div>
                </div>

                {templateFile && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <FileTextIcon className="w-4 h-4 text-gray-400" />
                      <span>{templateFile.name}</span>
                      <span className="text-sm text-gray-500">
                        ({Math.round(templateFile.size / 1024)} KB)
                      </span>
                    </div>
                    <button
                      onClick={handleTemplateDelete}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Cross2Icon className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowTemplateModal(false);
                  setTemplateFile(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleTemplateSubmit}>
                确认上传
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Template List Modal */}
      {showTemplateListModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">模板列表</h3>
              <button
                onClick={() => setShowTemplateListModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {templateList.map((template, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      <FileTextIcon className="w-4 h-4 text-gray-400" />
                      <span>{template}</span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePreviewTemplate(template)}
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownloadTemplateFile(template)}
                      >
                        <DownloadIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                {templateList.length === 0 && (
                  <div className="text-center text-gray-500 py-4">
                    暂无模板文件
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowTemplateListModal(false)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});