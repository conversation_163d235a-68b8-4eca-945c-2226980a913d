import axios from 'axios';

import { fetchData } from './fetch';

const STAFF_URL = 'http://172.27.1.153:8081';

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjects: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),
    
  // 搜索项目
  searchProjects: (keyword) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/search?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),
};

// 测试计划相关接口
export const testPlanApi = {
  // 获取测试计划列表
  getTestPlans: (projectId, params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/${projectId}?${queryParams}`).then(res => res.json());
  },

  // 创建测试计划
  createTestPlan: (planData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/create`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(planData)
    }).then(res => res.json()),

  // 更新测试计划
  updateTestPlan: (planId, planData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/update/${planId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(planData)
    }).then(res => res.json()),

  // 删除测试计划
  deleteTestPlan: (planId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/delete/${planId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 获取测试计划详情
  getTestPlanDetail: (planId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-plans/detail/${planId}`).then(res => res.json()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`)
      .then(res => res.json())
      .then(data => data.map(emp => ({
        id: emp.id,
        name: emp.name
      })))
};

// 验收相关接口
export const acceptanceApi = {
  // 获取验收列表
  getAcceptanceList: (projectId, params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/acceptance/${projectId}?${queryParams}`).then(res => res.json());
  },

  // 创建验收记录
  createAcceptance: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/acceptances/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新验收记录
  updateAcceptance: (acceptanceId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/acceptances/update/${acceptanceId}`, {
      method: 'PUT',
      body: formData
    }).then(res => res.json()),

  // 删除验收记录
  deleteAcceptance: (acceptanceId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/acceptance/delete/${acceptanceId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 获取验收详情
  getAcceptanceDetail: (acceptanceId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/acceptance/detail/${acceptanceId}`).then(res => res.json()),
};

// 交付相关接口
export const deliveryApi = {
  // 获取交付列表
  getDeliveryList: (projectId, page = 0, size = 10, parentId = null) => {
    const url = new URL(`${fetchData["PROJECT_URL"]}/api/deliverable/page`);
    url.searchParams.append('projectId', projectId);
    url.searchParams.append('page', page);
    url.searchParams.append('size', size);
    if (parentId !== null) {
      url.searchParams.append('parentId', parentId);
    }
    return fetch(url)
      .then(res => res.json())
      .then(data => data.content || []);
  },

  // 创建交付记录
  createDelivery: (deliveryData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/deliverable/create`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(deliverable)
    }).then(res => res.json()),

  // 更新交付记录
  updateDelivery: (deliveryId, deliveryData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/deliverable/update/${deliveryId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(deliverable)
    }).then(res => res.json()),

  // 删除交付记录
  deleteDelivery: (deliveryId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/deliverable/delete/${deliveryId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 获取交付详情
  getDeliveryDetail: (deliveryId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/deliverable/one/${deliveryId}`).then(res => res.json()),

};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: async (fileId) => {
    try {
      const response = await fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?fileName=${encodeURIComponent(fileId)}&bucketName=deliverable`);
      
      if (!response.ok) {
        throw new Error('预览文件失败');
      }

      // 获取响应类型
      const contentType = response.headers.get('content-type');
      
      let data;
      // 如果是JSON格式
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        // 如果是普通文本（URL字符串）
        data = await response.text();
      }
      
      // 处理预览URL
      let previewUrl = '';
      if (typeof data === 'string') {
        previewUrl = data.trim();
      } else if (data && typeof data === 'object') {
        previewUrl = data.url || data.previewUrl || data.path || data;
      }

      // 验证并处理URL
      if (previewUrl) {
        // 如果是相对路径，添加基础URL
        if (previewUrl.startsWith('/')) {
          previewUrl = `${fetchData["PROJECT_URL"]}${previewUrl}`;
        }
        // 如果不是完整URL，添加协议和域名
        if (!previewUrl.startsWith('http')) {
          previewUrl = `${fetchData["PROJECT_URL"]}/${previewUrl}`;
        }
        return previewUrl;
      } else {
        throw new Error('无法获取预览URL');
      }
    } catch (error) {
      console.error('预览文件错误:', error);
      throw error;
    }
  },

  // 下载文件
  downloadFile: (fileId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/download?fileName=${encodeURIComponent(fileId)}&bucketName=deliverable`)
      .then(res => res.blob()),

  // 上传文件
  uploadFile: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/upload`, {
      method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data' },
      body: formData
    }).then(res => res.json()),
}; 