import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Cross2Icon, MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { knowledgeStore } from '../store/knowledgeStore';

export const KnowledgeBase = observer(() => {
  const [searchQuery, setSearchQuery] = useState('');
  const { isOpen, toggleModal, currentCategory, setCurrentCategory } = knowledgeStore;

  if (!isOpen) return null;

  const documents = currentCategory 
    ? currentCategory.documents 
    : knowledgeStore.getAllDocuments();

  const filteredDocuments = searchQuery
    ? documents.filter(doc => 
        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : documents;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white w-[1000px] h-[600px] rounded-lg shadow-xl flex">
        {/* Left Sidebar */}
        <div className="w-64 border-r flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">知识库</h2>
          </div>
          <div className="flex-1 overflow-y-auto">
            <div
              className={`px-4 py-3 cursor-pointer hover:bg-gray-50 ${
                !currentCategory ? 'bg-blue-50 text-blue-600' : ''
              }`}
              onClick={() => setCurrentCategory(null)}
            >
              全部文档
            </div>
            {knowledgeStore.categories.map((category) => (
              <div
                key={category.id}
                className={`px-4 py-3 cursor-pointer hover:bg-gray-50 ${
                  currentCategory?.id === category.id ? 'bg-blue-50 text-blue-600' : ''
                }`}
                onClick={() => setCurrentCategory(category)}
              >
                {category.name}
              </div>
            ))}
          </div>
        </div>

        {/* Right Content */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 border-b flex justify-between items-center">
            <div className="text-lg font-semibold">
              {currentCategory?.name || '全部文档'}
            </div>
            <button
              onClick={toggleModal}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <Cross2Icon className="w-4 h-4" />
            </button>
          </div>

          {/* Search Bar */}
          <div className="p-4 border-b">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索文档..."
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <MagnifyingGlassIcon className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
            </div>
          </div>

          {/* Document List */}
          <div className="flex-1 overflow-y-auto p-4">
            {filteredDocuments.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                没有找到相关文档
              </div>
            ) : (
              filteredDocuments.map((doc) => (
                <div
                  key={doc.id}
                  className="p-4 border rounded-lg mb-3 hover:bg-gray-50 cursor-pointer"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{doc.title}</h3>
                    {doc.isNew && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                        新
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{doc.description}</p>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>更新时间：{doc.updateTime}</span>
                    <span>浏览次数：{doc.views}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
});