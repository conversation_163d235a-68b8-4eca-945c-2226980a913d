import axios from 'axios';

import { fetchData } from './fetch';


// 测试用例相关接口
export const testCaseApi = {
  // 获取测试用例分页列表
  getTestCaseList: (projectId, page, size) => {
    const params = new URLSearchParams({ projectId, page, size });
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/page?${params}`).then(res => res.json());
  },

  // 搜索测试用例
  searchTestCases: (projectId, page, size, name, creatorId) => {
    const params = new URLSearchParams({ 
      projectId, 
      page, 
      size,
      name: name || '',
      creatorId: creatorId || ''
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/page?${params}`).then(res => res.json());
  },

  // 获取单个测试用例详情
  getTestCaseDetail: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/one/${id}`).then(res => res.json()),

  // 创建测试用例
  createTestCase: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新测试用例
  updateTestCase: (id, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/update/${id}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除测试用例
  deleteTestCase: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-cases/${id}`, {
      method: 'DELETE'
    }).then(res => res.json()),
};

// 文件相关接口
export const fileApi = {
  // 文件预览
  previewFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 文件下载
  downloadFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },

  // 文件上传
  uploadFile: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/upload`, {
      method: 'POST',
      body: formData
    }).then(res => res.text()),

  // 获取所有文件名
  getAllFileNames: (bucketName) => {
    const params = new URLSearchParams({ bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/getAllFileName?${params}`).then(res => res.json());
  },

  // 删除文件
  deleteFile: (fileId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/files/delete/${fileId}`, {
      method: 'DELETE'
    }).then(res => res.json()),
}; 