import React, { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { userStore } from '../store/userStore';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  DownloadIcon,
  Cross2Icon,
  CalendarIcon,
  FileIcon,
  TrashIcon,
  Pencil1Icon,
  EyeOpenIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { inputReviewApi, projectApi, designInputApi, fileApi, employeeApi } from '../services/inputReviewService';

// 评审状态配置
const reviewStatuses = [
  { id: 0, name: '未评审', color: 'gray' },
  { id: 1, name: '已通过', color: 'green' },
  { id: 2, name: '未通过', color: 'red' }
];

// 评审类型配置
const reviewTypes = [
  { id: 0, name: '设计评审' },
  { id: 1, name: '代码评审' },
  { id: 2, name: '文档评审' }
];

/**
 * 输入评审组件
 * 功能：管理项目的输入评审流程，包括创建、编辑、查看、删除评审记录
 * 主要特性：
 * - 项目选择和切换
 * - 评审记录的CRUD操作
 * - 文件选择和预览
 * - 评审人员管理
 * - 评审状态跟踪
 */
export const InputReview = observer(() => {
  // 搜索和筛选状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedReviewer, setSelectedReviewer] = useState('');
  const [searchProjectQuery, setSearchProjectQuery] = useState('');

  // 模态框显示状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewReviewModal, setShowNewReviewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showFileSelect, setShowFileSelect] = useState(false);
  const [showReviewerSelect, setShowReviewerSelect] = useState(false);

  // 核心数据状态
  const [selectedReview, setSelectedReview] = useState(null);
  const [editingReview, setEditingReview] = useState(null);
  const [deletingReviewId, setDeletingReviewId] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [designInputs, setDesignInputs] = useState([]);
  const [employees, setEmployees] = useState([]);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);
  const [totalElements, setTotalElements] = useState(0);

  // 新建评审表单状态
  const [newReview, setNewReview] = useState({
    reviewName: '',
    designInputFiles: [],
    description: '',
    reviewLeaderId: '',
    reviewerIds: [],
    reviewTime: '',
    reviewAdvice: '',
    reviewStandard: '',
    reviewType: null
  });

  // 错误处理状态
  const [errors, setErrors] = useState({});
  const [errorMessage, setErrorMessage] = useState('');
  const [showError, setShowError] = useState(false);

  // 过滤项目列表
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchProjectQuery.toLowerCase())
  );

  // 处理项目切换
  const handleProjectChange = async (e) => {
    const projectId = Number(e.target.value);
    setSelectedProject(projectId);

    try {
      // 清空设计输入文件和新评审表单
      setDesignInputs([]);
      setNewReview(prev => ({ ...prev, designInputFiles: [] }));

      // 获取项目的设计输入文件
      await fetchDesignInputs(projectId);

      // 获取项目的评审列表
      const userData = userStore.getUserData();
      const reviewerId = userData?.id;
      const reviewData = await inputReviewApi.getReviewList(projectId, 0, 10, reviewerId);
      setReviews(reviewData.content || []);
      setTotalElements(reviewData.totalElements || 0);
      setCurrentPage(0);
    } catch (error) {
      console.error('获取数据失败:', error);
      setReviews([]);
      setTotalElements(0);
      setCurrentPage(0);
    }
  };

  const handleViewDetail = async (review) => {
    try {
      const detailData = await inputReviewApi.getReviewDetail(review.id);
      setSelectedReview({
        ...detailData,
        projectName: getProjectName(detailData.projectId),
      });
      setShowDetailModal(true);
    } catch (error) {
      console.error('获取评审详情出错:', error);
      alert('获取详情失败，请重试');
    }
  };

  const getProjectName = (projectId) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : '';
  };

  const handleFileSelect = (fileName) => {
    if (newReview.designInputFiles.includes(fileName)) {
      // 如果文件已经被选中，则取消选择
      setNewReview(prev => ({
        ...prev,
        designInputFiles: prev.designInputFiles.filter(file => file !== fileName)
      }));
    } else {
      // 如果文件未被选中，则添加到选中列表
      setNewReview(prev => ({
        ...prev,
        designInputFiles: [...prev.designInputFiles, fileName]
      }));
    }
    // 清除错误提示
    if (errors.designInputFiles) {
      setErrors(prev => ({ ...prev, designInputFiles: '' }));
    }
  };

  const handleRemoveFile = (fileName) => {
    setNewReview(prev => ({
      ...prev,
      designInputFiles: prev.designInputFiles.filter(file => file !== fileName)
    }));
  };

  const handleCreateReview = async () => {
    console.log('提交表单时的newReview:', newReview);
    
    setErrors({
      reviewName: '',
      reviewerIds: '',
      designInputFiles: '',
      reviewLeaderId: '',
      reviewTime: '',
      reviewType: ''
    });

    let hasError = false;

    if (!selectedProject) {
      alert('请选择项目');
      return;
    }

    if (!newReview.reviewLeaderId) {
      setErrors(prev => ({
        ...prev,
        reviewLeaderId: '请选择评审组长'
      }));
      hasError = true;
    }

    if (!newReview.reviewTime) {
      setErrors(prev => ({
        ...prev,
        reviewTime: '请选择评审时间'
      }));
      hasError = true;
    }

    if (newReview.designInputFiles.length === 0) {
      setErrors(prev => ({
        ...prev,
        designInputFiles: '请至少选择一个评审文件'
      }));
      hasError = true;
    }

    if (!newReview.reviewName) {
      setErrors(prev => ({
        ...prev,
        reviewName: '请输入评审名称'
      }));
      hasError = true;
      
    }

    if (newReview.reviewType === '' || newReview.reviewType === undefined || newReview.reviewType === null) {
      setErrors(prev => ({
        ...prev,
        reviewType: '请选择评审类型'
      }));
      hasError = true;
    }

    // 添加评审人员必填项验证
    if (!newReview.reviewerIds || newReview.reviewerIds.length === 0) {
      setErrors(prev => ({
        ...prev,
        reviewerIds: '请选择至少一名评审人员'
      }));
      hasError = true;
    }

    if (hasError) {
      return;
    }

    try {
      const designInputIds = designInputs
        .filter(input => newReview.designInputFiles.includes(input.name))
        .map(input => input.id);

      const reviewLeader = employees.find(emp => emp.id === Number(newReview.reviewLeaderId));
      const reviewers = employees.filter(emp => newReview.reviewerIds.includes(emp.id.toString()));
      const reviewerNames = reviewers.map(reviewer => reviewer.name);

      const requestData = {
        reviewName: newReview.reviewName,
        reviewerId: 0,
        description: newReview.description || '',
        reviewStandard: newReview.reviewStandard || '',
        projectId: Number(selectedProject),
        reviewStatus: 0,
        inputId: designInputIds,
        reviewType: Number(newReview.reviewType),
        reviewersId: newReview.reviewerIds.map(Number),
        reviewLeader: Number(newReview.reviewLeaderId),
        reviewLeaderName: reviewLeader?.name || '',
        reviewersName: reviewerNames,
        reviewDate: newReview.reviewTime
      };

      await inputReviewApi.createReview(requestData);
      await fetchReviews(selectedProject);

      setShowNewReviewModal(false);
      setNewReview({
        projectName: '',
        reviewName: '',
        designInputFiles: [],
        description: '',
        reviewLeaderId: '',
        reviewerIds: [],
        reviewTime: '',
        reviewAdvice: '',
        reviewStandard: '',
        reviewType: null
      });
      setErrors({
        reviewName: '',
        reviewerIds: '',
        designInputFiles: '',
        reviewLeaderId: '',
        reviewTime: '',
        reviewType: ''
      });
    } catch (error) {
      console.error('创建评审出错:', error);
      showErrorMessage('创建失败，请重试');
    }
  };

  const getStatusColor = (status) => {
    return {
      0: 'bg-gray-100 text-gray-800',    // 未评审
      1: 'bg-green-100 text-green-800',  // 已通过
      2: 'bg-red-100 text-red-800'       // 未通过
    }[status] || 'bg-gray-100 text-gray-800';
  };

  const handleEditReview = async (review) => {
    try {
      const detailData = await inputReviewApi.getReviewDetail(review.id);

      // const filePromises = (detailData.inputId || []).map(async (fileId) => {
      //   const fileData = await designInputApi.getDesignInputDetail(fileId);
      //   return fileData ? {
      //     id: fileId,
      //     name: fileData.name
      //   } : null;
      // });

      // const fileResults = await Promise.all(filePromises);
      // const validFiles = fileResults.filter(file => file !== null);

      setEditingReview({
        ...detailData,
        projectName: getProjectName(detailData.projectId),
        designInputFiles: detailData.projectFiles?.map(file => file.name) || [],
        originalInputIds: detailData.projectFiles?.map(file => file.id) || [],
        reviewType: detailData.reviewType,
        reviewStatus: detailData.reviewStatus,
        reviewerIds: detailData.reviewersId?.map(String) || [],
        reviewersName: detailData.reviewersName || [],
        reviewLeader: detailData.reviewLeader?.toString() || '',
      });

      setShowEditModal(true);
    } catch (error) {
      console.error('获取评审详情出错:', error);
      alert('获取详情失败，请重试');
    }
  };



  // 确认删除评审
  const confirmDelete = async () => {
    try {
      await inputReviewApi.deleteReview(deletingReviewId);
      await fetchReviews(selectedProject);
      setShowDeleteConfirm(false);
      setDeletingReviewId(null);
    } catch (error) {
      console.error('删除评审出错:', error);
      showErrorMessage('删除失败，请重试');
    }
  };

  const fetchReviews = async (projectId) => {
    if (!projectId) return;

    try {
      const userData = userStore.getUserData();
      const reviewerId = userData?.id;
      const data = await inputReviewApi.getReviewList(projectId, currentPage, pageSize, reviewerId);
      setReviews(data.content || []);
      setTotalElements(data.totalElements || 0);
    } catch (error) {
      console.error('获取评审列表失败:', error);
      showErrorMessage('获取评审列表失败');
    }
  };

  // 获取设计输入文件列表
  const fetchDesignInputs = async (projectId) => {
    try {
      const data = await designInputApi.getDesignInputList(projectId);
      setDesignInputs(data);
    } catch (error) {
      console.error('获取设计输入文件出错:', error);
    }
  };

  // 预览文件
  const handlePreviewFile = async (fileName) => {
    try {
      const previewUrl = await fileApi.previewFile(fileName, 'designinput');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('预览文件出错:', error);
      showErrorMessage('预览文件失败，请重试');
    }
  };

  // 下载文件
  const handleDownloadFile = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName, 'designinput');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载文件出错:', error);
      showErrorMessage('下载文件失败，请重试');
    }
  };

  // 处理分页变化
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // 初始化数据
  const fetchInitialData = useCallback(async () => {
    try {
      console.log('开始初始化数据');

      // 获取项目列表
      const projectData = await projectApi.getProjectList();
      setProjects(projectData);
      console.log('获取到项目列表:', projectData);

      // 如果有项目，选择第一个项目
      if (projectData.length > 0) {
        const firstProject = projectData[0];
        setSelectedProject(firstProject.id);
        console.log('选择第一个项目:', firstProject);

        // 获取第一个项目的设计输入文件
        await fetchDesignInputs(firstProject.id);

        // 获取第一个项目的评审列表
        const userData = userStore.getUserData();
        const reviewerId = userData?.id;
        const reviewData = await inputReviewApi.getReviewList(firstProject.id, 0, 10, reviewerId);
        setReviews(reviewData.content || []);
        setTotalElements(reviewData.totalElements || 0);
        setCurrentPage(0);
      }

      // 获取员工列表
      const employeeData = await employeeApi.getEmployeeList();
      console.log('获取到员工列表:', employeeData);

      if (employeeData && employeeData.length > 0) {
        setEmployees(employeeData);
      } else {
        console.warn('员工列表为空');
      }
    } catch (error) {
      console.error('初始化数据失败:', error);
    }
  }, []);

  // 组件初始化
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.reviewer-select')) {
        setShowReviewerSelect(false);
      }
      if (!event.target.closest('.file-select')) {
        setShowFileSelect(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 添加错误提示组件
  const ErrorMessage = ({ message }) => {
    if (!message) return null;

    return (
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-white rounded-lg shadow-lg px-4 py-2 flex items-center gap-2 text-sm text-red-600 border border-red-100">
          <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          {message}
        </div>
      </div>
    );
  };

  // 修改错误处理函数
  const showErrorMessage = (message) => {
    setErrorMessage(message);
    setShowError(true);
    setTimeout(() => {
      setShowError(false);
      setErrorMessage('');
    }, 3000); // 3秒后自动消失
  };

  const handleApproveReview = async () => {
    try {
      await inputReviewApi.confirmReview(editingReview.id, 1);
      await fetchReviews(selectedProject);
      setShowEditModal(false);
    } catch (error) {
      console.error('审批失败:', error);
      showErrorMessage('审批失败，请重试');
    }
  };

  const handleRejectReview = async () => {
    try {
      await inputReviewApi.confirmReview(editingReview.id, 2);
      await fetchReviews(selectedProject);
      setShowEditModal(false);
    } catch (error) {
      console.error('审批失败:', error);
      showErrorMessage('审批失败，请重试');
    }
  };



  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={searchProjectQuery}
              onChange={(e) => setSearchProjectQuery(e.target.value)}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          {filteredProjects.map(project => (
            <div
              key={project.id}
              onClick={() => handleProjectChange({ target: { value: project.id } })}
              className={`p-4 cursor-pointer ${selectedProject === project.id ? 'bg-[#EBF6FF]' : 'hover:bg-gray-50'}`}
            >
              <div className={`font-medium ${selectedProject === project.id ? 'text-[#1677FF]' : 'text-gray-900'}`}>
                {project.name}
              </div>
              <div className={`text-sm mt-1 px-2 py-0.5 inline-block rounded ${project.status === 0 ? 'text-gray-600 bg-gray-100' :
                project.status === 1 ? 'text-[#1677FF] bg-[#EBF6FF]' :
                  project.status === 2 ? 'text-green-600 bg-green-50' : ''
                }`}>
                {project.status === 0 ? '未开始' :
                  project.status === 1 ? '进行中' :
                    project.status === 2 ? '已结束' : '未知状态'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{getProjectName(selectedProject)}</h1>
              <p className="text-gray-500">输入评审</p>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索评审..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">评审状态（全部）</option>
                {reviewStatuses.map(status => (
                  <option key={status.id} value={status.id}>{status.name}</option>
                ))}
              </select>
              <select
                value={selectedReviewer}
                onChange={(e) => setSelectedReviewer(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">评审人（全部）</option>
                {employees.map(reviewer => (
                  <option key={reviewer.id} value={reviewer.id}>{reviewer.name}</option>
                ))}
              </select>
              <div className="flex gap-2">
                <Button
                  onClick={() => fetchReviews(selectedProject)}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  style={{ backgroundColor: '#007bff', color: 'white' }}
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedStatus('');
                    setSelectedReviewer('');
                    fetchReviews(selectedProject);
                  }}
                  variant="outline"
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={() => setShowNewReviewModal(true)}
                >
                  <PlusIcon className="w-4 h-4" />
                  新建评审
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <div className="divide-y">
              <div className="p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
                    <div className="text-sm font-medium text-gray-500">评审名称</div>
                    <div className="text-sm font-medium text-gray-500">评审状态</div>
                    <div className="text-sm font-medium text-gray-500">评审组长</div>
                    <div className="text-sm font-medium text-gray-500">评审人员</div>
                    <div className="text-sm font-medium text-gray-500">评审时间</div>

                  </div>
                  <div className="w-[200px] text-sm font-medium text-gray-500">操作</div>
                </div>
              </div>

              {reviews.map(review => (
                <div key={review.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
                      <div className="flex items-center gap-2">
                        <FileIcon className="w-5 h-5 text-blue-500" />
                        <div className="text-sm">
                          <div className="font-medium text-gray-900">{review.reviewName}</div>
                        </div>
                      </div>
                      <div>
                        <div className={`px-3 py-1 rounded-full text-sm inline-block ${review.reviewStatus === 0 ? 'bg-gray-100 text-gray-800' :
                          review.reviewStatus === 1 ? 'bg-green-100 text-green-800' :
                            review.reviewStatus === 2 ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                          }`}>
                          {reviewStatuses.find(s => s.id === review.reviewStatus)?.name}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {review.reviewLeaderName}
                      </div>
                      <div className="text-sm text-gray-600 truncate max-w-[200px]">
                        {review.reviewersName ? review.reviewersName.join('、') : '-'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {review.reviewDate ? new Date(review.reviewDate).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit',
                          second: '2-digit',
                          hour12: false
                        }) : '-'}
                      </div>

                    </div>
                    <div className="flex gap-2 w-[200px] justify-end">
                      <Button
                        variant="outline"
                        onClick={() => handleViewDetail(review)}
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleEditReview(review)}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="text-red-600"
                        onClick={() => {
                          setDeletingReviewId(review.id);
                          setShowDeleteConfirm(true);
                        }}
                      >
                        <TrashIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-end mt-4">
            <div className="flex items-center gap-2">
              <span>共 {totalElements} 条记录</span>
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                上一页
              </Button>
              {Array.from({ length: Math.ceil(totalElements / pageSize) }, (_, index) => (
                <Button
                  key={index}
                  onClick={() => handlePageChange(index)}
                  className={`px-3 py-1 border rounded ${currentPage === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                >
                  {index + 1}
                </Button>
              ))}
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === Math.ceil(totalElements / pageSize) - 1}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                下一页
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          请选择一个项目
        </div>
      )}

      {showNewReviewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建评审</h3>
              <button
                onClick={() => {
                  setShowNewReviewModal(false);
                  setNewReview({
                    projectName: '',
                    reviewName: '',
                    designInputFiles: [],
                    description: '',
                    reviewLeaderId: '',
                    reviewerIds: [],
                    reviewTime: '',
                    reviewAdvice: '',
                    reviewStandard: '',
                    reviewType: null
                  });
                  setErrors({
                    reviewName: '',
                    reviewerIds: '',
                    designInputFiles: '',
                    reviewLeaderId: '',
                    reviewTime: '',
                    reviewType: ''
                  });
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={getProjectName(Number(selectedProject))}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newReview.reviewName}
                      onChange={(e) => {
                        setNewReview({ ...newReview, reviewName: e.target.value });
                        if (e.target.value) {
                          setErrors(prev => ({ ...prev, reviewName: '' }));
                        }
                      }}
                      placeholder="请输入评审名称"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.reviewName ? 'border-red-500' : ''
                        }`}
                    />
                    {errors.reviewName && (
                      <div className="mt-1 text-sm text-red-500">
                        {errors.reviewName}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审组长 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      {/* 使用原生select元素替代自定义下拉组件 */}
                      <select
                        value={newReview.reviewLeaderId}
                        onChange={(e) => {
                          const selectedId = e.target.value;
                          console.log('选择评审组长:', selectedId);
                          setNewReview({
                            ...newReview,
                            reviewLeaderId: selectedId
                          });
                          if (selectedId) {
                            setErrors(prev => ({ ...prev, reviewLeaderId: '' }));
                          }
                        }}
                        name="reviewLeader"
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer ${errors.reviewLeaderId ? 'border-red-500' : 'border-gray-300'}`}
                      >
                        <option value="">请选择评审组长</option>
                        {employees.map(employee => (
                          <option key={employee.id} value={employee.id.toString()}>
                            {employee.name}
                          </option>
                        ))}
                      </select>
                      {errors.reviewLeaderId && (
                        <div className="mt-1 text-sm text-red-500">
                          {errors.reviewLeaderId}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审人员 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        className={`reviewer-select w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto ${errors.reviewerIds ? 'border-red-500' : ''
                          }`}
                        onClick={() => setShowReviewerSelect(!showReviewerSelect)}
                      >
                        {newReview.reviewerIds.length > 0 ? (
                          <div className="flex flex-wrap">
                            {newReview.reviewerIds.map((id) => {
                              const employee = employees.find(e => e.id === Number(id));
                              return (
                                <div key={id} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                                  <span className="text-sm">{employee?.name}</span>
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      setNewReview(prev => ({
                                        ...prev,
                                        reviewerIds: prev.reviewerIds.filter(rid => rid !== id)
                                      }));
                                    }}
                                    className="text-gray-400 hover:text-red-500 ml-1"
                                  >
                                    <Cross2Icon className="w-4 h-4" />
                                  </button>
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <span className="text-gray-400">请选择评审人员</span>
                        )}
                      </div>
                      {showReviewerSelect && (
                        <div
                          className="absolute z-[60] w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto reviewer-select"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="flex flex-wrap p-2 gap-2">
                            {employees.map((employee) => (
                              <div
                                key={employee.id}
                                className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                onClick={() => {
                                  const id = employee.id.toString();
                                  setNewReview(prev => ({
                                    ...prev,
                                    reviewerIds: (prev.reviewerIds || []).includes(id)
                                      ? (prev.reviewerIds || []).filter(rid => rid !== id)
                                      : [...(prev.reviewerIds || []), id]
                                  }));
                                  if (errors.reviewerIds) {
                                    setErrors(prev => ({ ...prev, reviewerIds: '' }));
                                  }
                                }}
                              >
                                <input
                                  type="checkbox"
                                  checked={newReview.reviewerIds.includes(employee.id.toString())}
                                  onChange={() => { }}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <div className={`flex-1 px-2 py-1 rounded ${newReview.reviewerIds.includes(employee.id.toString()) ? 'bg-blue-50' : ''
                                  }`}>
                                  <span className="text-sm">{employee.name}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.reviewerIds && (
                      <div className="mt-1 text-sm text-red-500">
                        {errors.reviewerIds}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="datetime-local"
                      step="1"
                      value={newReview.reviewTime}
                      onChange={(e) => {
                        setNewReview(prev => ({
                          ...prev,
                          reviewTime: e.target.value
                        }));
                        if (e.target.value) {
                          setErrors(prev => ({ ...prev, reviewTime: '' }));
                        }
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.reviewTime ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {errors.reviewTime && (
                      <div className="mt-1 text-sm text-red-500">
                        {errors.reviewTime}
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审文件 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        className={`file-select w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto ${errors.designInputFiles ? 'border-red-500' : ''}`}
                        onClick={() => setShowFileSelect(!showFileSelect)}
                      >
                        {newReview.designInputFiles.length > 0 ? (
                          <div className="flex flex-wrap">
                            {newReview.designInputFiles.map((fileName) => (
                              <div key={fileName} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                                <span className="text-sm">{fileName}</span>
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleRemoveFile(fileName);
                                  }}
                                  className="text-gray-400 hover:text-red-500 ml-1"
                                >
                                  <Cross2Icon className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-400">请选择评审文件</span>
                        )}
                      </div>
                      {showFileSelect && (
                        <div className="absolute z-[60] w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto file-select">
                          <div className="flex flex-wrap p-2 gap-2">
                            {designInputs.map((file) => (
                              <div
                                key={file.name}
                                className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                onClick={() => handleFileSelect(file.name)}
                              >
                                <input
                                  type="checkbox"
                                  checked={newReview.designInputFiles.includes(file.name)}
                                  onChange={() => { }}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <div className={`flex-1 px-2 py-1 rounded ${newReview.designInputFiles.includes(file.name) ? 'bg-blue-50' : ''}`}>
                                  <span className="text-sm">{file.name}</span>
                                </div>
                              </div>
                            ))}
                            {designInputs.length === 0 && (
                              <div className="text-center py-4 text-gray-500 w-full">
                                暂无可选文件
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.designInputFiles && (
                      <div className="mt-1 text-sm text-red-500">
                        {errors.designInputFiles}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      {/* 使用原生select元素替代自定义下拉组件 */}
                      <select
                        value={newReview.reviewType !== null && newReview.reviewType !== undefined ? newReview.reviewType : ''}
                        onChange={(e) => {
                          const selectedType = e.target.value ? Number(e.target.value) : null;
                          console.log('选择评审类型:', selectedType);
                          setNewReview({
                            ...newReview,
                            reviewType: selectedType
                          });
                          if (selectedType !== null && selectedType !== undefined) {
                            setErrors(prev => ({ ...prev, reviewType: '' }));
                          }
                        }}
                        name="reviewType"
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer ${errors.reviewType ? 'border-red-500' : 'border-gray-300'}`}
                      >
                        <option value="">请选择评审类型</option>
                        {reviewTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {errors.reviewType && (
                        <div className="mt-1 text-sm text-red-500">
                          {errors.reviewType}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审标准
                    </label>
                    <textarea
                      value={newReview.reviewStandard}
                      onChange={(e) => setNewReview({ ...newReview, reviewStandard: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入评审标准..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审描述
                    </label>
                    <textarea
                      value={newReview.description}
                      onChange={(e) => setNewReview({ ...newReview, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入评审描述..."
                    />
                  </div>
                </div>

              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewReviewModal(false);
                  setNewReview({
                    projectName: '',
                    reviewName: '',
                    designInputFiles: [],
                    description: '',
                    reviewLeaderId: '',
                    reviewerIds: [],
                    reviewTime: '',
                    reviewAdvice: '',
                    reviewStandard: '',
                    reviewType: null
                  });
                  setErrors({
                    reviewName: '',
                    reviewerIds: '',
                    designInputFiles: '',
                    reviewLeaderId: '',
                    reviewTime: '',
                    reviewType: ''
                  });
                }}
              >
                取消
              </Button>
              <Button onClick={handleCreateReview}>
                创建评审
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDetailModal && selectedReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">评审详情</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">项目名称</label>
                    <div className="text-sm">{selectedReview.projectName}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审名称</label>
                    <div className="text-sm">{selectedReview.reviewName}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审人</label>
                    <div className="text-sm">{selectedReview.reviewersName ? selectedReview.reviewersName.join(', ') : '暂无评审人'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审组长</label>
                    <div className="text-sm">{selectedReview.reviewLeaderName || '暂无评审组长'}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审时间</label>
                    <div className="text-sm">{selectedReview.reviewDate ? new Date(selectedReview.reviewDate).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    }) : '-'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审状态</label>
                    <div>
                      <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(selectedReview.reviewStatus)}`}>
                        {reviewStatuses.find(s => s.id === selectedReview.reviewStatus)?.name}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审类型</label>
                    <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(selectedReview.reviewType)}`}>
                      {selectedReview.reviewType === 0 ? '设计评审' :
                        selectedReview.reviewType === 1 ? '代码评审' :
                          selectedReview.reviewType === 2 ? '文档评审' : '未知类型'}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">审核标准</label>
                    <div className="text-sm bg-gray-50 p-3 rounded-lg min-h-[60px]">
                      {selectedReview.reviewStandard || '暂无审核标准'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审描述</label>
                    <div className="text-sm bg-gray-50 p-3 rounded-lg min-h-[60px]">
                      {selectedReview.description || '暂无描述'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">评审建议</label>
                    <div className="text-sm bg-gray-50 p-3 rounded-lg min-h-[60px]">
                      {selectedReview.reviewAdvice || '暂无建议'}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">评审文件</label>
                  <div className="space-y-2 max-h-[100px] overflow-y-auto scrollbar-visible" style={{ overflowY: 'scroll' }}>
                    {selectedReview.projectFiles && selectedReview.projectFiles.length > 0 ? (
                      selectedReview.projectFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg h-[40px]">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{file.name || '未知文件'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handlePreviewFile(file.name)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览文件"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDownloadFile(file.name)}
                              className="text-blue-500 hover:text-blue-700"
                              title="下载文件"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-sm text-gray-500 p-3 bg-gray-50 rounded-lg">暂无评审文件</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingReview && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black/50"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto relative">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">编辑输入评审</h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={editingReview.projectName}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={editingReview.reviewName}
                        onChange={(e) => {
                          setEditingReview({ ...editingReview, reviewName: e.target.value });
                          if (e.target.value) {
                            setErrors(prev => ({ ...prev, reviewName: '' }));
                          }
                        }}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.reviewName ? 'border-red-500' : ''}`}
                      />
                      {errors.reviewName && (
                        <div className="mt-1 text-sm text-red-500">{errors.reviewName}</div>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审状态 <span className="text-red-500">*</span>
                      </label>
                      <div className={`w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-700`}>
                        {reviewStatuses.find(s => s.id === editingReview.reviewStatus)?.name || '未知状态'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审组长 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        {/* 使用原生select元素替代自定义下拉组件 */}
                        <select
                          value={editingReview.reviewLeader || ''}
                          onChange={(e) => {
                            const selectedId = e.target.value;
                            console.log('编辑模式 - 选择评审组长:', selectedId);
                            const selectedLeader = employees.find(emp => emp.id === Number(selectedId));
                            setEditingReview({
                              ...editingReview,
                              reviewLeader: selectedId,
                              reviewLeaderName: selectedLeader ? selectedLeader.name : ''
                            });
                          }}
                          name="editReviewLeader"
                          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                        >
                          <option value="">请选择评审组长</option>
                          {employees.map(employee => (
                            <option key={employee.id} value={employee.id.toString()}>
                              {employee.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审人员 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div
                          className={`reviewer-select w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto ${errors.reviewerIds ? 'border-red-500' : ''}`}
                          onClick={() => setShowReviewerSelect(!showReviewerSelect)}
                        >
                          {editingReview.reviewersId?.length > 0 ? (
                            <div className="flex flex-wrap">
                              {editingReview.reviewersId.map((id) => {
                                const employee = employees.find(e => e.id === Number(id));
                                return (
                                  <div key={id} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                                    <span className="text-sm">{employee?.name}</span>
                                    <button
                                      type="button"
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        setEditingReview(prev => ({
                                          ...prev,
                                          reviewersId: prev.reviewersId.filter(rid => rid !== id),
                                          reviewersName: prev.reviewersName.filter(name => name !== employee?.name)
                                        }));
                                      }}
                                      className="text-gray-400 hover:text-red-500 ml-1"
                                    >
                                      <Cross2Icon className="w-4 h-4" />
                                    </button>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择评审人员</span>
                          )}
                        </div>
                        {showReviewerSelect && (
                          <div
                            className="absolute z-[60] w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto reviewer-select"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="flex flex-wrap p-2 gap-2">
                              {employees
                                .filter(emp => emp.id.toString() !== editingReview.reviewLeader)
                                .map((employee) => (
                                  <div
                                    key={employee.id}
                                    className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                    onClick={() => {
                                      const id = employee.id;
                                      setEditingReview(prev => {
                                        const reviewersId = prev.reviewersId || [];
                                        const reviewersName = prev.reviewersName || [];
                                        if (reviewersId.includes(id)) {
                                          return {
                                            ...prev,
                                            reviewersId: reviewersId.filter(rid => rid !== id),
                                            reviewersName: reviewersName.filter(name => name !== employee.name)
                                          };
                                        } else {
                                          return {
                                            ...prev,
                                            reviewersId: [...reviewersId, id],
                                            reviewersName: [...reviewersName, employee.name]
                                          };
                                        }
                                      });
                                      if (errors.reviewerIds) {
                                        setErrors(prev => ({ ...prev, reviewerIds: '' }));
                                      }
                                    }}
                                  >
                                    <input
                                      type="checkbox"
                                      checked={editingReview.reviewersId?.includes(employee.id)}
                                      onChange={() => { }}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    <div className={`flex-1 px-2 py-1 rounded ${(editingReview.reviewersId || []).includes(employee.id) ? 'bg-blue-50' : ''}`}>
                                      <span className="text-sm">{employee.name}</span>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}
                      </div>
                      {errors.reviewerIds && (
                        <div className="mt-1 text-sm text-red-500">
                          {errors.reviewerIds}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审时间 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center ${errors.reviewTime ? 'border-red-500' : ''
                          }`}>
                          <input
                            type="datetime-local"
                            step="1"
                            value={editingReview.reviewDate || ''}
                            onChange={(e) => {
                              setEditingReview(prev => ({
                                ...prev,
                                reviewDate: e.target.value
                              }));
                            }}
                            className="absolute inset-0 opacity-0 cursor-pointer w-full"
                          />
                          <span className={`${editingReview.reviewDate ? 'text-gray-900' : 'text-gray-400'}`}>
                            {editingReview.reviewDate ? new Date(editingReview.reviewDate).toLocaleString('zh-CN', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                              hour: '2-digit',
                              minute: '2-digit',
                              second: '2-digit',
                              hour12: false
                            }) : '请选择评审时间'}
                          </span>
                          <CalendarIcon className="w-4 h-4 text-gray-400 ml-auto" />
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审文件 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div
                          className={`file-select w-full px-3 py-2 border rounded-lg bg-white cursor-pointer max-h-[120px] overflow-y-auto ${errors.designInputFiles ? 'border-red-500' : ''}`}
                          onClick={() => setShowFileSelect(!showFileSelect)}
                        >
                          {(editingReview.designInputFiles || []).length > 0 ? (
                            <div className="flex flex-wrap">
                              {editingReview.designInputFiles.map((fileName) => (
                                <div key={fileName} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1 mr-2 mb-1">
                                  <span className="text-sm">{fileName}</span>
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      setEditingReview(prev => ({
                                        ...prev,
                                        designInputFiles: prev.designInputFiles.filter(f => f !== fileName)
                                      }));
                                    }}
                                    className="text-gray-400 hover:text-red-500 ml-1"
                                  >
                                    <Cross2Icon className="w-4 h-4" />
                                  </button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">请选择评审文件</span>
                          )}
                        </div>
                        {showFileSelect && (
                          <div className="absolute z-[60] w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto file-select">
                            <div className="flex flex-wrap p-2 gap-2">
                              {designInputs.map((file) => (
                                <div
                                  key={file.name}
                                  className="flex items-center gap-2 cursor-pointer w-full p-2 hover:bg-gray-50"
                                  onClick={() => {
                                    const fileName = file.name;
                                    setEditingReview(prev => {
                                      const newFiles = (prev.designInputFiles || []).includes(fileName)
                                        ? (prev.designInputFiles || []).filter(f => f !== fileName)
                                        : [...(prev.designInputFiles || []), fileName];

                                      return {
                                        ...prev,
                                        designInputFiles: newFiles
                                      };
                                    });
                                    if (errors.designInputFiles) {
                                      setErrors(prev => ({ ...prev, designInputFiles: '' }));
                                    }
                                  }}
                                >
                                  <input
                                    type="checkbox"
                                    checked={(editingReview.designInputFiles || []).includes(file.name)}
                                    onChange={() => { }}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                  <div className={`flex-1 px-2 py-1 rounded ${(editingReview.designInputFiles || []).includes(file.name) ? 'bg-blue-50' : ''}`}>
                                    <span className="text-sm">{file.name}</span>
                                  </div>
                                </div>
                              ))}
                              {designInputs.length === 0 && (
                                <div className="text-center py-4 text-gray-500 w-full">
                                  暂无可选文件
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      {errors.designInputFiles && (
                        <div className="mt-1 text-sm text-red-500">
                          {errors.designInputFiles}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审类型 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        {/* 使用原生select元素替代自定义下拉组件 */}
                        <select
                          value={editingReview.reviewType !== null && editingReview.reviewType !== undefined ? editingReview.reviewType : ''}
                          onChange={(e) => {
                            const selectedType = e.target.value ? Number(e.target.value) : null;
                            console.log('编辑模式 - 选择评审类型:', selectedType);
                            setEditingReview({
                              ...editingReview,
                              reviewType: selectedType
                            });
                          }}
                          name="editReviewType"
                          className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                        >
                          <option value="">请选择评审类型</option>
                          {reviewTypes.map(type => (
                            <option key={type.id} value={type.id}>
                              {type.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审标准
                      </label>
                      <textarea
                        value={editingReview.reviewStandard || ''}
                        onChange={(e) => setEditingReview({ ...editingReview, reviewStandard: e.target.value })}
                        rows={1}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入评审标准..."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审描述
                      </label>
                      <textarea
                        value={editingReview.description || ''}
                        onChange={(e) => setEditingReview({ ...editingReview, description: e.target.value })}
                        rows={1}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入评审描述..."
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        评审建议
                      </label>
                      <textarea
                        value={editingReview.reviewAdvice || ''}
                        onChange={(e) => setEditingReview({ ...editingReview, reviewAdvice: e.target.value })}
                        rows={1}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入评审建议..."
                      />
                    </div>
                  </div>


                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowEditModal(false)}>
                  取消
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleRejectReview}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  不通过
                </Button>
                <Button
                  onClick={handleApproveReview}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  通过
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-2">确认删除</h3>
              <p className="text-gray-600 mb-4">
                确定要删除项目 &ldquo;{reviews.find(r => r.id === deletingReviewId)?.reviewName}&rdquo; 吗？此操作不可恢复。
              </p>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setDeletingReviewId(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  variant="destructive"
                  onClick={confirmDelete}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* 添加错误提示组件 */}
      {showError && <ErrorMessage message={errorMessage} />}
    </div>
  );
});