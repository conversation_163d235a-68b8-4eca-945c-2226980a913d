import axios from 'axios';

import { fetchData } from './fetch';

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () =>
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
};

// 设计方案相关接口
export const designProposalApi = {
  // 获取方案列表（分页）
  getProposalList: (projectId, page, size, params = {}) => {
    const searchParams = new URLSearchParams({ projectId, page, size, ...params });
    return fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/page?${searchParams}`).then(res => res.json());
  },

  // 获取单个方案详情
  getProposalDetail: (proposalId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/one/${proposalId}`).then(res => res.json()),

  // 创建方案
  createProposal: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新方案
  updateProposal: (proposalId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/update/${proposalId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除方案
  deleteProposal: (proposalId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/${proposalId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 提交评审
  submitReview: (schemeDesignId, projectId, reviewDate, reviewLeader, reviewerIds) => {
    const params = new URLSearchParams({
      schemeDesignId,
      projectId,
      reviewDate,
      reviewLeader
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/scheme-designs/submit?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewerIds)
    }).then(res => res.json());
  }
};

// 文件相关接口
export const fileApi = {
  // 上传文件
  uploadFile: (file, bucketName) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bucketName', bucketName);
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/upload`, {
      method: 'POST',
      body: formData
    }).then(res => res.text());
  },

  // 下载文件
  downloadFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },

  // 预览文件
  previewFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 获取所有模板文件名
  getAllTemplateFiles: () => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/getAllFileName?bucketName=model`).then(res => res.json()),

  // 下载模板文件
  downloadTemplateFile: (fileName) => {
    const params = new URLSearchParams({ fileName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download/module/file?${params}`);
  },

  // 下载模块文件
  downloadModuleFile: (fileName) => {
    const params = new URLSearchParams({ fileName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download/module/file?${params}`);
  }
}; 