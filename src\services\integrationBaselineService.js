import axios from 'axios';

import { fetchData } from './fetch';

const STAFF_URL = 'http://172.27.1.153:8081';

// 集成基线相关接口
export const integrationBaselineApi = {
  // 获取基线列表
  getBaselineList: (projectId, page, size, name = '') => {
    const params = new URLSearchParams({ page, size, name });
    return axios.get(`${fetchData["PROJECT_URL"]}/api/integration/line/page?projectId=${projectId}&${params}`);
  },

  // 获取单个基线详情
  getBaselineDetail: (baselineId) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/integration/line/one/${baselineId}`),

  // 创建基线
  createBaseline: (formData) => 
    axios.post(`${fetchData["PROJECT_URL"]}/api/integration/line/create`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json'
      }
    }),

  // 更新基线
  updateBaseline: (baselineId, formData) => 
    axios.post(`${fetchData["PROJECT_URL"]}/api/integration/line/update/${baselineId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json'
      }
    }),

  // 删除基线
  deleteBaseline: (baselineId) => 
    axios.delete(`${fetchData["PROJECT_URL"]}/api/integration/line/${baselineId}`),
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName) => {
    return axios.get(`${fetchData["PROJECT_URL"]}/api/file/preview`, {
      params: { fileName, bucketName },
      responseType: 'blob'
    });
  },

  // 下载文件
  downloadFile: (fileName, bucketName) => {
    return axios.get(`${fetchData["PROJECT_URL"]}/api/file/download`, {
      params: { fileName, bucketName },
      responseType: 'blob'
    });
  }
};


// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: () => 
    axios.get(`${fetchData["STAFF_URL"]}/api/projects/list`),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    axios.get(`${fetchData["BASE_URL"]}/api/employees/list`),
}; 