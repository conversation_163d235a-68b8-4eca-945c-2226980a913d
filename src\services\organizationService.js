// 组织架构相关的 API 服务
import { fetchData } from './fetch';

const API_PREFIX = {
  "organization-service": fetchData["BASE_URL"]
}

// 获取所有组织数据（每次都从服务器获取最新数据）
export const getAllOrganizations = () => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/all`,
    method: 'GET'
  })
}

// 创建新部门
export const createDepartment = (departmentData) => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/create`,
    method: 'POST',
    data: departmentData
  })
}

// 更新部门信息
export const updateDepartment = (id, departmentData) => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/update/${id}`,
    method: 'POST',
    data: departmentData
  })
}

// 删除部门
export const deleteDepartment = (id) => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/delete/${id}`,
    method: 'GET'
  })
}

// 搜索部门
export const searchDepartment = () => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/search-department`,
    method: 'GET'
  })
}

// 搜索组织
export const searchOrganizations = (keyword) => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/organizations/search`,
    method: 'GET',
    params: { keyword }
  })
}

// 检查部门下的员工
export const checkDepartmentEmployees = (organizationId, params = { page: 0, size: 10 }) => {
  return fetch({
    url: `${API_PREFIX["organization-service"]}/api/employees/list/${organizationId}`,
    method: 'GET',
    params
  })
}