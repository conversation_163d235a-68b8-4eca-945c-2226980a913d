import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { 
  MagnifyingGlassIcon,
  GitHubLogoIcon,
  BranchIcon,
  CommitIcon,
  FileTextIcon,
  ChevronRightIcon,
  ClockIcon,
  PersonIcon,
  CodeIcon,
  PlusIcon,
  Cross2Icon,
  ChatBubbleIcon,
  CheckCircledIcon,
  CrossCircledIcon,
  QuestionMarkCircledIcon,
  ChevronDownIcon,
  DotFilledIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';

const reviewStatuses = [
  { id: 'pending', name: '待评审', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'reviewing', name: '评审中', color: 'bg-blue-100 text-blue-800' },
  { id: 'approved', name: '已通过', color: 'bg-green-100 text-green-800' },
  { id: 'rejected', name: '未通过', color: 'bg-red-100 text-red-800' }
];

const mockReviews = [
  {
    id: 'CR-001',
    title: '用户认证模块重构',
    author: '张三',
    reviewers: ['李四', '王五'],
    status: 'reviewing',
    createdAt: '2024-02-20 14:30',
    updatedAt: '2024-02-21 10:00',
    description: '重构用户认证模块，提升安全性和性能',
    commits: [
      {
        id: 'abc123',
        message: 'Refactor auth module',
        files: [
          {
            path: 'src/auth/index.js',
            changes: {
              added: 50,
              deleted: 30,
              modified: 20
            },
            diff: `@@ -1,5 +1,7 @@
+ import { jwt } from 'jsonwebtoken';
+ import { hash } from 'bcrypt';
  
  export class AuthService {
-   constructor() {
+   constructor(config) {
+     this.config = config;
    }
            `
          }
        ]
      }
    ],
    comments: [
      {
        id: 1,
        user: '李四',
        file: 'src/auth/index.js',
        line: 15,
        content: '建议添加参数验证',
        time: '2024-02-21 09:30',
        resolved: false
      }
    ]
  }
];

export const CodeReview = observer(() => {
  const [reviews, setReviews] = useState(mockReviews);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [newComment, setNewComment] = useState('');
  const [expandedFile, setExpandedFile] = useState(null);

  const handleViewDetail = (review) => {
    setSelectedReview(review);
    setShowDetailModal(true);
  };

  const handleAddComment = () => {
    if (!newComment.trim()) {
      alert('请输入评论内容');
      return;
    }

    const newCommentData = {
      id: Date.now(),
      user: '当前用户',
      content: newComment,
      time: new Date().toLocaleString(),
      resolved: false
    };

    const updatedReview = {
      ...selectedReview,
      comments: [...selectedReview.comments, newCommentData]
    };

    setReviews(reviews.map(r => 
      r.id === selectedReview.id ? updatedReview : r
    ));
    setSelectedReview(updatedReview);
    setNewComment('');
  };

  const handleResolveComment = (commentId) => {
    const updatedReview = {
      ...selectedReview,
      comments: selectedReview.comments.map(c =>
        c.id === commentId ? { ...c, resolved: !c.resolved } : c
      )
    };

    setReviews(reviews.map(r => 
      r.id === selectedReview.id ? updatedReview : r
    ));
    setSelectedReview(updatedReview);
  };

  const handleApprove = () => {
    const updatedReview = {
      ...selectedReview,
      status: 'approved',
      updatedAt: new Date().toLocaleString()
    };

    setReviews(reviews.map(r => 
      r.id === selectedReview.id ? updatedReview : r
    ));
    setSelectedReview(updatedReview);
  };

  const handleReject = () => {
    const updatedReview = {
      ...selectedReview,
      status: 'rejected',
      updatedAt: new Date().toLocaleString()
    };

    setReviews(reviews.map(r => 
      r.id === selectedReview.id ? updatedReview : r
    ));
    setSelectedReview(updatedReview);
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">代码评审</h1>
          <p className="text-gray-500">管理代码评审任务</p>
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索代码评审..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
          <select className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">状态（全部）</option>
            <option value="reviewing">评审中</option>
            <option value="approved">已通过</option>
            <option value="rejected">已拒绝</option>
            <option value="closed">已关闭</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {reviews
          .filter(review => 
            review.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            review.id.toLowerCase().includes(searchQuery.toLowerCase())
          )
          .map(review => (
            <div
              key={review.id}
              className="bg-white p-4 rounded-lg shadow-sm hover:bg-gray-50"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <CodeIcon className="w-8 h-8 text-blue-500" />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{review.title}</span>
                      <span className="text-sm text-gray-500">({review.id})</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      作者：{review.author} | 创建时间：{review.createdAt}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-8">
                  <div className="text-sm">
                    <div className="text-gray-500">分支</div>
                    <div className="flex items-center gap-1">
                      <BranchIcon className="w-4 h-4" />
                      {review.branch}
                    </div>
                  </div>
                  <div className="text-sm">
                    <div className="text-gray-500">评审者</div>
                    <div>{review.reviewers.join(', ')}</div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-sm ${
                    reviewStatuses.find(s => s.id === review.status)?.color
                  }`}>
                    {reviewStatuses.find(s => s.id === review.status)?.name}
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => handleViewDetail(review)}
                  >
                    查看详情
                  </Button>
                </div>
              </div>
              {review.comments.length > 0 && (
                <div className="mt-4 pl-12">
                  <div className="text-sm text-gray-500 mb-2">最新评论</div>
                  <div className="space-y-2">
                    {review.comments.map(comment => (
                      <div
                        key={comment.id}
                        className="flex items-start gap-2 p-3 bg-gray-50 rounded"
                      >
                        <ChatBubbleIcon className="w-4 h-4 text-gray-400 mt-1" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium">{comment.user}</span>
                            <span className="text-sm text-gray-500">{comment.time}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            {comment.file}:{comment.line}
                          </div>
                          <div className="text-gray-600 mt-1">{comment.content}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[90vh] flex flex-col">
            <div className="p-6 border-b flex justify-between items-center">
              <div>
                <h3 className="text-xl font-semibold">{selectedReview.title}</h3>
                <div className="text-sm text-gray-500 mt-1">
                  {selectedReview.id}
                </div>
              </div>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {/* Description */}
              <div className="p-6 border-b">
                <h4 className="font-medium mb-2">描述</h4>
                <p className="text-gray-600">{selectedReview.description}</p>
              </div>

              {/* Files */}
              <div className="p-6 border-b">
                <h4 className="font-medium mb-4">变更文件</h4>
                {selectedReview.commits.map(commit => (
                  <div key={commit.id}>
                    {commit.files.map(file => (
                      <div key={file.path} className="mb-4">
                        <div
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg cursor-pointer"
                          onClick={() => setExpandedFile(expandedFile === file.path ? null : file.path)}
                        >
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4 text-gray-400" />
                            <span className="font-medium">{file.path}</span>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className="text-sm">
                              <span className="text-green-600">+{file.changes.added}</span>
                              {' '}
                              <span className="text-red-600">-{file.changes.deleted}</span>
                              {' '}
                              <span className="text-blue-600">~{file.changes.modified}</span>
                            </span>
                            <ChevronDownIcon className={`w-4 h-4 transition-transform ${
                              expandedFile === file.path ? 'transform rotate-180' : ''
                            }`} />
                          </div>
                        </div>
                        {expandedFile === file.path && (
                          <div className="mt-2 p-4 bg-gray-900 rounded-lg overflow-x-auto">
                            <pre className="text-gray-100 font-mono text-sm">
                              {file.diff}
                            </pre>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ))}
              </div>

              {/* Comments */}
              <div className="p-6">
                <h4 className="font-medium mb-4">评论</h4>
                <div className="space-y-4">
                  {selectedReview.comments.map(comment => (
                    <div
                      key={comment.id}
                      className={`p-4 rounded-lg border ${
                        comment.resolved ? 'bg-gray-50' : 'bg-white'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{comment.user}</span>
                            <span className="text-sm text-gray-500">{comment.time}</span>
                          </div>
                          {comment.file && (
                            <div className="text-sm text-gray-500 mt-1">
                              {comment.file}:{comment.line}
                            </div>
                          )}
                          <div className="mt-2">{comment.content}</div>
                        </div>
                        <Button
                          variant={comment.resolved ? "outline" : "default"}
                          size="sm"
                          onClick={() => handleResolveComment(comment.id)}
                        >
                          {comment.resolved ? '重新打开' : '标记已解决'}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 space-y-4">
              <div>
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="添加评论..."
                />
              </div>
              <div className="flex justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowDetailModal(false)}
                  >
                    关闭
                  </Button>
                  <Button onClick={handleAddComment}>
                    添加评论
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="text-red-600 hover:bg-red-50"
                    onClick={handleReject}
                  >
                    拒绝
                  </Button>
                  <Button
                    className="bg-green-600 hover:bg-green-700"
                    onClick={handleApprove}
                  >
                    通过
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});