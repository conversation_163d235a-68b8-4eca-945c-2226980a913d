import axios from 'axios';

import { fetchData } from './fetch';

// 输入评审相关接口
export const inputReviewApi = {
  // 获取评审列表
  getReviewList: (projectId, page, size, reviewerId) => {
    const params = new URLSearchParams({ projectId, page, size, reviewerId });
    return fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/all?${params}`).then(res => res.json());
  },

  // 获取单个评审详情
  getReviewDetail: (reviewId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/one/${reviewId}`).then(res => res.json()),

  // 创建评审
  createReview: (reviewData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewData)
    }).then(res => res.json()),

  // 更新评审
  updateReview: (reviewId, reviewData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/update/${reviewId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(reviewData)
    }).then(res => res.json()),

  // 删除评审
  deleteReview: (reviewId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/delete/${reviewId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 确认评审
  confirmReview: (reviewId, status) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/input-reviews/confirm?id=${reviewId}&status=${status}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list`).then(res => res.json()),
};

// 设计输入文件相关接口
export const designInputApi = {
  // 获取设计输入文件列表
  getDesignInputList: (projectId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/all?status=0&projectId=${projectId}`).then(res => res.json()),

  // 获取单个设计输入文件
  getDesignInputDetail: (fileId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/design-inputs/one/${fileId}`).then(res => res.json()),
};

// 文件操作相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`).then(res => res.text());
  },

  // 下载文件
  downloadFile: (fileName, bucketName) => {
    const params = new URLSearchParams({ fileName, bucketName });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
}; 