import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Login } from '../Login';
import { userStore } from '../../store/userStore';

// Mock dependencies
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

jest.mock('../../store/userStore', () => ({
  userStore: {
    login: jest.fn(),
  }
}));

jest.mock('../../services/loginService', () => ({
  loginApi: {
    login: jest.fn(),
  }
}));

const renderLogin = () => {
  return render(
    <BrowserRouter>
      <Login />
    </BrowserRouter>
  );
};

describe('Login Component - Admin Login Test', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('admin login should work correctly', async () => {
    renderLogin();
    
    // 找到表单元素
    const usernameInput = screen.getByPlaceholderText('请输入用户名');
    const passwordInput = screen.getByPlaceholderText('请输入密码');
    const submitButton = screen.getByRole('button', { name: /登录/ });

    // 输入管理员凭据
    fireEvent.change(usernameInput, { target: { value: 'admin' } });
    fireEvent.change(passwordInput, { target: { value: '123456' } });

    // 提交表单
    fireEvent.click(submitButton);

    // 验证登录逻辑
    await waitFor(() => {
      expect(userStore.login).toHaveBeenCalledWith('admin-token', {
        username: 'admin',
        role: 'administrator',
        email: '<EMAIL>'
      });
      expect(mockNavigate).toHaveBeenCalledWith('/project-management/create');
    });
  });

  test('form validation should work', async () => {
    renderLogin();
    
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    // 直接点击提交按钮（不输入任何内容）
    fireEvent.click(submitButton);

    // 应该显示验证错误
    await waitFor(() => {
      expect(screen.getByText('请输入用户名!')).toBeInTheDocument();
    });
  });
});
