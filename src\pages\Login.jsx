import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import { useNavigate, Link } from "react-router-dom";
import { Form, Input, Button, Alert, Card, Typography } from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { userStore } from "../store/userStore";
import { loginApi } from "../services/loginService";

const { Title, Text } = Typography;

/**
 * 登录页面组件
 * 功能：用户登录认证，支持管理员快速登录和API登录两种方式
 */
export const Login = observer(() => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 表单提交处理
  const handleSubmit = async (values) => {
    const { username, password } = values;
    setLoading(true);
    setError("");

    try {
      // 管理员快速登录（测试用）
      if (username === "admin" && password === "123456") {
        // 设置管理员登录状态
        const adminUserData = {
          username: "admin",
          role: "administrator",
          email: "<EMAIL>"
        };
        userStore.login("admin-token", adminUserData);
        navigate("/project-management/create");
        return;
      }

      // API登录验证
      const response = await loginApi.login({ username, password });
      if (response.data) {
        const { token, ...userData } = response.data;
        // 保存用户登录状态
        userStore.login(token, userData);
        navigate("/project-management/create");
      } else {
        setError("用户名或密码错误");
      }
    } catch (error) {
      setError(error.response?.data?.message || "登录失败，请重试");
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };

  // 表单验证失败处理
  const handleSubmitFailed = (errorInfo) => {
    console.log("表单验证失败:", errorInfo);
  };

  // 渲染登录页面UI
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card
        className="w-full max-w-md"
        style={{ boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}
      >
        {/* 页面标题区域 */}
        <div className="text-center mb-8">
          <div className="w-12 h-12 bg-blue-500 rounded-full mx-auto mb-4"></div>
          <Title level={2} className="mb-2">企业管理系统</Title>
          <Text type="secondary">请登录您的账号</Text>
        </div>

        {/* 错误信息显示 */}
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            className="mb-6"
            closable
            onClose={() => setError("")}
          />
        )}

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          onFinishFailed={handleSubmitFailed}
          autoComplete="off"
          layout="vertical"
          size="large"
        >
          {/* 用户名输入框 */}
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 2, message: '用户名至少2个字符!' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              onChange={() => setError("")} // 输入时清除错误信息
            />
          </Form.Item>

          {/* 密码输入框 */}
          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              onChange={() => setError("")} // 输入时清除错误信息
            />
          </Form.Item>

          {/* 忘记密码链接 */}
          <div className="text-right mb-6">
            <Link
              to="/forgot-password"
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              忘记密码？
            </Link>
          </div>

          {/* 登录按钮 */}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
            >
              {loading ? "登录中..." : "登录"}
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
});
