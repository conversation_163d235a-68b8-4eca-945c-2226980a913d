import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { testPlanApi } from '../services/testPlanService';

// 计划状态选项配置
const planStatusOptions = [
  { value: 0, label: '未完成' },
  { value: 1, label: '已完成' }
];

// 错误消息组件
const ErrorMessage = ({ message }) => (
  <div className="absolute -top-16 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 shadow-lg">
    <Cross2Icon className="w-4 h-4 text-red-500" />
    <div className="text-sm text-red-800">{message}</div>
  </div>
);

// 日期时间输入组件
const DateTimeInput = ({ value, onChange, className, placeholder = "请选择时间" }) => (
  <div className="relative">
    <input
      type="datetime-local"
      value={value}
      onChange={onChange}
      className={`${className} ${!value ? 'text-transparent' : ''}`}
      step="1"
    />
    {!value && (
      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
        {placeholder}
      </div>
    )}
  </div>
);

export const TestPlan = observer(() => {
  // 基础数据状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [testPlans, setTestPlans] = useState([]);
  const [employees, setEmployees] = useState([]);

  // 搜索和分页状态
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchStatus, setSearchStatus] = useState('');
  const [searchCreator, setSearchCreator] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const pageSize = 10; // 固定页面大小

  // 弹窗状态
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // 表单数据状态
  const [newPlan, setNewPlan] = useState({
    projectId: '',
    projectName: '',
    planName: '',
    creator: '',
    status: '',
    description: '',
    startTime: '',
    endTime: '',
    files: []
  });
  const [editingPlan, setEditingPlan] = useState(null);
  const [viewingPlan, setViewingPlan] = useState(null);
  const [deletingPlan, setDeletingPlan] = useState(null);

  // 错误和下拉菜单状态
  const [planErrors, setPlanErrors] = useState({});
  const [errorMessage, setErrorMessage] = useState('');
  const [dropdownStates, setDropdownStates] = useState({
    creator: false,
    status: false,
    editCreator: false,
    editStatus: false
  });

  // 下拉菜单引用
  const dropdownRefs = {
    creator: useRef(null),
    status: useRef(null),
    editCreator: useRef(null),
    editStatus: useRef(null)
  };

  // 错误处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 2000);
  };

  // 获取项目列表
  const fetchProjects = async (searchName = '') => {
    try {
      const data = await testPlanApi.getProjectList(searchName);
      setProjects(data || []);
      // 如果没有选中项目且有数据，选择第一个
      if (!selectedProject && data?.length > 0) {
        setSelectedProject(data[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setProjects([]);
      handleError('获取项目列表失败');
    }
  };

  // 项目搜索处理
  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        // 并行获取项目列表和员工列表
        const [projectsData, employeesData] = await Promise.all([
          testPlanApi.getProjectList(),
          testPlanApi.getEmployeeList()
        ]);

        setEmployees(employeesData || []);

        if (projectsData?.length > 0) {
          setProjects(projectsData);
          const firstProject = projectsData[0];
          setSelectedProject(firstProject);
          // 获取第一个项目的测试计划列表
          await fetchTestPlans(firstProject.id);
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
        handleError('初始化数据失败');
      }
    };

    initializeData();
  }, []);

  // 处理下拉菜单外部点击关闭
  useEffect(() => {
    const handleClickOutside = (event) => {
      Object.entries(dropdownRefs).forEach(([key, ref]) => {
        if (ref.current && !ref.current.contains(event.target)) {
          setDropdownStates(prev => ({ ...prev, [key]: false }));
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 获取测试计划列表
  const fetchTestPlans = async (projectId, name = searchQuery, status = searchStatus, creator = searchCreator, page = currentPage) => {
    try {
      const data = await testPlanApi.getTestPlanList(projectId, page, pageSize, name, status, creator);

      // 设置计划列表数据
      setTestPlans(data.content || data.records || data.data || []);

      // 设置分页信息
      const totalCount = data.page?.totalElements || data.total || data.totalElements || 0;
      setTotalElements(totalCount);
      setTotalPages(data.page?.totalPages || data.totalPages || Math.ceil(totalCount / pageSize));
    } catch (error) {
      console.error('获取测试计划列表失败:', error);
      setTestPlans([]);
      handleError('获取测试计划列表失败');
    }
  };

  // 项目选择处理
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    setCurrentPage(0); // 重置页码
    fetchTestPlans(project.id, '', '', '', 0); // 重置搜索条件
  };

  const openNewModal = () => {
    if (!selectedProject) {
      alert('请先选择一个项目');
      return;
    }

    setNewPlan({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      planName: '',
      creator: '',
      status: '',
      description: '',
      startTime: '',
      endTime: '',
      files: []
    });
    setPlanErrors({});
    setShowNewPlanModal(true);
  };

  const handleCreatePlan = async () => {
    const newErrors = {};

    if (!newPlan.planName) {
      newErrors.planName = '请输入计划名称';
    }
    if (!newPlan.creator) {
      newErrors.creator = '请选择创建人';
    }
    if (newPlan.status === '') {
      newErrors.status = '请选择计划状态';
    }
    if (!newPlan.startTime) {
      newErrors.startTime = '请选择开始时间';
    }
    if (!newPlan.endTime) {
      newErrors.endTime = '请选择开始时间';
    }

    setPlanErrors(newErrors);

    if (Object.keys(newErrors).length > 0) return;

    try {
      const selectedEmployee = employees.find(emp => emp.id.toString() === newPlan.creator);
      
      const formData = new FormData();
      
      const planData = {
        name: newPlan.planName,
        description: newPlan.description || '',
        status: parseInt(newPlan.status),
        creatorId: parseInt(newPlan.creator),
        creatorName: selectedEmployee?.name || '',
        projectId: selectedProject.id,
        startTime: newPlan.startTime ? new Date(newPlan.startTime).toISOString() : null,
        endTime: newPlan.endTime ? new Date(newPlan.endTime).toISOString() : null
      };

      formData.append('testPlan', new Blob([JSON.stringify(planData)], {
        type: 'application/json'
      }));
      
      newPlan.files.forEach(file => {
        formData.append('files', file);
      });

      await testPlanApi.createTestPlan(formData);
      await fetchTestPlans(selectedProject.id);

      setShowNewPlanModal(false);
      setNewPlan({
        projectId: '',
        projectName: '',
        planName: '',
        creator: '',
        status: '',
        description: '',
        startTime: '',
        endTime: '',
        files: []
      });

    } catch (error) {
      console.error('创建计划失败:', error);
      handleError('创建计划失败，请重试');
    }
  };

  const handleEditPlan = async (plan) => {
    try {
      const data = await testPlanApi.getTestPlanDetail(plan.id);
      setEditingPlan({
        ...data,
        creator: data.creatorId.toString(),
        status: data.status.toString(),
        files: [],
        projectFiles: data.projectFiles || []
      });
      setShowEditModal(true);
    } catch (error) {
      console.error('获取计划详情失败:', error);
      handleError('获取计划详情失败');
    }
  };

  // 添加表单验证函数
  const validatePlanForm = (plan) => {
    const errors = {};

    if (!plan.name) {
      errors.planName = '请输入计划名称';
    }
    if (!plan.creator) {
      errors.creator = '请选择创建人';
    }
    if (plan.status === '') {
      errors.status = '请选择计划状态';
    }
    if (!plan.startTime) {
      errors.startTime = '请选择开始时间';
    }
    if (!plan.endTime) {
      errors.endTime = '请选择结束时间';
    }

    return errors;
  };

  // 修改 handleUpdatePlan 函数
  const handleUpdatePlan = async () => {
    try {
      const errors = validatePlanForm(editingPlan);
      if (Object.keys(errors).length > 0) {
        setPlanErrors(errors);
        return;
      }

      const selectedEmployee = employees.find(emp => emp.id.toString() === editingPlan.creator);
      
      const formData = new FormData();
      
      const planData = {
        id: editingPlan.id,
        name: editingPlan.name,
        description: editingPlan.description || '',
        status: parseInt(editingPlan.status),
        creatorId: parseInt(editingPlan.creator),
        creatorName: selectedEmployee?.name || '',
        projectId: selectedProject.id,
        startTime: editingPlan.startTime ? new Date(editingPlan.startTime).toISOString() : null,
        endTime: editingPlan.endTime ? new Date(editingPlan.endTime).toISOString() : null,
        projectFiles: editingPlan.projectFiles || []
      };

      formData.append('testPlan', new Blob([JSON.stringify(planData)], {
        type: 'application/json'
      }));
      
      if (editingPlan.files && editingPlan.files.length > 0) {
        editingPlan.files.forEach(file => {
          formData.append('files', file);
        });
      }

      await testPlanApi.updateTestPlan(editingPlan.id, formData);
      await fetchTestPlans(selectedProject.id);
      setShowEditModal(false);
      setEditingPlan(null);
      handleError('更新计划成功');
    } catch (error) {
      console.error('更新计划失败:', error);
      handleError('更新计划失败，请重试');
    }
  };

  const handleViewPlan = async (plan) => {
    try {
      const data = await testPlanApi.getTestPlanDetail(plan.id);
      setViewingPlan(data);
      setShowViewModal(true);
    } catch (error) {
      console.error('获取计划详情失败:', error);
      handleError('获取计划详情失败');
    }
  };

  // 删除计划处理
  const handleDeletePlan = (plan) => {
    setDeletingPlan(plan);
    setShowDeleteModal(true);
  };

  // 确认删除
  const confirmDelete = async () => {
    try {
      await testPlanApi.deleteTestPlan(deletingPlan.id);
      await fetchTestPlans(selectedProject.id);
      setShowDeleteModal(false);
      setDeletingPlan(null);
    } catch (error) {
      console.error('删除计划失败:', error);
      handleError('删除计划失败，请重试');
    }
  };

  // 修改重置函数
  const handleReset = () => {
    setSearchQuery('');
    setSearchStatus('');
    setSearchCreator('');
    setCurrentPage(0); // 重置页码
    fetchTestPlans(selectedProject.id, '', '', '', 0);
  };

  // 添加文件预览处理函数
  const handlePreviewFile = async (fileName) => {
    try {
      const fileUrl = await testPlanApi.getFilePreviewUrl(fileName);
      window.open(fileUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败，请重试');
    }
  };

  // 添加文件下载处理函数
  const handleDownloadFile = async (fileName) => {
    try {
      const url = testPlanApi.getFileDownloadUrl(fileName);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('文件下载失败');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);
    } catch (error) {
      console.error('文件下载失败:', error);
      handleError('文件下载失败，请重试');
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage message={errorMessage} />
      )}

      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        {projects.map(project => (
          <div
            key={project.id}
            className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
              }`}
            onClick={() => handleProjectSelect(project)}
          >
            <div className="font-medium">{project.name}</div>
            <div className="mt-2">
              <span className={`px-2 py-1 text-xs rounded-full ${
                project.status === 0 ? 'bg-gray-100 text-gray-800' :
                project.status === 1 ? 'bg-blue-100 text-blue-800' :
                'bg-green-100 text-green-800'
              }`}>
                {project.status === 0 ? '未开始' :
                 project.status === 1 ? '进行中' :
                 '已结束'}
              </span>
            </div>
          </div>
        ))}
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">测试计划</p>
            </div>

          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索计划..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              <div className="w-[150px]">
                <select
                  value={searchStatus}
                  onChange={(e) => setSearchStatus(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部状态</option>
                  {planStatusOptions.map(status => (
                    <option key={status.value} value={status.value}>{status.label}</option>
                  ))}
                </select>
              </div>

              <div className="w-[150px]">
                <select
                  value={searchCreator}
                  onChange={(e) => setSearchCreator(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部创建人</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>{employee.name}</option>
                  ))}
                </select>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => {
                    setCurrentPage(0); // 重置页码
                    fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, 0);
                  }}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  style={{ backgroundColor: '#007bff', color: 'white' }}
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={openNewModal}
                >
                  <PlusIcon className="w-4 h-4" />
                  添加测试计划
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">计划名称</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建人</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">计划状态</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">计划描述</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">开始时间</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">结束时间</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {testPlans.map(plan => (
                  <tr key={plan.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{plan.name}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">{plan.creatorName}</td>
                    <td className="px-4 py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${plan.status === 1 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                        {plan.status === 1 ? '已完成' : '未完成'}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      <div className="truncate max-w-[200px]">
                        {plan.description || '-'}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {plan.startTime ? new Date(plan.startTime).toLocaleString() : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {plan.endTime ? new Date(plan.endTime).toLocaleString() : '-'}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-3">
                        <button
                          onClick={() => handleViewPlan(plan)}
                          className="text-gray-400 hover:text-green-500"
                          title="查看"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditPlan(plan)}
                          className="text-gray-400 hover:text-blue-500"
                          title="编辑"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeletePlan(plan)}
                          className="text-gray-400 hover:text-red-500"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex items-center justify-end px-4 py-3 border-t">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 mr-4">
                  共 {totalElements} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={() => {
                    const newPage = Math.max(0, currentPage - 1);
                    setCurrentPage(newPage);
                    fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, newPage);
                  }}
                  disabled={currentPage === 0}
                  className="px-2 py-1 text-sm"
                >
                  上一页
                </Button>
                <div className="flex items-center">
                  <button
                    className={`px-3 py-1 text-sm rounded-lg ${currentPage === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                    onClick={() => {
                      setCurrentPage(0);
                      fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, 0);
                    }}
                  >
                    1
                  </button>
                  {currentPage > 2 && <span className="px-2 text-gray-500">...</span>}
                  {currentPage > 1 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => {
                        setCurrentPage(currentPage - 1);
                        fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, currentPage - 1);
                      }}
                    >
                      {currentPage}
                    </button>
                  )}
                  {currentPage > 0 && currentPage < totalPages - 1 && (
                    <button
                      className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                    >
                      {currentPage + 1}
                    </button>
                  )}
                  {currentPage < totalPages - 2 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => {
                        setCurrentPage(currentPage + 1);
                        fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, currentPage + 1);
                      }}
                    >
                      {currentPage + 2}
                    </button>
                  )}
                  {currentPage < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                  {totalPages > 1 && (
                    <button
                      className={`px-3 py-1 text-sm rounded-lg ${currentPage === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                      onClick={() => {
                        setCurrentPage(totalPages - 1);
                        fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, totalPages - 1);
                      }}
                    >
                      {totalPages}
                    </button>
                  )}
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    const newPage = Math.min(totalPages - 1, currentPage + 1);
                    setCurrentPage(newPage);
                    fetchTestPlans(selectedProject.id, searchQuery, searchStatus, searchCreator, newPage);
                  }}
                  disabled={currentPage >= totalPages - 1}
                  className="px-2 py-1 text-sm"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看测试计划</p>
          </div>
        </div>
      )}

      {showNewPlanModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="relative bg-white rounded-lg shadow-xl w-[600px]">
            {errorMessage && <ErrorMessage message={errorMessage} />}
            
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加测试计划</h3>
              <button
                onClick={() => setShowNewPlanModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={newPlan.projectName}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newPlan.planName}
                      onChange={(e) => setNewPlan({ ...newPlan, planName: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.planName ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入计划名称"
                    />
                    {planErrors.planName && <p className="text-red-500 text-sm">{planErrors.planName}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={dropdownRefs.creator}>
                      <div
                        onClick={() => setDropdownStates(prev => ({ ...prev, creator: !prev.creator }))}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newPlan.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newPlan.creator ? 'text-gray-900' : 'text-gray-400'}>
                          {newPlan.creator ? employees.find(emp => emp.id.toString() === newPlan.creator)?.name : '请选择创建人'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.creator ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {dropdownStates.creator && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewPlan({ ...newPlan, creator: employee.id.toString() });
                                  setDropdownStates(prev => ({ ...prev, creator: false }));
                                  if (planErrors.creator) {
                                    setPlanErrors({ ...planErrors, creator: '' });
                                  }
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newPlan.creator === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.creator && <p className="text-red-500 text-sm">{planErrors.creator}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划状态 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={dropdownRefs.status}>
                      <div
                        onClick={() => setDropdownStates(prev => ({ ...prev, status: !prev.status }))}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newPlan.status ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newPlan.status ? 'text-gray-900' : 'text-gray-400'}>
                          {newPlan.status ? planStatusOptions.find(status => status.value.toString() === newPlan.status)?.label : '请选择计划状态'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.status ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {dropdownStates.status && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {planStatusOptions.map(status => (
                              <div
                                key={status.value}
                                onClick={() => {
                                  setNewPlan({ ...newPlan, status: status.value.toString() });
                                  setDropdownStates(prev => ({ ...prev, status: false }));
                                  if (planErrors.status) {
                                    setPlanErrors({ ...planErrors, status: '' });
                                  }
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newPlan.status === status.value.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {status.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.status && <p className="text-red-500 text-sm">{planErrors.status}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间 <span className="text-red-500">*</span>
                    </label>
                    <DateTimeInput
                      value={newPlan.startTime}
                      onChange={(e) => setNewPlan({ ...newPlan, startTime: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.startTime ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {planErrors.startTime && <p className="text-red-500 text-sm">{planErrors.startTime}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结束时间 <span className="text-red-500">*</span>
                    </label>
                    <DateTimeInput
                      value={newPlan.endTime}
                      onChange={(e) => setNewPlan({ ...newPlan, endTime: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.endTime ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {planErrors.endTime && <p className="text-red-500 text-sm">{planErrors.endTime}</p>}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    计划描述
                  </label>
                  <textarea
                    value={newPlan.description}
                    onChange={(e) => setNewPlan({ ...newPlan, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                    placeholder="请输入计划描述..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <label
                    htmlFor="file-upload"
                    className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-500 transition-colors duration-200"
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-8 w-8 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                        <span className="font-medium text-blue-600 hover:text-blue-500">点击上传文件</span>
                        <span>或拖拽文件到这里</span>
                      </div>
                      <p className="text-xs text-gray-500">支持任意文件类型</p>
                      <input
                        id="file-upload"
                        type="file"
                        className="sr-only"
                        multiple
                        onChange={(e) => {
                          const newFiles = Array.from(e.target.files || []);
                          setNewPlan(prev => ({
                            ...prev,
                            files: [...prev.files, ...newFiles]
                          }));
                        }}
                      />
                    </div>
                  </label>

                  {newPlan.files.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <div className="text-sm font-medium text-gray-700">已上传文件：</div>
                      {newPlan.files.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center">
                            <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-600">{file.name}</span>
                          </div>
                          <button
                            onClick={() => {
                              setNewPlan(prev => ({
                                ...prev,
                                files: prev.files.filter((_, i) => i !== index)
                              }));
                            }}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewPlanModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreatePlan}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看计划</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    {selectedProject?.name}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划名称
                    </label>
                    {viewingPlan.name}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    {viewingPlan.creatorName}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划状态
                    </label>
                    <span className={`px-2 py-1 rounded-full text-xs ${viewingPlan.status === 1 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                      }`}>
                      {viewingPlan.status === 1 ? '已完成' : '未完成'}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划描述
                    </label>
                    {viewingPlan.description || '-'}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间
                    </label>
                    {viewingPlan.startTime ? new Date(viewingPlan.startTime).toLocaleString() : '-'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    结束时间
                  </label>
                  {viewingPlan.endTime ? new Date(viewingPlan.endTime).toLocaleString() : '-'}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 ? (
                    <div className="space-y-2">
                      {viewingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center">
                            <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-600">{file.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handlePreviewFile(file.name)}
                              className="text-blue-500 hover:text-blue-700"
                              title="查看文件"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDownloadFile(file.name)}
                              className="text-blue-500 hover:text-blue-700"
                              title="下载文件"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-500">暂无附件</div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑计划</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPlan.name}
                      onChange={(e) => setEditingPlan({ ...editingPlan, name: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.planName ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入计划名称"
                    />
                    {planErrors.planName && <p className="text-red-500 text-sm">{planErrors.planName}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={dropdownRefs.editCreator}>
                      <div
                        onClick={() => setDropdownStates(prev => ({ ...prev, editCreator: !prev.editCreator }))}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.creator ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.creator ? employees.find(emp => emp.id.toString() === editingPlan.creator)?.name : '请选择创建人'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.editCreator ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {dropdownStates.editCreator && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, creator: employee.id.toString() });
                                  setDropdownStates(prev => ({ ...prev, editCreator: false }));
                                  if (planErrors.creator) {
                                    setPlanErrors({ ...planErrors, creator: '' });
                                  }
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.creator === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.creator && <p className="text-red-500 text-sm">{planErrors.creator}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      计划状态 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={dropdownRefs.editStatus}>
                      <div
                        onClick={() => setDropdownStates(prev => ({ ...prev, editStatus: !prev.editStatus }))}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.status ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.status ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.status ? planStatusOptions.find(status => status.value.toString() === editingPlan.status)?.label : '请选择计划状态'}
                        </span>
                        <svg
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${dropdownStates.editStatus ? 'rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>

                      {dropdownStates.editStatus && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {planStatusOptions.map(status => (
                              <div
                                key={status.value}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, status: status.value.toString() });
                                  setDropdownStates(prev => ({ ...prev, editStatus: false }));
                                  if (planErrors.status) {
                                    setPlanErrors({ ...planErrors, status: '' });
                                  }
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.status === status.value.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {status.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.status && <p className="text-red-500 text-sm">{planErrors.status}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间 <span className="text-red-500">*</span>
                    </label>
                    <DateTimeInput
                      value={editingPlan.startTime ? new Date(editingPlan.startTime).toISOString().slice(0, 19) : ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, startTime: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.startTime ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {planErrors.startTime && <p className="text-red-500 text-sm">{planErrors.startTime}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      结束时间 <span className="text-red-500">*</span>
                    </label>
                    <DateTimeInput
                      value={editingPlan.endTime ? new Date(editingPlan.endTime).toISOString().slice(0, 19) : ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, endTime: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${planErrors.endTime ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {planErrors.endTime && <p className="text-red-500 text-sm">{planErrors.endTime}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    计划描述
                  </label>
                  <textarea
                    value={editingPlan.description || ''}
                    onChange={(e) => setEditingPlan({ ...editingPlan, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300"
                    placeholder="请输入计划描述..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    已上传文件
                  </label>
                  {editingPlan.projectFiles && editingPlan.projectFiles.length > 0 ? (
                    <div className="space-y-2">
                      {editingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center">
                            <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-600">{file.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handlePreviewFile(file.name)}
                              className="text-blue-500 hover:text-blue-700"
                              title="查看文件"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                setEditingPlan(prev => ({
                                  ...prev,
                                  projectFiles: prev.projectFiles.filter((_, i) => i !== index)
                                }));
                              }}
                              className="text-red-500 hover:text-red-700"
                              title="删除文件"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 p-2">暂无已上传文件</div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <label
                    htmlFor="edit-file-upload"
                    className="mt-1 flex justify-center px-6 pt-3 pb-3 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-500 transition-colors duration-200"
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-8 w-8 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                        <span className="font-medium text-blue-600 hover:text-blue-500">点击上传文件</span>
                        <span>或拖拽文件到这里</span>
                      </div>
                      <p className="text-xs text-gray-500">支持任意文件类型</p>
                      <input
                        id="edit-file-upload"
                        type="file"
                        className="sr-only"
                        multiple
                        onChange={(e) => {
                          const newFiles = Array.from(e.target.files || []);
                          setEditingPlan(prev => ({
                            ...prev,
                            files: [...(prev.files || []), ...newFiles]
                          }));
                        }}
                      />
                    </div>
                  </label>

                  {editingPlan.files && editingPlan.files.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <div className="text-sm font-medium text-gray-700">已上传文件：</div>
                      {editingPlan.files.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center">
                            <FileTextIcon className="w-4 h-4 text-gray-400 mr-2" />
                            <span className="text-sm text-gray-600">{file.name}</span>
                          </div>
                          <button
                            onClick={() => {
                              setEditingPlan(prev => ({
                                ...prev,
                                files: prev.files.filter((_, i) => i !== index)
                              }));
                            }}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleUpdatePlan}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">确认删除</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-600">
                确定要删除测试计划 "{deletingPlan.name}" 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingPlan(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={confirmDelete}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});