/**
 * 设计基线管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和选择
 * 2. 设计基线的增删改查
 * 3. 基线文件的上传、预览和下载
 * 4. 分页和搜索功能
 * 5. 员工选择和权限管理
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  ClockIcon,
  PersonIcon,
  TrashIcon,
  Pencil1Icon,
  EyeOpenIcon,
  BoxIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { designBaselineApi, fileApi } from '../services/designBaselineService';

// 工具函数：格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// 项目状态映射
const projectStatusMap = {
  0: { text: '未开始', color: 'bg-gray-100 text-gray-600' },
  1: { text: '进行中', color: 'bg-blue-100 text-blue-600' },
  2: { text: '已结束', color: 'bg-green-100 text-green-600' }
};

// 错误提示组件
const ErrorMessage = ({ message, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-white rounded-lg shadow-lg border border-red-100 px-4 py-2 flex items-center gap-2">
        <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
          <Cross2Icon className="w-3 h-3 text-white" />
        </div>
        <span className="text-gray-700">{message}</span>
      </div>
    </div>
  );
};

// 设计基线管理主组件
export const DesignBaseline = observer(() => {
  // 基础状态
  const [baselines, setBaselines] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState(null);
  const [projects, setProjects] = useState([]);
  const [employees, setEmployees] = useState([]);

  // 弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [selectedBaseline, setSelectedBaseline] = useState(null);
  const [editingBaseline, setEditingBaseline] = useState(null);

  // 表单状态
  const [newBaseline, setNewBaseline] = useState({
    name: '',
    description: '',
    creator: '',
    creatorId: 0,
    attachments: []
  });
  const [editingFiles, setEditingFiles] = useState([]);

  // 加载和错误状态
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(10);

  // 删除确认状态
  const [deletingItem, setDeletingItem] = useState(null);
  const [deleteType, setDeleteType] = useState('');

  // 下拉选择状态
  const [isCreatorDropdownOpen, setIsCreatorDropdownOpen] = useState(false);
  const [isEditCreatorDropdownOpen, setIsEditCreatorDropdownOpen] = useState(false);

  // 下拉选择框引用
  const creatorDropdownRef = useRef(null);
  const editCreatorDropdownRef = useRef(null);

  // 点击外部区域关闭下拉选择
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (creatorDropdownRef.current && !creatorDropdownRef.current.contains(event.target)) {
        setIsCreatorDropdownOpen(false);
      }
      if (editCreatorDropdownRef.current && !editCreatorDropdownRef.current.contains(event.target)) {
        setIsEditCreatorDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 查看基线详情
  const handleViewDetail = async (baseline) => {
    setIsLoading(true);
    setErrorMessage('');
    try {
      const response = await designBaselineApi.getBaselineDetail(baseline.id);
      if (response.data) {
        const detailData = {
          ...response.data,
          createdBy: response.data.creatorName || '',
          createdAt: response.data.createdAt ? new Date(response.data.createdAt).toLocaleString() : '',
          updatedAt: response.data.updatedAt ? new Date(response.data.updatedAt).toLocaleString() : '',
          status: response.data.status === 1 ? 'locked' : 'unlocked',
          components: response.data.projectFiles?.map(file => ({
            id: file.id,
            name: file.name || '',
            version: file.uploadTime ? new Date(file.uploadTime).toLocaleString() : '',
            size: file.size || 0,
            displaySize: file.size ? formatFileSize(file.size) : '',
            type: file.type || 0
          })) || []
        };
        setSelectedBaseline(detailData);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('获取基线详情失败:', error);
      setErrorMessage('获取基线详情失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 创建基线
  const handleCreateBaseline = async () => {
    // 验证必填项
    if (!newBaseline.name || !newBaseline.creator || !selectedProject) {
      setErrorMessage('请填写基线名称、选择创建人和项目');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      const designLine = {
        id: 0,
        name: newBaseline.name,
        status: 0,
        createdBy: newBaseline.creatorId,
        description: newBaseline.description,
        projectId: selectedProject.id,
        creatorName: newBaseline.creator
      };

      formData.append('designLine', new Blob([JSON.stringify(designLine)], {
        type: 'application/json'
      }));

      newBaseline.attachments.forEach(file => {
        formData.append('files', file);
      });

      const response = await designBaselineApi.createBaseline(formData);
      if (response.data) {
        setShowNewModal(false);
        resetNewBaseline();
        fetchBaselines(selectedProject.id);
      }
    } catch (error) {
      console.error('创建基线失败:', error);
      setErrorMessage(error.response?.data?.message || '创建失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 重置新建基线表单
  const resetNewBaseline = () => {
    setNewBaseline({
      name: '',
      description: '',
      creator: '',
      creatorId: 0,
      attachments: []
    });
    setErrorMessage('');
  };

  const handleDeleteBaseline = (baseline) => {
    setDeletingItem(baseline);
    setDeleteType('baseline');
    setShowDeleteConfirmModal(true);
  };

  // 重置编辑模态框状态
  const resetEditModalState = () => {
    setEditingBaseline({
      id: null,
      name: '',
      description: '',
      creator: '',
      createdAt: null,
      attachments: [],
      components: [],
      projectFiles: []
    });
    setErrorMessage('');
  };

  // 编辑基线
  const handleEditBaseline = async (baseline) => {
    setIsLoading(true);
    try {
      const [baselineResponse, employeesResponse] = await Promise.all([
        designBaselineApi.getBaselineDetail(baseline.id),
        designBaselineApi.getEmployeeList()
      ]);

      if (baselineResponse.data) {
        setEditingBaseline({
          ...baselineResponse.data,
          projectFiles: baselineResponse.data.projectFiles || [],
          createdBy: baselineResponse.data.creatorName || '',
          creatorId: baselineResponse.data.createdBy || 0,
          originalCreatedAt: baselineResponse.data.createdAt
        });
        setShowEditModal(true);
      }

      if (employeesResponse.data) {
        setEmployees(employeesResponse.data);
      }
    } catch (error) {
      setErrorMessage(error.response?.data?.message || '获取基线详情失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 编辑时文件变更
  const handleEditFileChange = (e) => {
    const files = Array.from(e.target.files);
    setEditingFiles([...editingFiles, ...files]);
  };

  const handleCloseEditModal = () => {
    resetEditModalState();
    setShowEditModal(false);
  };

  // 更新基线
  const handleUpdateBaseline = async () => {
    if (!editingBaseline.name) {
      setErrorMessage('请输入基线名称！');
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      const designLine = {
        id: editingBaseline.id,
        name: editingBaseline.name,
        status: editingBaseline.status === 'locked' ? 1 : 0,
        createdBy: editingBaseline.creatorId || 0,
        description: editingBaseline.description || '',
        projectId: selectedProject.id,
        creatorName: editingBaseline.createdBy,
        createdAt: editingBaseline.originalCreatedAt,
        projectFiles: editingBaseline.projectFiles || [],
        fileIds: editingBaseline.projectFiles?.map(file => file.id) || []
      };

      formData.append('designLine', new Blob([JSON.stringify(designLine)], {
        type: 'application/json'
      }));

      editingFiles.forEach(file => {
        formData.append('files', file);
      });

      const response = await designBaselineApi.updateBaseline(editingBaseline.id, formData);
      if (response.data) {
        resetEditModalState();
        setShowEditModal(false);
        setEditingFiles([]);
        fetchBaselines(selectedProject.id);
      }
    } catch (error) {
      console.error('更新基线失败:', error);
      setErrorMessage(error.response?.data?.message || '更新失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 文件处理
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setNewBaseline({
      ...newBaseline,
      attachments: [...newBaseline.attachments, ...files]
    });
  };

  const handleRemoveFile = (index) => {
    setNewBaseline({
      ...newBaseline,
      attachments: newBaseline.attachments.filter((_, i) => i !== index)
    });
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const response = await designBaselineApi.getEmployeeList();
      if (response.data) {
        setEmployees(response.data);
      }
    } catch (error) {
      console.error('获取员工列表失败:', error);
      setErrorMessage('获取员工列表失败，请重试');
    }
  };

  // 打开新建模态框
  const handleOpenNewModal = () => {
    resetNewBaseline();
    setShowNewModal(true);
    fetchEmployees();
  };

  // 获取项目列表
  const fetchProjects = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await designBaselineApi.getProjectList();
      if (response.data) {
        setProjects(response.data);
        if (response.data.length > 0 && !selectedProject) {
          setSelectedProject(response.data[0]);
        }
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setErrorMessage('获取项目列表失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [selectedProject]);

  // 获取基线列表
  const fetchBaselines = useCallback(async (projectId) => {
    setIsLoading(true);
    try {
      const response = await designBaselineApi.getBaselinePage(projectId, currentPage, pageSize, searchQuery);
      if (response.data) {
        const baselineList = Array.isArray(response.data.content) ? response.data.content : [];
        setTotalPages(response.data.totalPages || 0);
        setTotalRecords(response.data.totalElements || 0);

        const processedBaselines = baselineList.map(baseline => ({
          id: baseline.id || '',
          name: baseline.name || '',
          version: baseline.version || '',
          description: baseline.description || '',
          createdBy: baseline.creatorName || '',
          createdAt: baseline.createdAt ? new Date(baseline.createdAt).toLocaleString() : '',
          updatedAt: baseline.updatedAt ? new Date(baseline.updatedAt).toLocaleString() : '',
          status: baseline.status === 1 ? 'locked' : 'unlocked',
          components: baseline.projectFiles?.map(file => ({
            id: file.id,
            name: file.fileName || '',
            version: file.version || '',
            type: file.fileType || ''
          })) || []
        }));

        setBaselines(processedBaselines);
      } else {
        setBaselines([]);
        setTotalPages(0);
      }
    } catch (error) {
      console.error('获取基线列表失败:', error);
      setErrorMessage('获取基线列表失败，请重试');
      setBaselines([]);
      setTotalPages(0);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize, searchQuery]);

  // 选择项目
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    fetchBaselines(project.id);
  };

  // 预览文件
  const handlePreviewFile = async (file) => {
    setIsLoading(true);
    try {
      const response = await fileApi.previewFile(file.name, 'designline');
      if (response.data) {
        window.open(response.data, '_blank', 'noopener,noreferrer');
      } else {
        setErrorMessage('获取文件预览失败：未获取到文件路径');
      }
    } catch (error) {
      console.error('获取文件预览失败:', error);
      setErrorMessage('获取文件预览失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownloadFile = async (component) => {
    try {
      const response = await fileApi.downloadFile(component.name, 'designline');

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', component.name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      const successDiv = document.createElement('div');
      successDiv.className = 'fixed top-4 left-1/2 -translate-x-1/2 bg-white p-4 rounded-lg shadow-lg z-50 flex items-center';
      successDiv.innerHTML = `
        <svg class="w-5 h-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span>下载成功</span>
      `;
      document.body.appendChild(successDiv);
      setTimeout(() => {
        successDiv.remove();
      }, 3000);
    } catch (error) {
      console.error('下载文件失败:', error);
      setErrorMessage('下载文件失败，请重试');
    }
  };

  const handleConfirmDelete = async () => {
    try {
      if (deleteType === 'baseline') {
        // 调用删除基线接口
        await designBaselineApi.deleteBaseline(deletingItem.id);

        // 关闭弹窗
        setShowDeleteConfirmModal(false);
        setDeletingItem(null);

        // 刷新基线列表
        if (selectedProject) {
          fetchBaselines(selectedProject.id);
        }

        // 显示成功提示
      } else if (deleteType === 'component') {
        setEditingBaseline({
          ...editingBaseline,
          components: editingBaseline.components.filter(c => c.id !== deletingItem.id)
        });
        setShowDeleteConfirmModal(false);
        setDeletingItem(null);
      } else if (deleteType === 'file') {
        setEditingFiles(editingFiles.filter((_, i) => i !== deletingItem.index));
        setShowDeleteConfirmModal(false);
        setDeletingItem(null);
      }
    } catch (error) {
      console.error('删除失败:', error);
      setErrorMessage(error.response?.data?.message || '删除失败，请重试');
    }
  };

  // 搜索基线
  const handleSearch = async () => {
    if (!selectedProject) return;
    setCurrentPage(0);
    fetchBaselines(selectedProject.id);
  };

  // 页码变化处理
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  }, [currentPage, selectedProject]);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setErrorMessage('')}
        />
      )}
      
      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''}`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-2">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  projectStatusMap[project.status]?.color || 'bg-gray-100 text-gray-600'
                }`}>
                  {projectStatusMap[project.status]?.text || '未知状态'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容区 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {selectedProject ? (
          <>
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">设计基线管理</p>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
              <div className="flex flex-wrap gap-4">
                <div className="relative w-[200px]">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="搜索基线..."
                    className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={handleSearch}
                    className="w-[100px] whitespace-nowrap flex items-center justify-center"
                    style={{ backgroundColor: '#007bff', color: 'white' }}
                  >
                    <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                    搜索
                  </Button>
                  <Button
                    className="flex items-center gap-1"
                    onClick={handleOpenNewModal}
                  >
                    <PlusIcon className="w-4 h-4" />
                    创建基线
                  </Button>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
              <div className="divide-y">
                <div className="p-4 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="grid grid-cols-4 gap-8 flex-1 mr-8">
                      <div className="text-sm font-medium text-gray-500">基线名称</div>
                      <div className="text-sm font-medium text-gray-500">创建者</div>
                      <div className="text-sm font-medium text-gray-500">创建时间</div>
                      <div className="text-sm font-medium text-gray-500">操作</div>
                    </div>
                  </div>
                </div>

                {baselines.map(baseline => (
                  <div key={baseline.id} className="p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="grid grid-cols-4 gap-8 flex-1 mr-8">
                        <div className="flex items-center gap-2">
                          <BoxIcon className="w-4 h-4 text-gray-400" />
                          <span>{baseline.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <PersonIcon className="w-4 h-4 text-gray-400" />
                          <span>{baseline.createdBy}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <ClockIcon className="w-4 h-4 text-gray-400" />
                          <span>{baseline.createdAt}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetail(baseline)}
                          >
                            <EyeOpenIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditBaseline(baseline)}
                          >
                            <Pencil1Icon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600"
                            onClick={() => handleDeleteBaseline(baseline)}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-end mt-4">
              <div className="flex items-center gap-2">
                <span>共 {totalRecords} 条记录</span>
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 0}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  上一页
                </Button>

                {Array.from({ length: totalPages }, (_, index) => (
                  <Button
                    key={index}
                    onClick={() => handlePageChange(index)}
                    className={`px-3 py-1 border rounded ${currentPage === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                  >
                    {index + 1}
                  </Button>
                ))}

                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages - 1}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  下一页
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目查看设计基线</p>
            </div>
          </div>
        )}

        {/* Detail Modal */}
        {showDetailModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">设计基线详情</h3>
                <button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedBaseline(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>

              {isLoading ? (
                <div className="p-6 text-center text-gray-500">
                  加载中...
                </div>
              ) : selectedBaseline && (
                <>
                  <div className="p-6">
                    <div className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            项目名称
                          </label>
                          <div className="text-base">{selectedProject?.name}</div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            基线名称
                          </label>
                          <div className="text-base">{selectedBaseline.name}</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            创建人
                          </label>
                          <div className="text-base">{selectedBaseline.createdBy}</div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            描述
                          </label>
                          <div className="text-base">{selectedBaseline.description}</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            创建时间
                          </label>
                          <div className="text-base">{selectedBaseline.createdAt}</div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-500 mb-1">
                            更新时间
                          </label>
                          <div className="text-base">{selectedBaseline.updatedAt}</div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-500 mb-2">
                          附件
                        </label>
                        <div className="space-y-2">
                          {selectedBaseline.components.map(component => (
                            <div
                              key={component.id}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-3">
                                <FileTextIcon className="w-5 h-5 text-gray-400" />
                                <div>
                                  <div className="font-medium">{component.name}</div>
                                  <div className="text-sm text-gray-500">
                                    上传时间：{component.version}
                                    {component.displaySize && (
                                      <span className="ml-2">大小：{component.displaySize}</span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handlePreviewFile(component)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <EyeOpenIcon className="w-4 h-4 mr-1" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownloadFile(component)}
                                  className="text-blue-600 hover:text-blue-800"
                                >
                                  <DownloadIcon className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="p-6 border-t bg-gray-50 flex justify-end">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowDetailModal(false);
                        setSelectedBaseline(null);
                      }}
                    >
                      关闭
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}



        {/* New Baseline Modal */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">创建设计基线</h3>
                <button
                  onClick={() => setShowNewModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  {/* 项目名称 */}
                  <div className="w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>

                  {/* 基线名称和创建人 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        基线名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="基线名称"
                        value={newBaseline.name}
                        onChange={(e) => {
                          const errorMessage = e.target.parentNode.querySelector('.error-message');
                          if (errorMessage) {
                            errorMessage.remove();
                            e.target.classList.remove('border-red-500');
                          }
                          setNewBaseline({ ...newBaseline, name: e.target.value });
                        }}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入基线名称"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        创建人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={creatorDropdownRef}>
                        <div
                          onClick={() => setIsCreatorDropdownOpen(!isCreatorDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !newBaseline.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={newBaseline.creator ? 'text-gray-900' : 'text-gray-400'}>
                            {newBaseline.creator ? employees.find(emp => emp.name === newBaseline.creator)?.name : '请选择创建人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isCreatorDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(employee => (
                                <div
                                  key={employee.id}
                                  onClick={() => {
                                    setNewBaseline({
                                      ...newBaseline,
                                      creator: employee.name,
                                      creatorId: employee.id
                                    });
                                    setIsCreatorDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    newBaseline.creator === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {employee.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 基线描述 */}
                  <div className="w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线描述
                    </label>
                    <textarea
                      value={newBaseline.description}
                      onChange={(e) => setNewBaseline({ ...newBaseline, description: e.target.value })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入基线描述..."
                    />
                  </div>

                  {/* 附件上传 */}
                  <div className="w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      附件
                    </label>


                    {/* 文件上传框 */}
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors"
                      onClick={() => {
                        document.getElementById('file-upload').click();
                      }}
                    >
                      <div className="text-center">
                        <div className="flex flex-col items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V7a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <div className="text-sm text-gray-600">点击或拖拽文件到此处上传</div>
                          <div className="text-xs text-gray-500">支持 Word、PDF、图片等格式</div>
                        </div>
                      </div>
                      <input
                        id="file-upload"
                        type="file"
                        multiple
                        className="hidden"
                        onChange={handleFileChange}
                      />
                    </div>
                    {/* 已上传文件列表 */}
                    {newBaseline.attachments.length > 0 && (
                      <div className="mb-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">已选择的文件：</div>
                        <div className="space-y-2">
                          {newBaseline.attachments.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-4 h-4 text-gray-400" />
                                <span className="font-medium">{file.name}</span>
                                <span className="text-sm text-gray-500">
                                  ({formatFileSize(file.size)})
                                </span>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveFile(index);
                                }}
                              >
                                <TrashIcon className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowNewModal(false)}>
                  取消
                </Button>
                <Button
                  onClick={handleCreateBaseline}
                  disabled={isLoading}
                >
                  {isLoading ? '创建中...' : '创建基线'}
                </Button>
              </div>
            </div>
          </div>
        )}



        {/* Edit Baseline Modal */}
        {showEditModal && editingBaseline && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">编辑设计基线</h3>
                <button
                  onClick={handleCloseEditModal}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        基线名称 <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="基线名称"
                        value={editingBaseline.name}
                        onChange={(e) => {
                          const errorMessage = e.target.parentNode.querySelector('.error-message');
                          if (errorMessage) {
                            errorMessage.remove();
                            e.target.classList.remove('border-red-500');
                          }
                          setEditingBaseline({ ...editingBaseline, name: e.target.value });
                        }}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入基线名称"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        创建人 <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" ref={editCreatorDropdownRef}>
                        <div
                          onClick={() => setIsEditCreatorDropdownOpen(!isEditCreatorDropdownOpen)}
                          className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                            !editingBaseline.createdBy ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                          }`}
                        >
                          <span className={editingBaseline.createdBy ? 'text-gray-900' : 'text-gray-400'}>
                            {editingBaseline.createdBy ? employees.find(emp => emp.name === editingBaseline.createdBy)?.name : '请选择创建人'}
                          </span>
                          <svg 
                            className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                            viewBox="0 0 20 20" 
                            fill="currentColor"
                          >
                            <path 
                              fillRule="evenodd" 
                              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                              clipRule="evenodd" 
                            />
                          </svg>
                        </div>
                        
                        {isEditCreatorDropdownOpen && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                            <div className="py-1 max-h-60 overflow-auto">
                              {employees.map(employee => (
                                <div
                                  key={employee.id}
                                  onClick={() => {
                                    setEditingBaseline({
                                      ...editingBaseline,
                                      createdBy: employee.name,
                                      creatorId: employee.id
                                    });
                                    setIsEditCreatorDropdownOpen(false);
                                  }}
                                  className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                    editingBaseline.createdBy === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                  }`}
                                >
                                  {employee.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      版本描述
                    </label>
                    <textarea
                      value={editingBaseline.description}
                      onChange={(e) => setEditingBaseline({
                        ...editingBaseline,
                        description: e.target.value
                      })}
                      rows={2}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入版本描述..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文件列表
                    </label>
                    {/* 已有文件列表 */}
                    {editingBaseline.projectFiles && editingBaseline.projectFiles.length > 0 && (
                      <div className="mb-4 space-y-2">
                        {editingBaseline.projectFiles.map((file) => (
                          <div
                            key={file.id}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-4 h-4 text-gray-400" />
                              <span className="font-medium">{file.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreviewFile(file)}
                                className="text-blue-600"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </Button>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingBaseline({
                                    ...editingBaseline,
                                    projectFiles: editingBaseline.projectFiles.filter(f => f.id !== file.id)
                                  });
                                }}
                                className="p-1 hover:bg-gray-200 rounded-full"
                              >
                                <Cross2Icon className="w-4 h-4 text-gray-500" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 文件上传框 */}
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
                      onClick={() => document.getElementById('edit-file-upload').click()}
                    >
                      <div className="flex flex-col items-center gap-2">
                        <div className="text-center">
                          <svg
                            className="mx-auto h-12 w-12 text-gray-400"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <div className="text-sm text-gray-600">
                            点击或拖拽文件到此处上传
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            支持 Word、PDF、图片等格式
                          </p>
                        </div>
                      </div>
                    </div>
                    <input
                      id="edit-file-upload"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={handleEditFileChange}
                    />

                    {/* 新上传的文件列表 */}
                    {editingFiles.length > 0 && (
                      <div className="mt-4 space-y-2">
                        {editingFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center gap-2">
                              <FileTextIcon className="w-4 h-4 text-gray-400" />
                              <span className="font-medium">{file.name}</span>
                              <span className="text-sm text-gray-500">
                                ({formatFileSize(file.size)})
                              </span>
                            </div>
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingFiles(editingFiles.filter((_, i) => i !== index));
                              }}
                              className="p-1 hover:bg-gray-200 rounded-full"
                            >
                              <Cross2Icon className="w-4 h-4 text-gray-500" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={handleCloseEditModal}
                >
                  取消
                </Button>
                <Button
                  onClick={handleUpdateBaseline}
                  style={{ backgroundColor: '#0070f3', color: '#fff' }}
                >
                  {isLoading ? '保存中...' : '保存修改'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirmModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[400px]">
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-2">确定要删除吗？</h3>
                <p className="text-gray-500 text-sm">
                  {deleteType === 'baseline' && '删除后将无法恢复此基线。'}
                  {deleteType === 'component' && '确定要删除这个组件吗？'}
                  {deleteType === 'file' && '确定要删除这个文件吗？'}
                </p>
              </div>
              <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowDeleteConfirmModal(false);
                    setDeletingItem(null);
                  }}
                >
                  取消
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleConfirmDelete}
                  style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
                >
                  确定
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});