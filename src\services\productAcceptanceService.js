import axios from 'axios';

import { fetchData } from './fetch';


// 产品验收相关接口
export const productAcceptanceApi = {
  // 获取验收列表（分页）
  getAcceptancePage: (params) => {
    const queryParams = new URLSearchParams({
      projectId: params.projectId,
      name: params.name || '',
      startTime: params.startTime || '',
      endTime: params.endTime || '',
      page: params.page || 0,
      size: params.size || 10
    });
    return axios.get(`${fetchData["PROJECT_URL"]}/api/acceptances/page?${queryParams}`);
  },

  // 获取单个验收详情
  getAcceptanceDetail: (acceptanceId) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/acceptances/one/${acceptanceId}`),

  // 创建验收
  createAcceptance: (formData) => 
    axios.post(`${fetchData["PROJECT_URL"]}/api/acceptances/create`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 更新验收
  updateAcceptance: (acceptanceId, formData) => 
    axios.put(`${fetchData["PROJECT_URL"]}/api/acceptances/update/${acceptanceId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 删除验收
  deleteAcceptance: (acceptanceId) => 
    axios.delete(`${fetchData["PROJECT_URL"]}/api/acceptances/${acceptanceId}`)
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: () => 
    axios.get(`${fetchData["STAFF_URL"]}/api/projects/list`)
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName = 'acceptance') => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/file/preview`, {
      params: { fileName, bucketName }
    }),

  // 下载文件
  downloadFile: (fileName, bucketName = 'acceptance') => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/file/download`, {
      params: { fileName, bucketName },
      responseType: 'blob'
    })
};

// 测试计划相关接口
export const testPlanApi = {
  // 获取测试计划列表（分页）
  getTestPlanPage: (params) => {
    const queryParams = new URLSearchParams({
      projectId: params.projectId,
      page: params.page || 0,
      size: params.size || 10,
      name: params.name || '',
      status: params.status || '',
      creatorId: params.creatorId || ''
    });
    return axios.get(`${fetchData["PROJECT_URL"]}/api/test-plans/all?${queryParams}`);
  },

  // 获取单个测试计划详情
  getTestPlanDetail: (planId) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/test-plans/one/${planId}`),

  // 创建测试计划
  createTestPlan: (formData) => 
    axios.post(`${fetchData["PROJECT_URL"]}/api/test-plans/create`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 更新测试计划
  updateTestPlan: (planId, formData) => 
    axios.put(`${fetchData["PROJECT_URL"]}/api/test-plans/update/${planId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }),

  // 删除测试计划
  deleteTestPlan: (planId) => 
    axios.delete(`${fetchData["PROJECT_URL"]}/api/test-plans/${planId}`)
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    axios.get(`${fetchData["BASE_URL"]}/api/employees/list`)
}; 