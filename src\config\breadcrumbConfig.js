// 面包屑导航配置 - 与路由保持一致的结构
export const BreadCrumb = {
  "/": [
    { title: "Dashboard" },
    { title: "首页大屏" }
  ],
  "/project-management/create": [
    { title: "项目管理" },
    { title: "项目创建" }
  ],
  "/project-management/input": [
    { title: "项目管理" },
    { title: "项目输入" }
  ],
  "/project-management/output": [
    { title: "项目管理" },
    { title: "项目输出" }
  ],
  "/project-management/progress": [
    { title: "项目管理" },
    { title: "进度管理" }
  ],
  "/project-management/resource": [
    { title: "项目管理" },
    { title: "资源管理" }
  ],
  "/project-management/risk": [
    { title: "项目管理" },
    { title: "风险管理" }
  ],
  "/hr-management/organization": [
    { title: "人事管理" },
    { title: "组织架构" }
  ],
  "/hr-management/personnel": [
    { title: "人事管理" },
    { title: "人员管理" }
  ],
  "/dev-management/design-input": [
    { title: "研发管理" },
    { title: "设计输入" }
  ],
  "/dev-management/input-review": [
    { title: "研发管理" },
    { title: "输入评审" }
  ],
  "/dev-management/requirements": [
    { title: "研发管理" },
    { title: "需求分解" }
  ],
  "/dev-management/config-baseline": [
    { title: "研发管理" },
    { title: "配置基线" }
  ],
  "/dev-management/design-proposal": [
    { title: "研发管理" },
    { title: "方案设计" }
  ],
  "/dev-management/design-review": [
    { title: "研发管理" },
    { title: "方案评审" }
  ],
  "/dev-management/design-baseline": [
    { title: "研发管理" },
    { title: "设计基线" }
  ],
  "/dev-management/repository": [
    { title: "研发管理" },
    { title: "代码库" }
  ],
  "/dev-management/merge-requests": [
    { title: "研发管理" },
    { title: "合并请求" }
  ],
  "/dev-management/code-review": [
    { title: "研发管理" },
    { title: "代码评审" }
  ],
  "/dev-management/dev-baseline": [
    { title: "研发管理" },
    { title: "开发基线" }
  ],
  "/dev-management/integration-test": [
    { title: "研发管理" },
    { title: "集成测试" }
  ],
  "/dev-management/integration-baseline": [
    { title: "研发管理" },
    { title: "集成基线" }
  ],
  "/dev-management/test-plan": [
    { title: "研发管理" },
    { title: "测试计划" }
  ],
  "/dev-management/test-case": [
    { title: "研发管理" },
    { title: "测试用例" }
  ],
  "/dev-management/test-result": [
    { title: "研发管理" },
    { title: "测试结果" }
  ],
  "/dev-management/product-acceptance": [
    { title: "研发管理" },
    { title: "验收" }
  ],
  "/dev-management/product-delivery": [
    { title: "研发管理" },
    { title: "成果交付" }
  ],
  "/dev-management/project-closure": [
    { title: "研发管理" },
    { title: "项目结项" }
  ],
  "/finance/reports": [
    { title: "财务管理" },
    { title: "财务报表管理" }
  ],
  "/finance/accounting": [
    { title: "财务管理" },
    { title: "账务处理" }
  ],
  "/finance/budget": [
    { title: "财务管理" },
    { title: "预算与规划" }
  ],
  "/finance/income-expense": [
    { title: "财务管理" },
    { title: "收入与支出管理" }
  ],
  "/finance/assets": [
    { title: "财务管理" },
    { title: "资产管理" }
  ],
  "/finance/receivables-payables": [
    { title: "财务管理" },
    { title: "应收与应付管理" }
  ],
  "/finance/tax": [
    { title: "财务管理" },
    { title: "税务管理" }
  ],
  "/finance/funds": [
    { title: "财务管理" },
    { title: "资金管理" }
  ],
  "/finance/audit": [
    { title: "财务管理" },
    { title: "财务审计与合规" }
  ],
  "/finance/analysis": [
    { title: "财务管理" },
    { title: "财务分析与决策支持" }
  ],
  "/production/teams": [
    { title: "生产管理" },
    { title: "班组管理" }
  ],
  "/production/shifts": [
    { title: "生产管理" },
    { title: "班次管理" }
  ],
  "/production/workstations": [
    { title: "生产管理" },
    { title: "工作站" }
  ],
  "/production/workshops": [
    { title: "生产管理" },
    { title: "车间管理" }
  ],
  "/production/processes": [
    { title: "生产管理" },
    { title: "工序管理" }
  ],
  "/production/routes": [
    { title: "生产管理" },
    { title: "工艺路线" }
  ],
  "/production/bom": [
    { title: "生产管理" },
    { title: "产品BOM" }
  ],
  "/production/sales-orders": [
    { title: "生产管理" },
    { title: "销售订单" }
  ],
  "/production/plans": [
    { title: "生产管理" },
    { title: "生产计划" }
  ],
  "/production/work-orders": [
    { title: "生产管理" },
    { title: "产品工单" }
  ],
  "/production/tasks": [
    { title: "生产管理" },
    { title: "工单任务" }
  ],
  "/production/work-reports": [
    { title: "生产管理" },
    { title: "报工记录" }
  ],
  "/production/quality-check": [
    { title: "生产管理" },
    { title: "产出质检" }
  ],
  "/sales/orders": [
    { title: "销售管理" },
    { title: "销售订单" }
  ],
  "/sales/reports": [
    { title: "销售管理" },
    { title: "销售报表" }
  ],
  "/sales/customers": [
    { title: "销售管理" },
    { title: "客户管理" }
  ],
  "/sales/follow-up": [
    { title: "销售管理" },
    { title: "销售跟进" }
  ],
  // 质量管理
  "/quality/policy": [
    { title: "质量管理" },
    { title: "质量方针管理" }
  ],
  "/quality/planning": [
    { title: "质量管理" },
    { title: "质量计划制定" }
  ],
  "/quality/standards": [
    { title: "质量管理" },
    { title: "质量标准定义" }
  ],
  "/quality/material-inspection": [
    { title: "质量管理" },
    { title: "原材料检验" }
  ],
  "/quality/process-inspection": [
    { title: "质量管理" },
    { title: "生产过程检验" }
  ],
  "/quality/product-inspection": [
    { title: "质量管理" },
    { title: "成品检验" }
  ],
  "/quality/inspection-reports": [
    { title: "质量管理" },
    { title: "检验报告与数据记录" }
  ],
  "/quality/nonconforming-registration": [
    { title: "质量管理" },
    { title: "不合格品登记" }
  ],
  "/quality/nonconforming-isolation": [
    { title: "质量管理" },
    { title: "不合格品隔离" }
  ],
  "/quality/root-cause-analysis": [
    { title: "质量管理" },
    { title: "根因分析" }
  ],
  "/quality/corrective-preventive": [
    { title: "质量管理" },
    { title: "纠正与预防措施管理" }
  ],
  "/quality/internal-audit": [
    { title: "质量管理" },
    { title: "内部质量审核" }
  ],
  "/quality/external-audit": [
    { title: "质量管理" },
    { title: "外部质量审核" }
  ],
  "/quality/audit-reports": [
    { title: "质量管理" },
    { title: "审核报告生成与整改追踪" }
  ],
  "/quality/supplier-evaluation": [
    { title: "质量管理" },
    { title: "供应商评审与选择" }
  ],
  "/quality/supplier-audit": [
    { title: "质量管理" },
    { title: "供应商质量审核" }
  ],
  "/quality/supplier-issues": [
    { title: "质量管理" },
    { title: "供应商质量问题处理" }
  ],
  "/quality/supplier-performance": [
    { title: "质量管理" },
    { title: "供应商绩效跟踪与反馈" }
  ],
  "/quality/customer-requirements": [
    { title: "质量管理" },
    { title: "客户质量要求定义" }
  ],
  "/quality/customer-complaints": [
    { title: "质量管理" },
    { title: "客户投诉管理" }
  ],
  "/quality/customer-satisfaction": [
    { title: "质量管理" },
    { title: "客户满意度调查" }
  ],
  "/quality/customer-reports": [
    { title: "质量管理" },
    { title: "客户质量报告" }
  ],
  "/quality/trend-analysis": [
    { title: "质量管理" },
    { title: "质量趋势分析" }
  ],
  "/quality/report-generation": [
    { title: "质量管理" },
    { title: "质量报告生成" }
  ],
  "/quality/kpi-tracking": [
    { title: "质量管理" },
    { title: "质量KPI跟踪" }
  ],
  "/quality/statistical-analysis": [
    { title: "质量管理" },
    { title: "统计分析工具" }
  ],
  // 采购管理
  "/procurement/supplier-info": [
    { title: "采购管理" },
    { title: "供应商信息" }
  ],
  "/procurement/qualification-review": [
    { title: "采购管理" },
    { title: "资质审核" }
  ],
  "/procurement/evaluation-grading": [
    { title: "采购管理" },
    { title: "评估分级" }
  ],
  "/procurement/cooperation": [
    { title: "采购管理" },
    { title: "合作关系" }
  ],
  "/procurement/requirement-collection": [
    { title: "采购管理" },
    { title: "需求收集" }
  ],
  "/procurement/requirement-analysis": [
    { title: "采购管理" },
    { title: "需求分析" }
  ],
  "/procurement/purchase-application": [
    { title: "采购管理" },
    { title: "采购申请" }
  ],
  "/procurement/plan-formulation": [
    { title: "采购管理" },
    { title: "计划制定" }
  ],
  "/procurement/plan-approval": [
    { title: "采购管理" },
    { title: "计划审批" }
  ],
  "/procurement/plan-execution": [
    { title: "采购管理" },
    { title: "计划执行" }
  ],
  "/procurement/order-creation": [
    { title: "采购管理" },
    { title: "订单创建" }
  ],
  "/procurement/order-review": [
    { title: "采购管理" },
    { title: "订单审核" }
  ],
  "/procurement/order-tracking": [
    { title: "采购管理" },
    { title: "订单跟踪" }
  ],
  "/procurement/exception-handling": [
    { title: "采购管理" },
    { title: "异常处理" }
  ],
  "/procurement/contract-drafting": [
    { title: "采购管理" },
    { title: "合同起草" }
  ],
  "/procurement/contract-approval": [
    { title: "采购管理" },
    { title: "合同审批" }
  ],
  "/procurement/contract-execution": [
    { title: "采购管理" },
    { title: "合同执行" }
  ],
  "/procurement/payment-management": [
    { title: "采购管理" },
    { title: "付款管理" }
  ],
  "/procurement/inventory-monitoring": [
    { title: "采购管理" },
    { title: "库存监控" }
  ],
  "/procurement/inventory-warning": [
    { title: "采购管理" },
    { title: "库存预警" }
  ],
  "/procurement/inventory-analysis": [
    { title: "采购管理" },
    { title: "库存分析" }
  ],
  "/procurement/price-collection": [
    { title: "采购管理" },
    { title: "价格采集" }
  ],
  "/procurement/price-analysis": [
    { title: "采购管理" },
    { title: "价格分析" }
  ],
  "/procurement/price-strategy": [
    { title: "采购管理" },
    { title: "价格策略" }
  ],
  "/procurement/inspection-standards": [
    { title: "采购管理" },
    { title: "检验标准" }
  ],
  "/procurement/inspection-process": [
    { title: "采购管理" },
    { title: "检验流程" }
  ],
  "/procurement/nonconforming-handling": [
    { title: "采购管理" },
    { title: "不合格处理" }
  ],
  "/procurement/payment-application": [
    { title: "采购管理" },
    { title: "付款申请" }
  ],
  "/procurement/payment-review": [
    { title: "采购管理" },
    { title: "付款审核" }
  ],
  "/procurement/payment-records": [
    { title: "采购管理" },
    { title: "付款记录" }
  ],
  "/procurement/cost-analysis": [
    { title: "采购管理" },
    { title: "成本分析" }
  ],
  "/procurement/progress-reports": [
    { title: "采购管理" },
    { title: "进度报表" }
  ],
  "/procurement/performance-analysis": [
    { title: "采购管理" },
    { title: "绩效分析" }
  ],
  "/procurement/decision-support": [
    { title: "采购管理" },
    { title: "决策支持" }
  ],
  // 仓储管理
  "/warehouse/material-info": [
    { title: "仓储管理" },
    { title: "物料信息" }
  ],
  "/warehouse/material-classification": [
    { title: "仓储管理" },
    { title: "物料分类" }
  ],
  "/warehouse/material-coding": [
    { title: "仓储管理" },
    { title: "物料编码" }
  ],
  "/warehouse/specifications": [
    { title: "仓储管理" },
    { title: "规格型号" }
  ],
  "/warehouse/bom-creation": [
    { title: "仓储管理" },
    { title: "BOM创建" }
  ],
  "/warehouse/bom-maintenance": [
    { title: "仓储管理" },
    { title: "BOM维护" }
  ],
  "/warehouse/bom-version": [
    { title: "仓储管理" },
    { title: "BOM版本" }
  ],
  "/warehouse/bom-query": [
    { title: "仓储管理" },
    { title: "BOM查询" }
  ],
  "/warehouse/arrival-registration": [
    { title: "仓储管理" },
    { title: "到货登记" }
  ],
  "/warehouse/receipt-confirmation": [
    { title: "仓储管理" },
    { title: "收货确认" }
  ],
  "/warehouse/document-management": [
    { title: "仓储管理" },
    { title: "单据管理" }
  ],
  "/warehouse/inspection-standards": [
    { title: "仓储管理" },
    { title: "检验标准" }
  ],
  "/warehouse/inspection-records": [
    { title: "仓储管理" },
    { title: "检验记录" }
  ],
  "/warehouse/nonconforming-handling": [
    { title: "仓储管理" },
    { title: "不合格处理" }
  ],
  "/warehouse/return-application": [
    { title: "仓储管理" },
    { title: "退货申请" }
  ],
  "/warehouse/exchange-handling": [
    { title: "仓储管理" },
    { title: "换货处理" }
  ],
  "/warehouse/return-exchange-records": [
    { title: "仓储管理" },
    { title: "退换记录" }
  ],
  "/warehouse/material-preparation-plan": [
    { title: "仓储管理" },
    { title: "备料计划" }
  ],
  "/warehouse/material-preparation-order": [
    { title: "仓储管理" },
    { title: "备料单" }
  ],
  "/warehouse/material-preparation-progress": [
    { title: "仓储管理" },
    { title: "备料进度" }
  ],
  "/warehouse/material-requisition-application": [
    { title: "仓储管理" },
    { title: "领料申请" }
  ],
  "/warehouse/material-requisition-review": [
    { title: "仓储管理" },
    { title: "领料审核" }
  ],
  "/warehouse/material-requisition-records": [
    { title: "仓储管理" },
    { title: "领料记录" }
  ],
  "/warehouse/inventory-query": [
    { title: "仓储管理" },
    { title: "库存查询" }
  ],
  "/warehouse/inventory-warning": [
    { title: "仓储管理" },
    { title: "库存预警" }
  ],
  "/warehouse/inventory-transfer": [
    { title: "仓储管理" },
    { title: "库存调拨" }
  ],
  "/warehouse/inventory-freeze": [
    { title: "仓储管理" },
    { title: "库存冻结" }
  ],
  "/warehouse/stocktaking-plan": [
    { title: "仓储管理" },
    { title: "盘点计划" }
  ],
  "/warehouse/stocktaking-execution": [
    { title: "仓储管理" },
    { title: "盘点执行" }
  ],
  "/warehouse/variance-handling": [
    { title: "仓储管理" },
    { title: "差异处理" }
  ],
  "/warehouse/stocktaking-report": [
    { title: "仓储管理" },
    { title: "盘点报告" }
  ],
  "/warehouse/inbound-traceability": [
    { title: "仓储管理" },
    { title: "入库追溯" }
  ],
  "/warehouse/outbound-traceability": [
    { title: "仓储管理" },
    { title: "出库追溯" }
  ],
  "/warehouse/inventory-traceability": [
    { title: "仓储管理" },
    { title: "库存追溯" }
  ],
  "/warehouse/quality-traceability": [
    { title: "仓储管理" },
    { title: "质量追溯" }
  ],
  "/warehouse/inbound-reports": [
    { title: "仓储管理" },
    { title: "入库报表" }
  ],
  "/warehouse/outbound-reports": [
    { title: "仓储管理" },
    { title: "出库报表" }
  ],
  "/warehouse/inventory-reports": [
    { title: "仓储管理" },
    { title: "库存报表" }
  ],
  "/warehouse/turnover-analysis": [
    { title: "仓储管理" },
    { title: "周转分析" }
  ]
};
