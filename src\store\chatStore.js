import { makeAutoObservable } from 'mobx';

class ChatStore {
  isOpen = false;
  isMinimized = false;
  searchQuery = '';
  selectedDepartment = null;
  unreadMessages = 3;
  departments = [
    {
      id: 'tech',
      name: '技术部',
      employees: [
        { id: 'tech1', name: '张工', avatar: null, online: true },
        { id: 'tech2', name: '李工', avatar: null, online: false },
        { id: 'tech3', name: '王工', avatar: null, online: true }
      ]
    },
    {
      id: 'hr',
      name: '人事部',
      employees: [
        { id: 'hr1', name: '刘经理', avatar: null, online: true },
        { id: 'hr2', name: '陈专员', avatar: null, online: true }
      ]
    },
    {
      id: 'marketing',
      name: '市场部',
      employees: [
        { id: 'marketing1', name: '赵经理', avatar: null, online: false },
        { id: 'marketing2', name: '钱专员', avatar: null, online: true }
      ]
    }
  ];
  
  conversations = [
    {
      id: 'ai-assistant',
      name: 'AI助手',
      avatar: null,
      isAI: true,
      messages: [],
      lastMessage: null,
      timestamp: null,
      online: true
    }
  ];
  
  currentConversation = null;

  constructor() {
    makeAutoObservable(this);
    this.currentConversation = this.conversations[0];
  }

  setSearchQuery(query) {
    this.searchQuery = query;
  }

  setSelectedDepartment(departmentId) {
    this.selectedDepartment = departmentId;
  }

  getFilteredContacts() {
    let contacts = [...this.conversations];
    
    this.departments.forEach(dept => {
      contacts = contacts.concat(dept.employees.map(emp => ({
        ...emp,
        departmentName: dept.name
      })));
    });

    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      contacts = contacts.filter(contact => 
        contact.name.toLowerCase().includes(query) ||
        (contact.departmentName && contact.departmentName.toLowerCase().includes(query))
      );
    }

    if (this.selectedDepartment) {
      const department = this.departments.find(d => d.id === this.selectedDepartment);
      if (department) {
        const employeeIds = department.employees.map(e => e.id);
        contacts = contacts.filter(contact => 
          employeeIds.includes(contact.id) || contact.isAI
        );
      }
    }

    return contacts;
  }

  startConversation(contactId) {
    let contact;
    
    if (contactId === 'ai-assistant') {
      contact = this.conversations.find(c => c.id === 'ai-assistant');
    } else {
      for (const dept of this.departments) {
        const employee = dept.employees.find(emp => emp.id === contactId);
        if (employee) {
          contact = {
            ...employee,
            messages: [],
            lastMessage: null,
            timestamp: null
          };
          break;
        }
      }
    }

    if (contact) {
      const existingConversation = this.conversations.find(c => c.id === contactId);
      if (!existingConversation) {
        this.conversations.push(contact);
      }
      this.currentConversation = existingConversation || contact;
    }
  }

  toggleChat() {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.isMinimized = false;
    }
  }

  minimizeChat() {
    this.isMinimized = !this.isMinimized;
  }

  addMessage(message) {
    if (this.currentConversation) {
      this.currentConversation.messages.push(message);
      this.currentConversation.lastMessage = message;
      this.currentConversation.timestamp = new Date();
    }
  }

  markAllMessagesRead() {
    this.unreadMessages = 0;
  }
}

export const chatStore = new ChatStore();