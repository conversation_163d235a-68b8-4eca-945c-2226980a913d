import axios from 'axios';

import {fetchData} from './fetch'

// 设计基线相关接口
export const designBaselineApi = {
  // 获取分页基线列表
  getBaselinePage: (projectId, page, size, name) => {
    const params = new URLSearchParams({ projectId, page, size });
    if (name) {
      params.append('name', name);
    }
    return axios.get(`${fetchData["PROJECT_URL"]}/api/design-lines/page?${params}`);
  },

  // 获取单个基线详情
  getBaselineDetail: (baselineId) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/design-lines/one/${baselineId}`),

  // 创建基线
  createBaseline: (formData) => 
    axios.post(`${fetchData["PROJECT_URL"]}/api/design-lines/create`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    }),

  // 更新基线
  updateBaseline: (baselineId, formData) => 
    axios.put(`${fetchData["PROJECT_URL"]}/api/design-lines/update/${baselineId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    }),

  // 删除基线
  deleteBaseline: (baselineId) => 
    axios.delete(`${fetchData["PROJECT_URL"]}/api/design-lines/delete/${baselineId}`),

  // 获取项目列表
  getProjectList: () => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/projects/list`),

  // 获取员工列表
  getEmployeeList: () => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/employees/list`),
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName, bucketName) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/file/preview`, {
      params: { fileName, bucketName }
    }),

  // 下载文件
  downloadFile: (fileName, bucketName) => 
    axios.get(`${fetchData["PROJECT_URL"]}/api/file/download`, {
      params: { fileName, bucketName },
      responseType: 'blob'
    }),
}; 