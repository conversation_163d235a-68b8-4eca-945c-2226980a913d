import React from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from './ui/button';

export const MainContent = observer(() => {
  return (
    <div className="flex-1 p-6 overflow-y-auto scrollbar-none bg-gray-50">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold mb-2">项目管理</h1>
          <p className="text-gray-500">管理和跟踪所有项目的进展情况</p>
        </div>
        <Button className="bg-blue-500 hover:bg-blue-600 text-white">
          新建项目
        </Button>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-2">进行中项目</h3>
          <p className="text-3xl font-bold text-blue-500">24</p>
          <p className="text-sm text-gray-500 mt-2">较上月 +3</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-2">已完成项目</h3>
          <p className="text-3xl font-bold text-green-500">156</p>
          <p className="text-sm text-gray-500 mt-2">本年度累计</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-2">项目预算</h3>
          <p className="text-3xl font-bold text-purple-500">￥2.4M</p>
          <p className="text-sm text-gray-500 mt-2">剩余预算</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-semibold mb-2">风险项目</h3>
          <p className="text-3xl font-bold text-red-500">3</p>
          <p className="text-sm text-gray-500 mt-2">需要关注</p>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">项目列表</h3>
          <div className="flex gap-2">
            <Button variant="outline">筛选</Button>
            <Button variant="outline">排序</Button>
          </div>
        </div>
        
        <div className="space-y-4">
          {[
            { name: '企业门户网站重构', status: '进行中', progress: 75 },
            { name: 'APP用户体验优化', status: '规划中', progress: 30 },
            { name: '数据中台建设', status: '进行中', progress: 45 },
            { name: '智能客服系统', status: '已完成', progress: 100 },
            { name: '安全运维平台', status: '进行中', progress: 60 }
          ].map((project, index) => (
            <div key={index} className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg">
              <div className="flex-1">
                <div className="font-medium">{project.name}</div>
                <div className="text-sm text-gray-500">状态: {project.status}</div>
              </div>
              <div className="w-48 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full" 
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
              <div className="ml-4 text-sm text-gray-500">
                {project.progress}%
              </div>
              <Button variant="outline" className="ml-4">详情</Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});