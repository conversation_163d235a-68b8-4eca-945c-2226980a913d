import axios from 'axios';

import { fetchData } from './fetch';

// 添加员工概览数据缓存
let employeeOverviewCache = null;
let overviewCacheTimestamp = null;
const OVERVIEW_CACHE_DURATION = 2 * 60 * 1000; // 2分钟缓存

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: (organizationId, page, size, name) => {
    const params = new URLSearchParams({ page, size });
    if (name) {
      params.append('name', name);
    }
    return fetch(`${fetchData["BASE_URL"]}/api/employees/list/${organizationId}?${params}`).then(res => res.json());
  },

  // 获取员工概览数据（带缓存）
  getEmployeeOverview: async () => {
    const now = Date.now();

    // 如果缓存存在且未过期，直接返回缓存数据
    if (employeeOverviewCache && overviewCacheTimestamp && (now - overviewCacheTimestamp < OVERVIEW_CACHE_DURATION)) {
      console.log('使用缓存的员工概览数据');
      return employeeOverviewCache;
    }

    // 添加调用栈信息来追踪调用来源
    const stack = new Error().stack;
    console.log('从服务器获取员工概览数据');
    console.log('调用栈:', stack);
    const response = await fetch(`${fetchData["BASE_URL"]}/api/employees/overview`);
    const data = await response.json();

    // 更新缓存
    employeeOverviewCache = data;
    overviewCacheTimestamp = now;

    return data;
  },

  // 清除员工概览缓存
  clearOverviewCache: () => {
    employeeOverviewCache = null;
    overviewCacheTimestamp = null;
  },

  // 搜索员工
  searchEmployees: (keyword) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/search?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),

  // 混合搜索员工
  searchEmployeesMixed: (keyword) => 
    axios.get(`${fetchData["BASE_URL"]}/api/employees/search-mixed?keyword=${keyword}`),

  // 获取单个员工详情
  getEmployeeDetail: (employeeId) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/one/${employeeId}`).then(res => res.json()),

  // 创建员工
  createEmployee: (employeeData) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }, 
      body: JSON.stringify(employeeData)
    }).then(res => res.json()),

  // 更新员工信息
  updateEmployee: (employeeId, employeeData) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/update/${employeeId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(employeeData)
    }).then(res => res.json()),

  // 删除员工
  deleteEmployees: (employeeIds) => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(employeeIds)
    }).then(res => res.json()),
    
  // 上传SSH访问令牌
  uploadSshToken: (employeeId, accessToken) => 
    axios.post(`${fetchData["BASE_URL"]}/api/employees/upload/ssh`, {
      id: employeeId,
      accessToken: accessToken
    }),
}; 