import React from 'react';
import { useLocation } from 'react-router-dom';
import { Breadcrumb } from 'antd';
import { BreadCrumb } from '../config/breadcrumbConfig';

/**
 * 面包屑导航组件
 * 根据当前路由自动显示对应的面包屑导航
 */
export const BreadcrumbNav = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  
  // 获取当前路径对应的面包屑配置
  const breadcrumbItems = BreadCrumb[currentPath] || [
    { title: "未知页面" }
  ];

  return (
    <Breadcrumb
      style={{ margin: '16px 0' }}
      items={breadcrumbItems}
    />
  );
};

export default BreadcrumbNav;
