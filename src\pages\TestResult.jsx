/**
 * 测试结果管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 测试结果的增删改查
 * 3. 文件上传和管理
 * 4. 分页和筛选功能
 *
 * 组件结构：
 * - 左侧：项目列表侧边栏
 * - 右侧：测试结果管理主界面
 * - 模态框：新建、编辑、查看、删除确认
 */

import { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { testResultApi, projectApi, fileApi, employeeApi } from '../services/testResultService';

// 错误消息组件
const ErrorMessage = ({ message }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
    <Cross2Icon className="w-4 h-4 text-red-500" />
    <div className="text-sm text-red-800">{message}</div>
  </div>
);

// 测试级别选项
const testLevelOptions = [
  { value: 0, label: '低' },
  { value: 1, label: '中' },
  { value: 2, label: '高' }
];

// 测试优先级选项
const testPriorityOptions = [
  { value: 0, label: '低' },
  { value: 1, label: '中' },
  { value: 2, label: '高' }
];

// 测试状态选项
const testStatusOptions = [
  { value: 0, label: '已解决' },
  { value: 1, label: '未解决' },
  { value: 2, label: '不予解决' },
  { value: 4, label: '延后解决' }
];

// 项目状态映射
const projectStatusMap = [
  { value: 0, label: '未开始', color: 'bg-gray-100 text-gray-600' },
  { value: 1, label: '进行中', color: 'bg-blue-100 text-blue-600' },
  { value: 2, label: '已结束', color: 'bg-green-100 text-green-600' }
];

export const TestResult = observer(() => {
  // 项目相关状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');

  // 测试结果相关状态
  const [testCases, setTestCases] = useState([]);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);

  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState('');
  const [searchLevel, setSearchLevel] = useState('');
  const [searchPriority, setSearchPriority] = useState('');
  const [searchStatus, setSearchStatus] = useState('');
  const [searchCreator, setSearchCreator] = useState('');

  // 模态框状态
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // 表单数据状态
  const [newTestCase, setNewTestCase] = useState({
    projectId: '',
    projectName: '',
    testName: '',
    tester: '',
    responsibleId: '',
    testLevel: '',
    testPriority: '',
    testStatus: '',
    description: '',
    files: []
  });
  const [editingPlan, setEditingPlan] = useState(null);
  const [viewingPlan, setViewingPlan] = useState(null);
  const [deletingRequirement, setDeletingRequirement] = useState(null);

  // 其他状态
  const [employees, setEmployees] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [planErrors, setPlanErrors] = useState({});
  const [errorMessage, setErrorMessage] = useState('');

  // 下拉框状态
  const [isTestLevelDropdownOpen, setIsTestLevelDropdownOpen] = useState(false);
  const [isTesterDropdownOpen, setIsTesterDropdownOpen] = useState(false);
  const [isResponsibleDropdownOpen, setIsResponsibleDropdownOpen] = useState(false);
  const [isTestPriorityDropdownOpen, setIsTestPriorityDropdownOpen] = useState(false);
  const [isTestStatusDropdownOpen, setIsTestStatusDropdownOpen] = useState(false);

  // 下拉框引用
  const testLevelRef = useRef(null);
  const testerRef = useRef(null);
  const responsibleRef = useRef(null);
  const testPriorityRef = useRef(null);
  const testStatusRef = useRef(null);

  // 错误处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 2000);
  };

  // 获取项目列表
  const fetchProjects = async (searchName = '') => {
    try {
      const data = await projectApi.getProjectList({ name: searchName });
      setProjects(data);

      // 自动选择第一个项目
      if (data.length > 0 && !selectedProject) {
        handleProjectSelect(data[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      handleError('获取项目列表失败');
    }
  };

  // 项目搜索处理
  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  // 选择项目处理
  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    try {
      const data = await testResultApi.getTestResultPage({
        projectId: project.id,
        page: 0,
        size: 10
      });
      setTestCases(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
      setPage(0);
    } catch (error) {
      console.error('获取测试结果失败:', error);
      handleError('获取测试结果失败');
    }
  };

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表失败:', error);
      handleError('获取员工列表失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchEmployees();
  }, []);

  // 更新测试结果
  const handleUpdatePlan = async () => {
    try {
      const testResult = {
        id: editingPlan.id,
        title: editingPlan.title,
        description: editingPlan.description || '',
        level: parseInt(editingPlan.level),
        priority: parseInt(editingPlan.priority),
        status: parseInt(editingPlan.status),
        creatorId: parseInt(editingPlan.creatorId),
        creatorName: editingPlan.creatorName,
        responseId: parseInt(editingPlan.responsibleId),
        responseName: editingPlan.responseName,
        createdTime: editingPlan.createdTime,
        projectId: selectedProject.id,
        fileIds: editingPlan.fileIds || [],
        projectFiles: editingPlan.projectFiles || []
      };

      const formData = new FormData();
      formData.append('testResult', new Blob([JSON.stringify(testResult)], {
        type: 'application/json'
      }));

      uploadedFiles.forEach(file => {
        formData.append('files', file);
      });

      await testResultApi.updateTestResult(editingPlan.id, formData);

      setShowEditModal(false);
      setEditingPlan(null);
      setUploadedFiles([]);
      fetchTestCases();
    } catch (error) {
      console.error('更新测试结果失败:', error);
      handleError('更新测试结果失败');
    }
  };

  // 搜索测试结果
  const handleSearch = async () => {
    try {
      const params = {
        projectId: selectedProject?.id,
        page: 0,
        size: 10
      };

      // 添加搜索条件
      if (searchQuery) params.title = searchQuery;
      if (searchLevel) params.level = searchLevel;
      if (searchPriority) params.priority = searchPriority;
      if (searchStatus) params.status = searchStatus;
      if (searchCreator) params.creatorId = searchCreator;

      const data = await testResultApi.getTestResultPage(params);
      setTestCases(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
      setPage(0);
    } catch (error) {
      console.error('搜索测试结果失败:', error);
      handleError('搜索测试结果失败');
    }
  };

  // 重置搜索条件
  const handleReset = () => {
    setSearchQuery('');
    setSearchLevel('');
    setSearchPriority('');
    setSearchStatus('');
    setSearchCreator('');

    // 重新获取数据
    setTimeout(() => {
      fetchTestCases(selectedProject?.id, 0);
    }, 0);
  };

  // 文件上传处理
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  // 删除文件
  const handleFileDelete = (index) => {
    setUploadedFiles(uploadedFiles.filter((_, i) => i !== index));
  };

  // 拖拽处理
  const handleDragOver = (e) => e.preventDefault();
  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  // 打开新建模态框
  const openNewModal = () => {
    if (!selectedProject) {
      handleError('请先选择一个项目');
      return;
    }

    setNewTestCase({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      testName: '',
      tester: '',
      responsibleId: '',
      testLevel: '',
      testPriority: '',
      testStatus: '',
      description: '',
      files: []
    });
    setUploadedFiles([]);
    setShowNewPlanModal(true);
  };

  // 创建测试结果
  const handleCreateTestCase = async () => {
    // 表单验证
    const errors = {};
    if (!newTestCase.testName) errors.testName = '请输入测试名称';
    if (!newTestCase.tester) errors.tester = '请选择创建人';
    if (!newTestCase.responsibleId) errors.responsibleId = '请选择负责人';
    if (!newTestCase.testLevel) errors.testLevel = '请选择测试级别';
    if (!newTestCase.testPriority) errors.testPriority = '请选择测试优先级';
    if (!newTestCase.testStatus) errors.testStatus = '请选择测试状态';

    setPlanErrors(errors);
    if (Object.keys(errors).length > 0) return;

    try {
      // 构建测试结果数据
      const testResult = {
        number: `TEST-${Date.now()}`,
        title: newTestCase.testName,
        description: newTestCase.description || '',
        level: parseInt(newTestCase.testLevel),
        priority: parseInt(newTestCase.testPriority),
        status: parseInt(newTestCase.testStatus),
        creatorId: parseInt(newTestCase.tester),
        creatorName: employees.find(emp => emp.id.toString() === newTestCase.tester)?.name,
        projectId: selectedProject.id,
        responseId: parseInt(newTestCase.responsibleId),
        responseName: employees.find(emp => emp.id.toString() === newTestCase.responsibleId)?.name,
        fileIds: [],
        projectFiles: uploadedFiles.map(file => ({
          id: 0,
          name: file.name,
          path: '',
          type: 0,
          size: file.size,
          otherId: 0,
          uploadTime: new Date().toISOString(),
          uploaderId: parseInt(newTestCase.tester),
          description: '',
          module: ''
        }))
      };

      // 构建表单数据
      const formData = new FormData();
      formData.append('testResult', new Blob([JSON.stringify(testResult)], {
        type: 'application/json'
      }));
      uploadedFiles.forEach(file => formData.append('files', file));

      await testResultApi.createTestResult(formData);

      // 重置表单并关闭模态框
      setShowNewPlanModal(false);
      setNewTestCase({
        projectId: '',
        projectName: '',
        testName: '',
        tester: '',
        responsibleId: '',
        testLevel: '',
        testPriority: '',
        testStatus: '',
        description: '',
        files: []
      });
      setUploadedFiles([]);
      fetchTestCases();
    } catch (error) {
      console.error('创建测试结果失败:', error);
      handleError('创建测试结果失败');
    }
  };

  // 获取测试结果列表
  const fetchTestCases = async (projectId, pageNum = 0) => {
    try {
      const params = {
        projectId: projectId || selectedProject?.id,
        page: pageNum,
        size: 10
      };

      // 添加搜索条件
      if (searchLevel) params.level = searchLevel;
      if (searchPriority) params.priority = searchPriority;
      if (searchStatus) params.status = searchStatus;
      if (searchQuery) params.title = searchQuery;

      const data = await testResultApi.getTestResultPage(params);
      setTestCases(data.content || []);
      setTotalPages(data.totalPages || 1);
      setTotalElements(data.totalElements || 0);
    } catch (error) {
      console.error('获取测试结果失败:', error);
      handleError('获取测试结果失败');
    }
  };

  // 分页处理
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchTestCases(selectedProject?.id, newPage);
  };

  // 编辑测试结果
  const handleEditTestCase = async (id) => {
    try {
      const data = await testResultApi.getTestResultDetail(id);
      setEditingPlan({
        id: data.id,
        title: data.title,
        creatorId: data.creatorId?.toString(),
        creatorName: data.creatorName,
        responsibleId: data.responseId?.toString(),
        responseName: data.responseName,
        level: data.level?.toString(),
        priority: data.priority?.toString(),
        status: data.status?.toString(),
        description: data.description || '',
        createdTime: data.createdTime,
        projectId: data.projectId,
        fileIds: data.fileIds || [],
        projectFiles: data.projectFiles || [],
      });
      setShowEditModal(true);
    } catch (error) {
      console.error('获取测试结果详情失败:', error);
      handleError('获取测试结果详情失败');
    }
  };

  // 查看测试结果详情
  const fetchTestCaseDetail = async (id) => {
    try {
      const data = await testResultApi.getTestResultDetail(id);
      setViewingPlan(data);
      setShowViewModal(true);
    } catch (error) {
      console.error('获取测试结果详情失败:', error);
      handleError('获取测试结果详情失败');
    }
  };

  // 文件预览
  const handleFilePreview = async (file) => {
    try {
      const url = await fileApi.previewFile({
        fileName: file.name,
        bucketName: 'testresult'
      });
      window.open(url, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败');
    }
  };

  // 文件下载
  const handleFileDownload = async (file) => {
    try {
      const response = await fileApi.downloadFile({
        fileName: file.name,
        bucketName: 'testresult'
      });
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('文件下载失败:', error);
      handleError('文件下载失败');
    }
  };

  // 移除文件
  const handleRemoveFile = (fileIndex, isExisting = false) => {
    if (isExisting) {
      setEditingPlan(prev => ({
        ...prev,
        projectFiles: prev.projectFiles.filter((_, index) => index !== fileIndex)
      }));
    } else {
      setUploadedFiles(prev => prev.filter((_, index) => index !== fileIndex));
    }
  };

  // 删除测试结果
  const handleDeleteTestCase = async () => {
    try {
      await testResultApi.deleteTestResult(deletingRequirement.id);
      setShowDeleteModal(false);
      setDeletingRequirement(null);
      fetchTestCases();
    } catch (error) {
      console.error('删除测试结果失败:', error);
      handleError('删除测试结果失败');
    }
  };

  // 处理下拉框点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event) => {
      const refs = [
        { ref: testLevelRef, setter: setIsTestLevelDropdownOpen },
        { ref: testerRef, setter: setIsTesterDropdownOpen },
        { ref: responsibleRef, setter: setIsResponsibleDropdownOpen },
        { ref: testPriorityRef, setter: setIsTestPriorityDropdownOpen },
        { ref: testStatusRef, setter: setIsTestStatusDropdownOpen }
      ];

      refs.forEach(({ ref, setter }) => {
        if (ref.current && !ref.current.contains(event.target)) {
          setter(false);
        }
      });
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setErrorMessage('')}
        />
      )}

      {/* 项目列表侧边栏 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-[calc(100vh-100px)]">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>

        {/* 项目列表 */}
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              onClick={() => handleProjectSelect(project)}
              className={`p-4 cursor-pointer hover:bg-blue-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''}`}
            >
              <div className="font-medium">{project.name}</div>
              <div className={`mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs ${projectStatusMap[project.status]?.color}`}>
                {projectStatusMap[project.status]?.label || '未知状态'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 主内容区域 */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 页面标题 */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">测试结果管理</p>
            </div>
          </div>

          {/* 搜索和操作栏 */}
          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 items-end">
              {/* 搜索框 */}
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="搜索结果名称"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              {/* 筛选条件 */}
              <select
                value={searchLevel}
                onChange={(e) => setSearchLevel(e.target.value)}
                className="w-[120px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">测试级别</option>
                {testLevelOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchPriority}
                onChange={(e) => setSearchPriority(e.target.value)}
                className="w-[120px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">优先级</option>
                {testPriorityOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchStatus}
                onChange={(e) => setSearchStatus(e.target.value)}
                className="w-[120px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">状态</option>
                {testStatusOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>

              <select
                value={searchCreator}
                onChange={(e) => setSearchCreator(e.target.value)}
                className="w-[120px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">创建人</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>{employee.name}</option>
                ))}
              </select>

              {/* 操作按钮 */}
              <div className="flex gap-2 ml-auto">
                <Button onClick={handleSearch} className="bg-blue-500 hover:bg-blue-600 text-white">
                  搜索
                </Button>
                <Button variant="outline" onClick={handleReset}>
                  重置
                </Button>
                <Button onClick={openNewModal} className="bg-green-500 hover:bg-green-600 text-white">
                  <PlusIcon className="w-4 h-4 mr-1" />
                  添加测试结果
                </Button>
              </div>
            </div>
          </div>

          {/* 测试结果表格 */}
          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">测试名称</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建人</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">级别</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">优先级</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">状态</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">描述</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建时间</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody>
                {testCases.map(testCase => (
                  <tr key={testCase.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.title}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">{testCase.creatorName}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testLevelOptions.find(opt => opt.value === testCase.level)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testPriorityOptions.find(opt => opt.value === testCase.priority)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testStatusOptions.find(opt => opt.value === testCase.status)?.label || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500 max-w-xs truncate">
                      {testCase.description || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testCase.createdTime ? new Date(testCase.createdTime).toLocaleString() : '-'}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => fetchTestCaseDetail(testCase.id)}
                          className="text-gray-400 hover:text-blue-600 p-1"
                          title="查看详情"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditTestCase(testCase.id)}
                          className="text-gray-400 hover:text-blue-600 p-1"
                          title="编辑"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setDeletingRequirement(testCase);
                            setShowDeleteModal(true);
                          }}
                          className="text-gray-400 hover:text-red-500 p-1"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* 分页组件 */}
            <div className="flex items-center justify-between px-4 py-3 border-t bg-gray-50">
              <div className="text-sm text-gray-500">
                共 {totalElements} 条记录，第 {page + 1} / {totalPages} 页
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.max(0, page - 1))}
                  disabled={page === 0}
                  size="sm"
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.min(totalPages - 1, page + 1))}
                  disabled={page >= totalPages - 1}
                  size="sm"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* 空状态显示 */
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">暂无项目</h3>
            <p className="text-gray-500">请选择左侧的项目查看测试结果</p>
          </div>
        </div>
      )}

      {showNewPlanModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加结果</h3>
              <button
                onClick={() => setShowNewPlanModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试名 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newTestCase.testName}
                      onChange={(e) => setNewTestCase({ ...newTestCase, testName: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入测试名称"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试级别 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testLevelRef}>
                      <div
                        onClick={() => setIsTestLevelDropdownOpen(!isTestLevelDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.testLevel && newTestCase.testLevel !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTestCase.testLevel !== undefined && newTestCase.testLevel !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.testLevel !== undefined && newTestCase.testLevel !== '' ? testLevelOptions.find(t => t.value === Number(newTestCase.testLevel))?.label : '请选择测试级别'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestLevelDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestLevelDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testLevelOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, testLevel: option.value });
                                  setPlanErrors({ ...planErrors, testLevel: false });
                                  setIsTestLevelDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(newTestCase.testLevel) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.testLevel && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试级别
                      </p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testerRef}>
                      <div
                        onClick={() => setIsTesterDropdownOpen(!isTesterDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.tester ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTestCase.tester ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.tester ? employees.find(emp => emp.id.toString() === newTestCase.tester)?.name : '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTesterDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTesterDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, tester: employee.id.toString() });
                                  setPlanErrors({ ...planErrors, tester: false });
                                  setIsTesterDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newTestCase.tester === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.tester && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择创建人
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      负责人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={responsibleRef}>
                      <div
                        onClick={() => setIsResponsibleDropdownOpen(!isResponsibleDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.responsibleId ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTestCase.responsibleId ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.responsibleId ? employees.find(emp => emp.id.toString() === newTestCase.responsibleId)?.name : '请选择负责人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isResponsibleDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isResponsibleDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, responsibleId: employee.id.toString() });
                                  setPlanErrors({ ...planErrors, responsibleId: false });
                                  setIsResponsibleDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newTestCase.responsibleId === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.responsibleId && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择负责人
                      </p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试优先级 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testPriorityRef}>
                      <div
                        onClick={() => setIsTestPriorityDropdownOpen(!isTestPriorityDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.testPriority && newTestCase.testPriority !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTestCase.testPriority !== undefined && newTestCase.testPriority !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.testPriority !== undefined && newTestCase.testPriority !== '' ? testPriorityOptions.find(t => t.value === Number(newTestCase.testPriority))?.label : '请选择测试优先级'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestPriorityDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestPriorityDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testPriorityOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, testPriority: option.value });
                                  setPlanErrors({ ...planErrors, testPriority: false });
                                  setIsTestPriorityDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(newTestCase.testPriority) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.testPriority && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试优先级
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试状态 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testStatusRef}>
                      <div
                        onClick={() => setIsTestStatusDropdownOpen(!isTestStatusDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.testStatus && newTestCase.testStatus !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newTestCase.testStatus !== undefined && newTestCase.testStatus !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.testStatus !== undefined && newTestCase.testStatus !== '' ? testStatusOptions.find(t => t.value === Number(newTestCase.testStatus))?.label : '请选择测试状态'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestStatusDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestStatusDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testStatusOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, testStatus: option.value });
                                  setPlanErrors({ ...planErrors, testStatus: false });
                                  setIsTestStatusDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(newTestCase.testStatus) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.testStatus && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试状态
                      </p>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <textarea
                    value={newTestCase.description}
                    onChange={(e) => setNewTestCase({ ...newTestCase, description: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试描述"
                    rows={2}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <div
                    className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 min-h-[120px] flex flex-col items-center justify-center"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={() => document.getElementById('file-upload').click()}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-600 justify-center">
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          multiple
                          onChange={handleFileUpload}
                        />
                        <span className="text-blue-600 hover:text-blue-500">上传文件</span>
                        <p className="pl-1">或拖拽文件到这里</p>
                      </div>
                      <p className="text-xs text-gray-500">支持任意文件格式</p>
                    </div>
                  </div>
                  {/* 已上传文件列表 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">{file.name}</span>
                          <button
                            onClick={() => handleFileDelete(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewPlanModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateTestCase}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看测试结果</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <div className="text-gray-900">{selectedProject?.name}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试名
                    </label>
                    <div className="text-gray-900">{viewingPlan.title}</div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="text-gray-900">{viewingPlan.creatorName}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      负责人
                    </label>
                    <div className="text-gray-900">{viewingPlan.responseName}</div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试级别
                    </label>
                    <div className="text-gray-900">
                      {testLevelOptions.find(opt => opt.value === viewingPlan.level)?.label || '-'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试优先级
                    </label>
                    <div className="text-gray-900">
                      {testPriorityOptions.find(opt => opt.value === viewingPlan.priority)?.label || '-'}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试状态
                    </label>
                    <div className="text-gray-900">
                      {testStatusOptions.find(opt => opt.value === viewingPlan.status)?.label || '-'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建时间
                    </label>
                    <div className="text-gray-900">
                      {new Date(viewingPlan.createdTime).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                      }).replace(/\//g, '-')}
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <div className="text-gray-900">{viewingPlan.description || '-'}</div>
                </div>
                {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <div className="space-y-2">
                      {viewingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg hover:bg-gray-100"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleFileDownload(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="下载"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑测试结果</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试名 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPlan.title}
                      onChange={(e) => setEditingPlan({ ...editingPlan, title: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入测试名称"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试级别 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testLevelRef}>
                      <div
                        onClick={() => setIsTestLevelDropdownOpen(!isTestLevelDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.level && editingPlan.level !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.level !== undefined && editingPlan.level !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.level !== undefined && editingPlan.level !== '' ? testLevelOptions.find(t => t.value === Number(editingPlan.level))?.label : '请选择测试级别'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestLevelDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestLevelDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testLevelOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, level: option.value });
                                  setPlanErrors({ ...planErrors, level: false });
                                  setIsTestLevelDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(editingPlan.level) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.level && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试级别
                      </p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testerRef}>
                      <div
                        onClick={() => setIsTesterDropdownOpen(!isTesterDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.creatorId ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.creatorId ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.creatorId ? employees.find(emp => emp.id.toString() === editingPlan.creatorId)?.name : '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTesterDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTesterDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingPlan({
                                    ...editingPlan,
                                    creatorId: employee.id.toString(),
                                    creatorName: employee.name
                                  });
                                  setPlanErrors({ ...planErrors, creatorId: false });
                                  setIsTesterDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.creatorId === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.creatorId && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择创建人
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      负责人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={responsibleRef}>
                      <div
                        onClick={() => setIsResponsibleDropdownOpen(!isResponsibleDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.responsibleId ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.responsibleId ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.responsibleId ? employees.find(emp => emp.id.toString() === editingPlan.responsibleId)?.name : '请选择负责人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isResponsibleDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isResponsibleDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingPlan({
                                    ...editingPlan,
                                    responsibleId: employee.id.toString(),
                                    responseName: employee.name
                                  });
                                  setPlanErrors({ ...planErrors, responsibleId: false });
                                  setIsResponsibleDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.responsibleId === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.responsibleId && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择负责人
                      </p>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试优先级 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testPriorityRef}>
                      <div
                        onClick={() => setIsTestPriorityDropdownOpen(!isTestPriorityDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.priority && editingPlan.priority !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.priority !== undefined && editingPlan.priority !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.priority !== undefined && editingPlan.priority !== '' ? testPriorityOptions.find(t => t.value === Number(editingPlan.priority))?.label : '请选择测试优先级'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestPriorityDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestPriorityDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testPriorityOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, priority: option.value });
                                  setPlanErrors({ ...planErrors, priority: false });
                                  setIsTestPriorityDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(editingPlan.priority) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.priority && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试优先级
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      测试状态 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={testStatusRef}>
                      <div
                        onClick={() => setIsTestStatusDropdownOpen(!isTestStatusDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.status && editingPlan.status !== 0 ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingPlan.status !== undefined && editingPlan.status !== '' ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.status !== undefined && editingPlan.status !== '' ? testStatusOptions.find(t => t.value === Number(editingPlan.status))?.label : '请选择测试状态'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTestStatusDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTestStatusDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {testStatusOptions.map(option => (
                              <div
                                key={option.value}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, status: option.value });
                                  setPlanErrors({ ...planErrors, status: false });
                                  setIsTestStatusDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  Number(editingPlan.status) === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {option.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {planErrors.status && (
                      <p className="text-red-500 text-xs mt-1">
                        请选择测试状态
                      </p>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    测试描述
                  </label>
                  <textarea
                    value={editingPlan.description}
                    onChange={(e) => setEditingPlan({ ...editingPlan, description: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入测试描述"
                    rows={2}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  {/* 显示已有的文件 */}
                  {editingPlan.projectFiles && editingPlan.projectFiles.length > 0 && (
                    <div className="mb-2 space-y-2">
                      {editingPlan.projectFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleRemoveFile(index, true)}
                              className="text-red-500 hover:text-red-700"
                              title="删除"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 显示新上传的文件 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mb-2 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <button
                            onClick={() => handleRemoveFile(index)}
                            className="text-red-500 hover:text-red-700"
                            title="删除"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 文件上传框 */}
                  <div
                    className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50 min-h-[120px] flex flex-col items-center justify-center"
                    onClick={() => document.getElementById('fileInput').click()}
                  >
                    <input
                      id="fileInput"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => {
                        const files = Array.from(e.target.files || []);
                        setUploadedFiles(prev => [...prev, ...files]);
                        e.target.value = '';
                      }}
                    />
                    <FileTextIcon className="w-6 h-6 text-gray-400 mb-2" />
                    <div className="text-gray-500">
                      点击上传文件 或 拖拽文件到此处
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                }}
              >
                取消
              </Button>
              <Button onClick={handleUpdatePlan}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">确认删除</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-600">
                确定要删除测试结果 &ldquo;{deletingRequirement.title}&rdquo; 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingRequirement(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={handleDeleteTestCase}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
});