import axios from 'axios';

import { fetchData } from './fetch';

// 登录相关接口
export const loginApi = {
  // 用户登录
  login: (loginData) => 
    axios.post(`${fetchData["BASE_URL"]}/api/employees/login`, loginData),

  // 忘记密码
  forgotPassword: (email) => 
    axios.post(`${fetchData["BASE_URL"]}/api/employees/forgot-password`, { email }),

  // 重置密码
  resetPassword: (resetData) => 
    axios.post(`${fetchData["BASE_URL"]}/api/employees/reset-password`, resetData),

  // 验证token
  validateToken: (token) => 
    axios.post(`${fetchData["BASE_URL"]}/api/employees/validate-token`, { token })
}; 