import axios from 'axios';

import { fetchData } from './fetch';

// 项目结项相关接口
export const projectClosureApi = {
  // 获取项目列表
  getProjectList: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/by-client/page?${queryParams}`).then(res => res.json());
  },

  // 获取员工列表
  getEmployeesList: () => {
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json())
  },

  // 获取结项列表
  getClosureList: async (params) => {
    const queryParams = new URLSearchParams(params);
    const response = await fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/list?${queryParams}`);
    if (!response.ok) {
      throw new Error(`获取项目结项列表失败: ${response.status}`);
    }
    const data = await response.json();
    return data;
  },

  // 重置结项列表
  resetClosureList: async (params) => {
    const queryParams = new URLSearchParams(params);
    const response = await fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/list?${queryParams}`);
    if (!response.ok) {
      throw new Error(`重置失败: ${response.status}`);
    }
    const data = await response.json();
    return data;
  },

  // 获取单个结项详情
  getClosureDetail: (closureId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/one/${closureId}`).then(res => res.json()),

  // 创建结项
  createClosure: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新结项
  updateClosure: (closureId, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/update/${closureId}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除结项
  deleteClosure: (closureId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/${closureId}`, {
      method: 'DELETE'
    }).then(res => res.json()),

  // 获取文件预览URL
  getFilePreviewUrl: (fileId) => `${fetchData["PROJECT_URL"]}/api/project/closure/file/${fileId}`,

  // 文件相关接口
  fileApi: {
    // 预览文件
    previewFile: (fileName) => 
      fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?fileName=${encodeURIComponent(fileName)}&bucketName=projectclosure`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      }).then(res => res.text()),

    // 下载文件
    downloadFile: (fileName) => 
      fetch(`${fetchData["PROJECT_URL"]}/api/file/download?fileName=${encodeURIComponent(fileName)}&bucketName=projectclosure`, {
        method: 'GET'
      }),

    // 删除文件
    deleteFile: (fileId) => 
      fetch(`${fetchData["PROJECT_URL"]}/api/project/closure/file/${fileId}`, {
        method: 'DELETE'
      }).then(res => res.json()),
  }
}; 