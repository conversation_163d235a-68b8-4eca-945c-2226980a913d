import axios from 'axios';

import { fetchData } from './fetch';


const STAFF_URL = 'http://172.27.1.153:8081';

// 测试结果相关接口
export const testResultApi = {
  // 获取测试结果分页列表
  getTestResultPage: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/test-results/page?${queryParams}`).then(res => res.json());
  },

  // 获取单个测试结果详情
  getTestResultDetail: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-results/one/${id}`).then(res => res.json()),

  // 创建测试结果
  createTestResult: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-results/create`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 更新测试结果
  updateTestResult: (id, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-results/update/${id}`, {
      method: 'POST',
      body: formData
    }).then(res => res.json()),

  // 删除测试结果
  deleteTestResult: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/test-results/${id}`, {
      method: 'DELETE'
    }).then(res => res.json()),
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${queryParams}`).then(res => res.json());
  }
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${queryParams}`).then(res => res.text());
  },

  // 下载文件
  downloadFile: (params) => {
    const queryParams = new URLSearchParams(params);
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${queryParams}`);
  }
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json())
}; 