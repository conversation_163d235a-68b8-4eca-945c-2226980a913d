/**
 * 测试用例管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 测试用例的增删改查
 * 3. 文件上传和下载
 * 4. 模板管理
 *
 * 简化内容：
 * - 移除了未使用的状态变量和mock数据
 * - 合并了重复的函数逻辑
 * - 统一了错误处理和消息提示
 * - 简化了文件操作函数
 * - 优化了表单验证逻辑
 */

import { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon,
  CheckCircledIcon,
  CrossCircledIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { testCaseApi, fileApi } from '../services/testCaseService';
import { projectApi, employeeApi } from '../services/projectService';

// 项目状态映射配置
const PROJECT_STATUS_MAP = {
  0: { label: '未开始', color: 'bg-gray-100 text-gray-600' },
  1: { label: '进行中', color: 'bg-blue-100 text-blue-600' },
  2: { label: '已结束', color: 'bg-green-100 text-green-600' }
};

// 错误消息组件
const ErrorMessage = ({ message }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-red-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
    <Cross2Icon className="w-4 h-4 text-red-500" />
    <div className="text-sm text-red-800">{message}</div>
  </div>
);

// 成功消息组件
const SuccessMessage = ({ message }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 bg-green-50 rounded-lg p-4 flex items-center gap-2 z-[100]">
    <CheckCircledIcon className="w-4 h-4 text-green-500" />
    <div className="text-sm text-green-800">{message}</div>
  </div>
);


export const TestCase = observer(() => {
  // 项目相关状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');

  // 测试用例相关状态
  const [testCases, setTestCases] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchCreator, setSearchCreator] = useState('');
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [totalElements, setTotalElements] = useState(0);

  // 员工数据
  const [employees, setEmployees] = useState([]);

  // 弹窗状态
  const [showNewPlanModal, setShowNewPlanModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showTemplateUploadModal, setShowTemplateUploadModal] = useState(false);
  const [showTemplateListModal, setShowTemplateListModal] = useState(false);

  // 表单数据
  const [newTestCase, setNewTestCase] = useState({
    projectId: '',
    projectName: '',
    caseName: '',
    creator: '',
    module: '',
    precondition: '',
    purpose: '',
    inputData: '',
    steps: '',
    expectedResult: '',
    files: []
  });
  const [editingPlan, setEditingPlan] = useState(null);
  const [viewingPlan, setViewingPlan] = useState(null);
  const [deletingRequirement, setDeletingRequirement] = useState(null);

  // 文件相关状态
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);
  const [templateFile, setTemplateFile] = useState(null);
  const [templateFiles, setTemplateFiles] = useState([]);

  // 消息和错误状态
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});

  // 下拉框状态
  const [isCreatorDropdownOpen, setIsCreatorDropdownOpen] = useState(false);
  const [isEditCreatorDropdownOpen, setIsEditCreatorDropdownOpen] = useState(false);
  const creatorDropdownRef = useRef(null);
  const editCreatorDropdownRef = useRef(null);

  // 消息处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(''), 2000);
  };

  const handleSuccess = (message) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 2000);
  };

  // 项目相关函数
  const fetchProjects = async (searchName = '') => {
    try {
      const data = await projectApi.getProjectList(searchName);
      setProjects(data);
      // 自动选择第一个项目
      if (data.length > 0 && !selectedProject) {
        handleProjectSelect(data[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      handleError('获取项目列表失败');
    }
  };

  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    if (project) {
      await fetchTestCases(project.id);
    } else {
      setTestCases([]);
    }
  };

  const handleUpdatePlan = async () => {
    try {
      if (!validateEditForm()) {
        return;
      }

      const formData = new FormData();
      const testCaseData = {
        id: editingPlan.id,
        name: editingPlan.name,
        module: editingPlan.module,
        creatorId: parseInt(editingPlan.creator),
        creatorName: employees.find(emp => emp.id.toString() === editingPlan.creator)?.name,
        condition: editingPlan.condition,
        purpose: editingPlan.purpose,
        input: editingPlan.input,
        step: editingPlan.step,
        expect: editingPlan.expect,
        result: editingPlan.result,
        projectFiles: existingFiles,
        projectId: selectedProject.id,
        createdTime: editingPlan.createdTime
      };

      formData.append('testCase', new Blob([JSON.stringify(testCaseData)], { type: 'application/json' }));
      uploadedFiles.forEach(file => {
        formData.append('files', file);
      });

      await testCaseApi.updateTestCase(editingPlan.id, formData);
      await fetchTestCases();
      
      setShowEditModal(false);
      setEditingPlan(null);
      setUploadedFiles([]);
    } catch (error) {
      console.error('更新测试用例失败:', error);
      handleError('更新测试用例失败');
    }
  };




  // 测试用例相关函数
  const fetchTestCases = async (projectId = null, pageNum = page) => {
    try {
      if (!projectId && !selectedProject) return;
      const data = await testCaseApi.getTestCaseList(projectId || selectedProject.id, pageNum, 10);
      setTestCases(data.content);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
    } catch (error) {
      console.error('获取测试用例列表失败:', error);
      handleError('获取测试用例列表失败');
    }
  };

  const handleSearch = async () => {
    try {
      const data = await testCaseApi.searchTestCases(
        selectedProject.id, 0, 10, searchQuery, searchCreator
      );
      setTestCases(data.content);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
      setPage(0);
    } catch (error) {
      console.error('搜索测试用例失败:', error);
      handleError('搜索测试用例失败');
    }
  };

  const handleReset = async () => {
    setSearchQuery('');
    setSearchCreator('');
    setPage(0);
    await fetchTestCases();
  };

  const handlePageChange = async (newPage) => {
    setPage(newPage);
    await fetchTestCases(null, newPage);
  };

  // 员工数据获取
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      setEmployees(data);
    } catch (error) {
      console.error('获取员工列表失败:', error);
      handleError('获取员工列表失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchEmployees();
  }, []);

  // 文件处理函数
  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  const handleFileDelete = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleDragOver = (e) => e.preventDefault();

  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setUploadedFiles([...uploadedFiles, ...files]);
  };

  // 弹窗控制函数
  const openNewModal = () => {
    if (!selectedProject) {
      handleError('请先选择一个项目');
      return;
    }
    // 重置表单数据
    setNewTestCase({
      projectId: selectedProject.id,
      projectName: selectedProject.name,
      caseName: '',
      creator: '',
      module: '',
      precondition: '',
      purpose: '',
      inputData: '',
      steps: '',
      expectedResult: '',
      files: []
    });
    setUploadedFiles([]);
    setFieldErrors({});
    setShowNewPlanModal(true);
    setIsCreatorDropdownOpen(false);
  };

  // 表单验证函数
  const validateTestCase = (testCase) => {
    const errors = {};
    if (!testCase.caseName?.trim()) errors.name = '请输入用例名称';
    if (!testCase.creator) errors.creator = '请选择创建人';
    if (!testCase.module?.trim()) errors.module = '请输入用例模块';
    return errors;
  };

  // 创建测试用例函数
  const handleCreateTestCase = async () => {
    try {
      const errors = validateTestCase(newTestCase);
      setFieldErrors(errors);
      if (Object.keys(errors).length > 0) return;

      const formData = new FormData();
      const testCaseData = {
        projectId: selectedProject.id,
        name: newTestCase.caseName,
        module: newTestCase.module,
        creatorId: parseInt(newTestCase.creator),
        creatorName: employees.find(emp => emp.id.toString() === newTestCase.creator)?.name,
        purpose: newTestCase.purpose,
        input: newTestCase.inputData,
        step: newTestCase.steps,
        expect: newTestCase.expectedResult,
        condition: newTestCase.precondition,
      };

      formData.append('testCase', new Blob([JSON.stringify(testCaseData)], { type: 'application/json' }));
      uploadedFiles.forEach(file => formData.append('files', file));

      await testCaseApi.createTestCase(formData);
      await fetchTestCases();
      handleSuccess('测试用例创建成功');

      // 重置表单
      setShowNewPlanModal(false);
      setUploadedFiles([]);
      setFieldErrors({});
    } catch (error) {
      console.error('创建测试用例失败:', error);
      handleError('创建测试用例失败');
    }
  };



  // 测试用例操作函数
  const handleEditTestCase = async (id) => {
    try {
      const data = await testCaseApi.getTestCaseDetail(id);
      setEditingPlan({ ...data, creator: data.creatorId?.toString() });
      setExistingFiles(data.projectFiles || []);
      setShowEditModal(true);
    } catch (error) {
      console.error('获取测试用例详情失败:', error);
      handleError('获取测试用例详情失败');
    }
  };

  const fetchTestCaseDetail = async (id) => {
    try {
      const data = await testCaseApi.getTestCaseDetail(id);
      setViewingPlan(data);
      setShowViewModal(true);
    } catch (error) {
      console.error('获取测试用例详情失败:', error);
      handleError('获取测试用例详情失败');
    }
  };

  const handleDeleteTestCase = async () => {
    try {
      await testCaseApi.deleteTestCase(deletingRequirement.id);
      await fetchTestCases();
      setShowDeleteModal(false);
      setDeletingRequirement(null);
      handleSuccess('测试用例删除成功');
    } catch (error) {
      console.error('删除测试用例失败:', error);
      handleError('删除测试用例失败');
    }
  };

  // 文件操作函数
  const handleFilePreview = async (file) => {
    try {
      const previewUrl = await fileApi.previewFile(file.name, 'testcase');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败');
    }
  };

  const handleRemoveFile = (fileIndex, isExisting = false) => {
    if (isExisting) {
      setExistingFiles(prev => prev.filter((_, index) => index !== fileIndex));
      setEditingPlan(prev => ({
        ...prev,
        projectFiles: prev.projectFiles.filter((_, index) => index !== fileIndex)
      }));
    } else {
      setUploadedFiles(prev => prev.filter((_, index) => index !== fileIndex));
    }
  };

  // 文件下载通用函数
  const downloadFile = async (fileName, bucketName) => {
    try {
      const response = await fileApi.downloadFile(fileName, bucketName);
      if (!response.ok) throw new Error('文件下载失败');

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('文件下载失败:', error);
      handleError('文件下载失败');
    }
  };

  const handleFileDownload = (file) => downloadFile(file.name, 'testcase');

  // 模板相关函数
  const handleTemplateUpload = async () => {
    if (!templateFile) return;
    try {
      const formData = new FormData();
      formData.append('file', templateFile);
      formData.append('bucketName', 'model');

      const fileName = await fileApi.uploadFile(formData);
      localStorage.setItem('testCaseTemplateFileName', fileName);
      handleSuccess('模板上传成功');
      setShowTemplateUploadModal(false);
      setTemplateFile(null);
    } catch (error) {
      console.error('模板上传失败:', error);
      handleError('模板上传失败');
    }
  };

  // 编辑表单验证
  const validateEditForm = () => {
    const errors = {};
    if (!editingPlan.name?.trim()) errors.name = '请输入用例名称';
    if (!editingPlan.creator) errors.creator = '请选择创建人';
    if (!editingPlan.module?.trim()) errors.module = '请输入用例模块';
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };


  const fetchTemplateFiles = async () => {
    try {
      const data = await fileApi.getAllFileNames('model');
      setTemplateFiles(data);
      setShowTemplateListModal(true);
    } catch (error) {
      console.error('获取模版文件列表失败:', error);
      handleError('获取模版文件列表失败');
    }
  };

  const handleTemplatePreview = async (fileName) => {
    try {
      const previewUrl = await fileApi.previewFile(fileName, 'model');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('文件预览失败:', error);
      handleError('文件预览失败');
    }
  };

  const handleTemplateDownload = (fileName) => downloadFile(fileName, 'model');

  // 处理下拉框外部点击事件
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (creatorDropdownRef.current && !creatorDropdownRef.current.contains(event.target)) {
        setIsCreatorDropdownOpen(false);
      }
      if (editCreatorDropdownRef.current && !editCreatorDropdownRef.current.contains(event.target)) {
        setIsEditCreatorDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 错误消息提示 */}
      {errorMessage && <ErrorMessage message={errorMessage} />}

      {/* 成功消息提示 */}
      {successMessage && <SuccessMessage message={successMessage} />}

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          {/* 项目搜索框 */}
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        {/* 项目列表 */}
        <div className="space-y-2">
          {projects.map(project => (
            <div
              key={project.id}
              onClick={() => handleProjectSelect(project)}
              className={`p-3 rounded-lg cursor-pointer hover:bg-blue-50 ${selectedProject?.id === project.id ? 'bg-blue-50' : ''}`}
            >
              <div className="font-medium">{project.name}</div>
              <div className={`mt-1 text-xs inline-block px-2 py-1 rounded-full ${PROJECT_STATUS_MAP[project.status]?.color || 'bg-gray-100 text-gray-600'}`}>
                {PROJECT_STATUS_MAP[project.status]?.label || '未知状态'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">测试用例</p>
            </div>

          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="搜索用例名称"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              <div className="w-[150px]">
                <select
                  value={searchCreator}
                  onChange={(e) => setSearchCreator(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部创建人</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>{employee.name}</option>
                  ))}
                </select>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleSearch}
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                >
                  搜索
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="px-4 py-2"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={openNewModal}
                >
                  <PlusIcon className="w-4 h-4" />
                  添加测试用例
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center gap-1"
                  onClick={fetchTemplateFiles}
                >
                  <DownloadIcon className="w-4 h-4" />
                  下载模版
                </Button>
                {/* 添加上传模板按钮 */}
                <div className="relative">
                  <Button
                    variant="outline"
                    className="flex items-center gap-1 bg-blue-500 text-white"
                    onClick={() => setShowTemplateUploadModal(true)}
                  >
                    上传模版
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">用例名称</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建人</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">用例模块</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">创建时间</th>
                  <th className="px-4 py-3 text-center text-sm font-medium text-gray-500">操作</th>
                </tr>
              </thead>
              <tbody>
                {testCases.map(testCase => (
                  <tr key={testCase.id} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-900">{testCase.name}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">{testCase.creatorName}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">{testCase.module}</td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {testCase.createdTime ? new Date(testCase.createdTime).toLocaleString() : '-'}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-3">
                        <button
                          onClick={() => fetchTestCaseDetail(testCase.id)}
                          className="text-gray-400 hover:text-blue-700"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleEditTestCase(testCase.id)}
                          className="text-gray-400 hover:text-blue-700"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setDeletingRequirement(testCase);
                            setShowDeleteModal(true);
                          }}
                          className="text-gray-400 hover:text-red-500"
                          title="删除"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex items-center justify-end px-4 py-3 border-t">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 mr-4">
                  共 {totalElements} 条记录
                </span>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.max(0, page - 1))}
                  disabled={page === 0}
                  className="px-2 py-1 text-sm"
                >
                  上一页
                </Button>
                <div className="flex items-center">
                  <button
                    className={`px-3 py-1 text-sm rounded-lg ${page === 0 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                    onClick={() => handlePageChange(0)}
                  >
                    1
                  </button>
                  {page > 2 && <span className="px-2 text-gray-500">...</span>}
                  {page > 1 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page - 1)}
                    >
                      {page}
                    </button>
                  )}
                  {page > 0 && page < totalPages - 1 && (
                    <button
                      className="px-3 py-1 text-sm bg-blue-500 text-white rounded-lg"
                    >
                      {page + 1}
                    </button>
                  )}
                  {page < totalPages - 2 && (
                    <button
                      className="px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg"
                      onClick={() => handlePageChange(page + 1)}
                    >
                      {page + 2}
                    </button>
                  )}
                  {page < totalPages - 3 && <span className="px-2 text-gray-500">...</span>}
                  {totalPages > 1 && (
                    <button
                      className={`px-3 py-1 text-sm rounded-lg ${page === totalPages - 1 ? 'bg-blue-500 text-white' : 'text-gray-500 hover:bg-gray-100'}`}
                      onClick={() => handlePageChange(totalPages - 1)}
                    >
                      {totalPages}
                    </button>
                  )}
                </div>
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(Math.min(totalPages - 1, page + 1))}
                  disabled={page >= totalPages - 1}
                  className="px-2 py-1 text-sm"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看测试用例</p>
          </div>
        </div>
      )}

      {showNewPlanModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">添加用例</h3>
              <button
                onClick={() => {
                  setShowNewPlanModal(false);
                  setIsCreatorDropdownOpen(false);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newTestCase.caseName}
                      onChange={(e) => {
                        setNewTestCase({ ...newTestCase, caseName: e.target.value });
                        if (e.target.value.trim()) {
                          setFieldErrors(prev => ({ ...prev, name: '' }));
                        }
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
                        fieldErrors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="请输入用例名称"
                    />
                    {fieldErrors.name && (
                      <div className="mt-1 text-sm text-red-500 flex items-center">
                        <CrossCircledIcon className="w-4 h-4 mr-1" />
                        {fieldErrors.name}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={creatorDropdownRef}>
                      <div
                        onClick={() => setIsCreatorDropdownOpen(!isCreatorDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newTestCase.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        } ${fieldErrors.creator ? 'border-red-500' : ''}`}
                      >
                        <span className={newTestCase.creator ? 'text-gray-900' : 'text-gray-400'}>
                          {newTestCase.creator ? employees.find(emp => emp.id.toString() === newTestCase.creator)?.name : '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewTestCase({ ...newTestCase, creator: employee.id.toString() });
                                  setFieldErrors(prev => ({ ...prev, creator: '' }));
                                  setIsCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newTestCase.creator === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {fieldErrors.creator && (
                      <div className="mt-1 text-sm text-red-500 flex items-center">
                        <CrossCircledIcon className="w-4 h-4 mr-1" />
                        {fieldErrors.creator}
                      </div>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例模块 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      type="text"
                      value={newTestCase.module}
                      onChange={(e) => {
                        setNewTestCase({ ...newTestCase, module: e.target.value });
                        if (e.target.value.trim()) {
                          setFieldErrors(prev => ({ ...prev, module: '' }));
                        }
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none ${
                        fieldErrors.module ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="请输入用例模块"
                      rows={2}
                    />
                    {fieldErrors.module && (
                      <div className="mt-1 text-sm text-red-500 flex items-center">
                        <CrossCircledIcon className="w-4 h-4 mr-1" />
                        {fieldErrors.module}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <textarea
                      value={newTestCase.precondition}
                      onChange={(e) => setNewTestCase({ ...newTestCase, precondition: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none"
                      placeholder="请输入预置条件"
                      rows={2}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例目的
                    </label>
                    <textarea
                      value={newTestCase.purpose}
                      onChange={(e) => setNewTestCase({ ...newTestCase, purpose: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none"
                      placeholder="请输入用例目的"
                      rows={2}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <textarea
                      value={newTestCase.inputData}
                      onChange={(e) => setNewTestCase({ ...newTestCase, inputData: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none"
                      placeholder="请输入测试数据"
                      rows={2}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      操作步骤
                    </label>
                    <textarea
                      value={newTestCase.steps}
                      onChange={(e) => setNewTestCase({ ...newTestCase, steps: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none"
                      placeholder="请输入操作步骤"
                      rows={2}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预期结果
                    </label>
                    <textarea
                      value={newTestCase.expectedResult}
                      onChange={(e) => setNewTestCase({ ...newTestCase, expectedResult: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[60px] resize-none"
                      placeholder="请输入预期结果"
                      rows={2}
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <label
                    htmlFor="file-upload"
                    className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                  >
                    <div className="space-y-1 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                        aria-hidden="true"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-600 justify-center">
                        <span className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                          点击上传文件
                        </span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          multiple
                          onChange={handleFileUpload}
                        />
                        <p className="pl-1">或拖拽文件到这里</p>
                      </div>
                      <p className="text-xs text-gray-500">支持任意文件格式</p>
                    </div>
                  </label>
                  {/* 已上传文件列表 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                          <span className="text-sm text-gray-600">{file.name}</span>
                          <button
                            onClick={() => handleFileDelete(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewPlanModal(false);
                  setIsCreatorDropdownOpen(false);
                }}
              >
                取消
              </Button>
              <Button onClick={handleCreateTestCase} style={{ backgroundColor: '#0070f3', color: 'white' }}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showViewModal && viewingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看用例</h3>
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <div className="text-base text-gray-900">{selectedProject?.name}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例名称
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.name}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.creatorName}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例模块
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.module}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.condition || '-'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例目的
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.purpose || '-'}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.input || '-'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      操作步骤
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.step || '-'}</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预期结果
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.expect || '-'}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      实际结果
                    </label>
                    <div className="text-base text-gray-900">{viewingPlan.result || '-'}</div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    创建时间
                  </label>
                  <div className="text-base text-gray-900">
                    {viewingPlan.createdTime ? new Date(viewingPlan.createdTime).toLocaleString() : '-'}
                  </div>
                </div>

                {/* 如果有附件，显示附件列表 */}
                {viewingPlan.projectFiles && viewingPlan.projectFiles.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      附件
                    </label>
                    <div className="space-y-2">
                      {viewingPlan.projectFiles.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleFileDownload(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="下载"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => {
                  setShowViewModal(false);
                  setViewingPlan(null);
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑用例</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                  setIsEditCreatorDropdownOpen(false);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name}
                    disabled
                    className="w-full px-3 py-1.5 border rounded-lg bg-gray-50 text-gray-500"
                  />
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingPlan.name}
                      onChange={(e) => {
                        setEditingPlan({ ...editingPlan, name: e.target.value });
                        if (e.target.value.trim()) {
                          setFieldErrors(prev => ({ ...prev, name: '' }));
                        }
                      }}
                      className={`w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 ${
                        fieldErrors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="请输入用例名称"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={editCreatorDropdownRef}>
                      <div
                        onClick={() => setIsEditCreatorDropdownOpen(!isEditCreatorDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingPlan.creator ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        } ${fieldErrors.creator ? 'border-red-500' : ''}`}
                      >
                        <span className={editingPlan.creator ? 'text-gray-900' : 'text-gray-400'}>
                          {editingPlan.creator ? employees.find(emp => emp.id.toString() === editingPlan.creator)?.name : '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isEditCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingPlan({ ...editingPlan, creator: employee.id.toString() });
                                  setFieldErrors(prev => ({ ...prev, creator: '' }));
                                  setIsEditCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingPlan.creator === employee.id.toString() ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {fieldErrors.creator && (
                      <div className="mt-1 text-sm text-red-500 flex items-center">
                        <CrossCircledIcon className="w-4 h-4 mr-1" />
                        {fieldErrors.creator}
                      </div>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例模块 <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={editingPlan.module}
                      onChange={(e) => {
                        setEditingPlan({ ...editingPlan, module: e.target.value });
                        if (e.target.value.trim()) {
                          setFieldErrors(prev => ({ ...prev, module: '' }));
                        }
                      }}
                      className={`w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none ${
                        fieldErrors.module ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="请输入用例模块"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预置条件
                    </label>
                    <textarea
                      value={editingPlan.condition || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, condition: e.target.value })}
                      className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                      placeholder="请输入预置条件"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      用例目的
                    </label>
                    <textarea
                      value={editingPlan.purpose || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, purpose: e.target.value })}
                      className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                      placeholder="请输入用例目的"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入数据
                    </label>
                    <textarea
                      value={editingPlan.input || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, input: e.target.value })}
                      className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                      placeholder="请输入测试数据"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      操作步骤
                    </label>
                    <textarea
                      value={editingPlan.step || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, step: e.target.value })}
                      className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                      placeholder="请输入操作步骤"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      预期结果
                    </label>
                    <textarea
                      value={editingPlan.expect || ''}
                      onChange={(e) => setEditingPlan({ ...editingPlan, expect: e.target.value })}
                      className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                      placeholder="请输入预期结果"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    实际结果
                  </label>
                  <textarea
                    value={editingPlan.result || ''}
                    onChange={(e) => setEditingPlan({ ...editingPlan, result: e.target.value })}
                    className="w-full px-3 py-1.5 border rounded-lg focus:outline-none focus:ring-2 border-gray-300 h-[45px] resize-none"
                    placeholder="请输入实际结果"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  
                  {/* 显示已有的文件 */}
                  {existingFiles.length > 0 && (
                    <div className="mb-4 space-y-2">
                      <div className="text-sm text-gray-500 mb-2">已上传文件：</div>
                      {existingFiles.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleFilePreview(file)}
                              className="text-blue-500 hover:text-blue-700"
                              title="预览"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleRemoveFile(existingFiles.findIndex(f => f.id === file.id), true)}
                              className="text-red-500 hover:text-red-700"
                              title="删除"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 显示新上传的文件 */}
                  {uploadedFiles.length > 0 && (
                    <div className="mb-4 space-y-2">
                      <div className="text-sm text-gray-500 mb-2">新上传文件：</div>
                      {uploadedFiles.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                        >
                          <div className="flex items-center text-sm text-gray-900">
                            <FileTextIcon className="w-4 h-4 mr-2" />
                            {file.name}
                          </div>
                          <button
                            onClick={() => handleRemoveFile(index)}
                            className="text-red-500 hover:text-red-700"
                            title="删除"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 文件上传区域 */}
                  <div
                    className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-gray-50"
                    onClick={() => document.getElementById('fileInput').click()}
                  >
                    <input
                      id="fileInput"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => {
                        const files = Array.from(e.target.files || []);
                        setUploadedFiles(prev => [...prev, ...files]);
                        e.target.value = '';
                      }}
                    />
                    <FileTextIcon className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                    <div className="text-gray-500">
                      点击上传文件 或 拖拽文件到此处
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditModal(false);
                  setEditingPlan(null);
                  setUploadedFiles([]);
                  setIsEditCreatorDropdownOpen(false);
                }}
              >
                取消
              </Button>
              <Button onClick={handleUpdatePlan}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">确认删除</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-600">
                确定要删除测试用例 &ldquo;{deletingRequirement.name}&rdquo; 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingRequirement(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={handleDeleteTestCase}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {successMessage && <SuccessMessage message={successMessage} />}

      {showTemplateUploadModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">上传模版</h3>
              <button
                onClick={() => {
                  setShowTemplateUploadModal(false);
                  setTemplateFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="template-upload"
                    className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
                  >
                    <div className="space-y-1 text-center">
                      <FileTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <span className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                          点击上传模版文件
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">支持.xlsx,.xls,.doc,.docx,.pdf格式</p>
                    </div>
                    <input
                      id="template-upload"
                      type="file"
                      className="hidden"
                      accept=".xlsx,.xls,.doc,.docx,.pdf"
                      onChange={(e) => setTemplateFile(e.target.files[0])}
                    />
                  </label>
                </div>
                
                {/* 显示已选择的文件 */}
                {templateFile && (
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div className="flex items-center text-sm text-gray-900">
                      <FileTextIcon className="w-4 h-4 mr-2" />
                      {templateFile.name}
                    </div>
                    <button
                      onClick={() => setTemplateFile(null)}
                      className="text-red-500 hover:text-red-700"
                      title="删除"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowTemplateUploadModal(false);
                  setTemplateFile(null);
                }}
              >
                取消
              </Button>
              <Button
                onClick={handleTemplateUpload}
                disabled={!templateFile}
                className={!templateFile ? 'opacity-50 cursor-not-allowed' : ''}
              >
                上传
              </Button>
            </div>
          </div>
        </div>
      )}

      {showTemplateListModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">模版文件列表</h3>
              <button
                onClick={() => setShowTemplateListModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {templateFiles.length > 0 ? (
                  templateFiles.map((fileName, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center text-sm text-gray-900">
                        <FileTextIcon className="w-4 h-4 mr-2" />
                        {fileName}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleTemplatePreview(fileName)}
                          className="text-blue-500 hover:text-blue-700"
                          title="预览"
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleTemplateDownload(fileName)}
                          className="text-blue-500 hover:text-blue-700"
                          title="下载"
                        >
                          <DownloadIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500">
                    暂无模版文件
                  </div>
                )}
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowTemplateListModal(false)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});