import { makeAutoObservable } from 'mobx';

class NavigationStore {
  currentSystem = localStorage.getItem('currentSystem') || '项目管理';
  currentSubPage = localStorage.getItem('currentSubPage') || '项目创建';

  constructor() {
    makeAutoObservable(this, {}, { autoBind: true });
  }

  setCurrentSystem(system) {
    this.currentSystem = system;
    localStorage.setItem('currentSystem', system);
    
    // 根据系统设置默认子页面
    if (system === '项目管理') {
      this.setCurrentSubPage('项目创建');
    } else if (system === '人事管理') {
      this.setCurrentSubPage('组织架构');
    } else if (system === '研发管理') {
      this.setCurrentSubPage('设计输入');
    } else if (system === '采购管理') {
      this.setCurrentSubPage('库存管理');
    } else if (system === '仓储管理') {
      this.setCurrentSubPage('入库管理');
    } else if (system === '财务管理') {
      this.setCurrentSubPage('财务报表管理');
    } else if (system === '生产管理') {
      this.setCurrentSubPage('班组管理');
    } else if (system === '销售管理') {
      this.setCurrentSubPage('销售订单');
    } else if (system === '质量管理') {
      this.setCurrentSubPage('质量方针管理');
    } else {
      this.currentSubPage = null;
    }
  }

  setCurrentSubPage(subPage) {
    this.currentSubPage = subPage;
    localStorage.setItem('currentSubPage', subPage);
  }

  // 根据路由路径设置当前系统和子页面
  setCurrentByPath(path) {
    const pathSegments = path.split('/');
    
    switch (pathSegments[1]) {
      case 'project-management':
        this.setCurrentSystem('项目管理');
        switch (pathSegments[2]) {
          case 'create':
            this.setCurrentSubPage('项目创建');
            break;
          case 'input':
            this.setCurrentSubPage('项目输入');
            break;
          // ... 其他项目管理路由映射
        }
        break;
        
      case 'dev-management':
        this.setCurrentSystem('研发管理');
        switch (pathSegments[2]) {
          case 'design-input':
            this.setCurrentSubPage('设计输入');
            break;
          case 'input-review':
            this.setCurrentSubPage('输入评审');
            break;
          // ... 其他研发管理路由映射
        }
        break;
        
      // ... 其他系统的路由映射
    }
  }
}

export const navigationStore = new NavigationStore();