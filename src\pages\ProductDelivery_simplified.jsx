/**
 * 产品交付管理页面 - 简化版本
 * 
 * 主要功能：
 * 1. 项目列表展示和搜索
 * 2. 成果交付的增删改查
 * 3. 支持层级结构的子成果管理
 * 4. 文件上传和预览下载
 * 5. 分页显示和搜索过滤
 */

import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronRightIcon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import {
  projectApi,
  deliveryApi,
  fileApi,
  employeeApi
} from '../services/productDeliveryService';

// 错误提示组件
const ErrorMessage = ({ message }) => {
  return (
    <div className="absolute -top-16 left-1/2 -translate-x-1/2 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2 shadow-lg">
      <div className="text-red-500">
        <Cross2Icon className="w-4 h-4" />
      </div>
      <div className="text-sm text-red-800">{message}</div>
    </div>
  );
};

// 成果交付类型选项
const deliveryTypeOptions = [
  { value: 0, label: '文件' },
  { value: 1, label: '软件源码' },
  { value: 2, label: '工具' },
  { value: 3, label: '硬件成果' }
];

const ProductDelivery = observer(() => {
  // 基础状态
  const [projects, setProjects] = useState([]); // 项目列表
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [projectSearchQuery, setProjectSearchQuery] = useState(''); // 项目搜索关键词
  const [employees, setEmployees] = useState([]); // 员工列表
  const [errorMessage, setErrorMessage] = useState(''); // 错误提示信息
  
  // 成果交付相关状态
  const [deliveries, setDeliveries] = useState([]); // 成果交付列表
  const [searchQuery, setSearchQuery] = useState(''); // 搜索关键词
  const [searchDeliveryPerson, setSearchDeliveryPerson] = useState(''); // 交付人搜索
  const [currentPage, setCurrentPage] = useState(0); // 当前页码
  const [pageSize] = useState(10); // 每页大小
  const [totalPages, setTotalPages] = useState(0); // 总页数
  const [totalElements, setTotalElements] = useState(0); // 总记录数
  
  // 子成果相关状态
  const [childDeliveries, setChildDeliveries] = useState({}); // 子成果数据
  const [expandedDeliveries, setExpandedDeliveries] = useState(new Set()); // 展开的成果ID集合
  
  // 弹窗状态
  const [showCreateDeliveryModal, setShowCreateDeliveryModal] = useState(false); // 创建成果交付弹窗
  const [showViewDeliveryModal, setShowViewDeliveryModal] = useState(false); // 查看成果交付弹窗
  const [showEditDeliveryModal, setShowEditDeliveryModal] = useState(false); // 编辑成果交付弹窗
  const [showDeleteDeliveryModal, setShowDeleteDeliveryModal] = useState(false); // 删除确认弹窗
  const [showAddChildDeliveryModal, setShowAddChildDeliveryModal] = useState(false); // 添加子成果弹窗
  
  // 表单数据状态
  const [newDelivery, setNewDelivery] = useState({
    projectId: '',
    projectName: '',
    name: '',
    type: '',
    deliveryPerson: '',
    receiver: '',
    deliveryTime: '',
    parentDelivery: '',
    files: []
  });
  const [editingDelivery, setEditingDelivery] = useState(null); // 正在编辑的成果交付
  const [viewingDelivery, setViewingDelivery] = useState(null); // 正在查看的成果交付
  const [deletingDelivery, setDeletingDelivery] = useState(null); // 待删除的成果交付
  const [parentDelivery, setParentDelivery] = useState(null); // 父成果交付（用于添加子成果）
  
  // 表单验证错误状态
  const [deliveryErrors, setDeliveryErrors] = useState({
    name: '',
    type: '',
    deliveryPerson: '',
    receiver: '',
    deliveryTime: ''
  });

  // 错误提示处理函数
  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => {
      setErrorMessage('');
    }, 2000);
  };

  // 获取项目列表数据
  const fetchProjects = async () => {
    try {
      const data = await projectApi.getProjects();
      setProjects(data);
      
      // 如果有项目数据且没有选中的项目，自动选择第一个项目
      if (data && data.length > 0 && !selectedProject) {
        const firstProject = data[0];
        setSelectedProject(firstProject);
        // 获取第一个项目的交付数据
        fetchDeliveryData(firstProject.id);
      }
    } catch {
      handleError('获取项目列表失败');
    }
  };

  // 项目选择处理函数
  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    // 重置搜索条件
    setSearchQuery('');
    setSearchDeliveryPerson('');
    setCurrentPage(0);
    // 获取新选中项目的交付数据
    fetchDeliveryData(project.id, 0);
  };

  // 搜索处理函数
  const handleSearch = () => {
    setCurrentPage(0);
    fetchDeliveryData(selectedProject.id, 0);
  };

  // 重置搜索条件
  const handleReset = () => {
    setSearchQuery('');
    setSearchDeliveryPerson('');
    setCurrentPage(0);
    fetchDeliveryData(selectedProject.id, 0);
  };

  // 获取成果交付列表数据
  const fetchDeliveryData = async (projectId, pageNum = 0, size = 10) => {
    try {
      const data = await deliveryApi.getDeliveryList(projectId, pageNum, size);
      setDeliveries(data || []);
      setTotalPages(Math.ceil(data.length / size));
      setTotalElements(data.length);
    } catch {
      handleError('获取交付数据失败');
    }
  };

  // 项目搜索处理函数
  const handleProjectSearch = async (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    
    try {
      if (searchValue.trim()) {
        const data = await projectApi.searchProjects(searchValue);
        setProjects(data);
      } else {
        // 如果搜索词为空，重新获取所有项目
        fetchProjects();
      }
    } catch {
      handleError('搜索项目失败');
    }
  };

  // 初始化时获取项目列表
  useEffect(() => {
    fetchProjects();
  }, []);

  // 获取员工列表
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      if (data) {
        setEmployees(data);
      }
    } catch {
      handleError('获取员工列表失败');
    }
  };

  // 在组件挂载时获取员工列表
  useEffect(() => {
    fetchEmployees();
  }, []);

  // 日期格式化函数
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  // 渲染成果交付表格行（支持层级显示）
  const renderDeliveryRow = (delivery, level = 0) => {
    const isExpanded = expandedDeliveries.has(delivery.id);

    return (
      <React.Fragment key={delivery.id}>
        <tr 
          className={`hover:bg-gray-50 ${level > 0 ? 'bg-gray-50' : ''} cursor-pointer`}
          onClick={() => handleToggleExpand(delivery)}
        >
          <td className="px-4 py-3 text-sm text-gray-900">
            <div className="flex items-center" style={{ paddingLeft: `${level * 20}px` }}>
              {/* 展开/收起图标 */}
              <ChevronRightIcon 
                className={`w-4 h-4 mr-2 text-gray-500 transition-transform ${
                  isExpanded ? 'transform rotate-90' : ''
                }`}
              />
              <span>{delivery.name}</span>
            </div>
          </td>
          <td className="px-4 py-3 text-sm text-gray-900">
            {deliveryTypeOptions.find(option => option.value === delivery.type)?.label || '-'}
          </td>
          <td className="px-4 py-3 text-sm text-gray-900">{delivery.deliveredByName}</td>
          <td className="px-4 py-3 text-sm text-gray-900">{delivery.receivedBy}</td>
          <td className="px-4 py-3 text-sm text-gray-900">{formatDateTime(delivery.deliveredTime)}</td>
          {/* 操作按钮 */}
          <td className="px-4 py-3 text-sm text-center" onClick={e => e.stopPropagation()}>
            <div className="flex items-center justify-center gap-2">
              <button
                onClick={() => handleViewDelivery(delivery)}
                className="text-blue-600 hover:text-blue-800"
                title="查看"
              >
                <EyeOpenIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleEditDelivery(delivery)}
                className="text-blue-600 hover:text-blue-800"
                title="编辑"
              >
                <Pencil1Icon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleAddChildDelivery(delivery)}
                className="text-green-600 hover:text-green-800"
                title="添加子成果"
              >
                <PlusIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleDeleteDelivery(delivery)}
                className="text-red-500 hover:text-red-700"
                title="删除"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </div>
          </td>
        </tr>
        {/* 渲染子成果 */}
        {isExpanded && childDeliveries[delivery.id]?.map(child => 
          renderDeliveryRow(child, level + 1)
        )}
      </React.Fragment>
    );
  };

  // 这里需要添加其他处理函数...
  // handleToggleExpand, handleViewDelivery, handleEditDelivery, 
  // handleAddChildDelivery, handleDeleteDelivery 等

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {errorMessage && (
        <ErrorMessage message={errorMessage} />
      )}

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''}`}
              onClick={() => handleProjectSelect(project)}
            >
              <div className="font-medium">{project.name}</div>
              <div className="mt-2 text-sm">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  project.status === 0 ? 'bg-gray-100 text-gray-600' :
                  project.status === 1 ? 'bg-blue-100 text-blue-600' :
                  'bg-green-100 text-green-600'
                }`}>
                  {project.status === 0 ? '未开始' :
                   project.status === 1 ? '进行中' :
                   '已结束'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧内容区域 - 这里需要继续添加... */}
    </div>
  );
});

export default ProductDelivery;
