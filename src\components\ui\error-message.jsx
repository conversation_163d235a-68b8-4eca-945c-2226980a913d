import React, { useEffect } from 'react';
import { Cross2Icon } from '@radix-ui/react-icons';

const ErrorMessage = ({ message, onClose }) => {
  useEffect(() => {
    if (onClose) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000); // 3秒后自动关闭
      return () => clearTimeout(timer);
    }
  }, [onClose]);

  if (!message) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[100]">
      <div className="bg-white rounded-lg shadow-lg px-4 py-2 flex items-center gap-2">
        <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
          <Cross2Icon className="w-3 h-3 text-white" />
        </div>
        <span className="text-gray-700">{message}</span>
      </div>
    </div>
  );
};

export default ErrorMessage; 