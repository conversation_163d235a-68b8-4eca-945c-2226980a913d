import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { mergeRequestsService } from '../services/mergeRequestsService';
import {
  GitHubLogoIcon,
  FileTextIcon,
  ChevronRightIcon,
  PlusIcon,
  Cross2Icon,
  ChatBubbleIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { userStore } from '../store/userStore';
// 合并请求状态配置
const mergeRequestStatuses = [
  { id: 'open', name: '待合并', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'reviewing', name: '评审中', color: 'bg-blue-100 text-blue-800' },
  { id: 'merged', name: '已合并', color: 'bg-green-100 text-green-800' },
  { id: 'closed', name: '已关闭', color: 'bg-red-100 text-red-800' }
];

// 获取用户SSH密钥
const defaultSshKey = {
  title: 'seadee-key',
  key: userStore.getUserData()?.sshKey || ''
};

export const MergeRequests = observer(() => {
  // 基础状态
  const [selectedProject, setSelectedProject] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [projects, setProjects] = useState([]);
  const [mergeRequests, setMergeRequests] = useState([]);
  const [branches, setBranches] = useState([]);
  const [error, setError] = useState(null);

  // 新建合并请求相关状态
  const [showNewModal, setShowNewModal] = useState(false);
  const [newMR, setNewMR] = useState({
    title: '',
    sourceBranch: 'main',
    targetBranch: '',
    description: ''
  });

  // 差异查看相关状态
  const [showDiffModal, setShowDiffModal] = useState(false);
  const [diffData, setDiffData] = useState(null);
  const [projectFiles, setProjectFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState(null);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [currentViewingMrId, setCurrentViewingMrId] = useState(null);

  // 初始化数据加载
  useEffect(() => {
    const fetchUserAndProjects = async () => {
      try {
        const user = await mergeRequestsService.getUserBySSHKey(defaultSshKey.key);

        if (user) {
          const formattedProjects = await mergeRequestsService.getUserProjects(user.id);
          setProjects(formattedProjects);

          // 自动选择第一个项目
          if (formattedProjects.length > 0) {
            const firstProject = formattedProjects[0];
            setSelectedProject(firstProject);
            fetchMergeRequests(firstProject.id);
            fetchBranches(firstProject.id);
          }
        }
      } catch (error) {
        showError('获取数据失败');
        console.error('获取数据失败:', error);
      }
    };

    fetchUserAndProjects();
  }, []);

  // 获取项目的合并请求列表
  const fetchMergeRequests = async (projectId) => {
    try {
      const formattedMRs = await mergeRequestsService.getProjectMergeRequests(projectId);
      setMergeRequests(formattedMRs);
    } catch (error) {
      showError('获取合并请求失败');
      console.error('获取合并请求失败:', error);
    }
  };

  // 获取项目的分支列表
  const fetchBranches = async (projectId) => {
    try {
      const branchData = await mergeRequestsService.getProjectBranches(projectId);
      setBranches(branchData);
    } catch (error) {
      showError('获取分支列表失败');
      console.error('获取分支列表失败:', error);
    }
  };

  // 选择项目处理函数
  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    fetchMergeRequests(project.id);
    fetchBranches(project.id);
  };

  // 创建新的合并请求
  const handleCreateMR = async () => {
    if (!newMR.title || !newMR.sourceBranch || !newMR.targetBranch) {
      showError('请填写必填项');
      return;
    }

    try {
      await mergeRequestsService.createMergeRequest(selectedProject.id, newMR);
      await fetchMergeRequests(selectedProject.id);
      setShowNewModal(false);
      setNewMR({
        title: '',
        sourceBranch: 'main',
        targetBranch: '',
        description: ''
      });
    } catch (error) {
      showError('创建合并请求失败，请重试');
      console.error('创建合并请求失败:', error);
    }
  };

  // 查看合并请求差异
  const handleViewDiff = async (mrId) => {
    try {
      setIsLoadingFiles(true);
      const processedChanges = await mergeRequestsService.getMergeRequestDiff(selectedProject.id, mrId);

      // 如果没有差异，获取项目所有文件
      if (!processedChanges || processedChanges.length === 0) {
        const selectedMR = mergeRequests.find(mr => mr.id === `MR-${mrId}`);
        if (selectedMR) {
          const sourceBranch = selectedMR.sourceBranch;
          // 获取源分支的所有文件
          const sourceFiles = await mergeRequestsService.getProjectFiles(selectedProject.id, sourceBranch);
          setProjectFiles(sourceFiles);
        }
      }

      setDiffData(processedChanges);
      setShowDiffModal(true);
      setCurrentViewingMrId(mrId);
    } catch (error) {
      showError('获取差异数据失败，请重试');
      console.error('获取差异数据失败:', error);
    } finally {
      setIsLoadingFiles(false);
    }
  };

  const handleFileSelect = async (file) => {
    try {
      setIsLoadingFiles(true);
      setSelectedFile(file);
      
      // 使用当前正在查看的合并请求ID
      const selectedMR = mergeRequests.find(mr => mr.id === `MR-${currentViewingMrId}`);
      if (selectedMR) {
        const sourceBranch = selectedMR.sourceBranch;
        const content = await mergeRequestsService.getFileContent(selectedProject.id, file.path, sourceBranch);
        setFileContent(content);
      }
    } catch (error) {
      console.error('获取文件内容失败:', error);
      showError('获取文件内容失败，请重试');
    } finally {
      setIsLoadingFiles(false);
    }
  };

  const showError = (message) => {
    setError(message);
    // 3秒后自动清除错误
    setTimeout(() => {
      setError(null);
    }, 3000);
  };

  return (
    <>
      {/* Error Message */}
      {error && (
        <div className="fixed top-0 left-0 right-0 z-50 flex justify-center">
          <div className="bg-red-50 text-red-600 px-4 py-2 rounded-b-lg shadow-lg flex items-center gap-2">
            <Cross2Icon className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
        {/* Project List Sidebar */}
        <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          </div>
          <div className="flex-1 overflow-y-auto">
            {projects.map(project => (
                <div
                  key={project.id}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                  }`}
                  onClick={() => handleProjectSelect(project)}
                >
                  <div className="flex items-center gap-2">
                    <GitHubLogoIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <div className="font-medium">{project.name}</div>
                      <div className="text-sm text-gray-500">代码仓库</div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Merge Requests Content */}
        {selectedProject ? (
          <div className="flex-1 flex flex-col h-full">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">合并请求</p>
              </div>
              <Button 
                className="flex items-center gap-1"
                onClick={() => setShowNewModal(true)}
              >
                <PlusIcon className="w-4 h-4" />
                新建合并请求
              </Button>
            </div>


            {/* 合并请求列表 */}
            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-hidden flex flex-col min-h-0">
              <div className="divide-y overflow-y-auto" style={{ maxHeight: '750px' }}>
                {mergeRequests
                  .filter(mr =>
                    mr.projectId === selectedProject.id &&
                    (mr.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                     mr.id.toLowerCase().includes(searchQuery.toLowerCase()))
                  )
                  .map(mr => (
                    <div key={mr.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        {/* 基本信息 */}
                        <div className="flex items-center gap-4">
                          <GitHubLogoIcon className="w-8 h-8 text-blue-500" />
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{mr.title}</span>
                              <span className="text-sm text-gray-500">({mr.id})</span>
                            </div>
                            <div className="text-sm text-gray-500">
                              作者：{mr.author} | 创建时间：{mr.createdAt}
                            </div>
                          </div>
                        </div>

                        {/* 详细信息和操作 */}
                        <div className="flex items-center gap-8">
                          <div className="text-sm">
                            <div className="text-gray-500">分支</div>
                            <div>{mr.sourceBranch} → {mr.targetBranch}</div>
                          </div>
                          <div className="text-sm">
                            <div className="text-gray-500">变更</div>
                            <div>
                              <span className="text-green-600">+{mr.changes.added}</span>
                              {' '}
                              <span className="text-blue-600">~{mr.changes.modified}</span>
                              {' '}
                              <span className="text-red-600">-{mr.changes.deleted}</span>
                            </div>
                          </div>
                          <div className={`px-3 py-1 rounded-full text-sm ${
                            mergeRequestStatuses.find(s => s.id === mr.status)?.color
                          }`}>
                            {mergeRequestStatuses.find(s => s.id === mr.status)?.name}
                          </div>
                          <Button
                            variant="outline"
                            className="bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                            onClick={() => handleViewDiff(mr.id.replace('MR-', ''))}
                          >
                            查看请求
                          </Button>
                        </div>
                      </div>

                      {/* 评论区域 */}
                      {mr.comments && mr.comments.length > 0 && (
                        <div className="mt-4 pl-12">
                          <div className="text-sm text-gray-500 mb-2">最新评论</div>
                          <div className="space-y-2">
                            {mr.comments.map(comment => (
                              <div key={comment.id} className="flex items-start gap-2 p-3 bg-gray-50 rounded">
                                <ChatBubbleIcon className="w-4 h-4 text-gray-400 mt-1" />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-medium">{comment.user}</span>
                                    <span className="text-sm text-gray-500">{comment.time}</span>
                                  </div>
                                  <div className="text-gray-600">{comment.content}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <GitHubLogoIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目查看合并请求</p>
            </div>
          </div>
        )}

        {/* New MR Modal */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">新建合并请求</h3>
                <button
                  onClick={() => setShowNewModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      标题 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newMR.title}
                      onChange={(e) => setNewMR({ ...newMR, title: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入合并请求标题"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      源分支 <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={newMR.sourceBranch}
                      onChange={(e) => setNewMR({ ...newMR, sourceBranch: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">请选择源分支</option>
                      {branches.map(branch => (
                        <option key={branch.name} value={branch.name}>
                          {branch.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      目标分支 <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={newMR.targetBranch}
                      onChange={(e) => setNewMR({ ...newMR, targetBranch: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">请选择目标分支</option>
                      {branches.map(branch => (
                        <option key={branch.name} value={branch.name}>
                          {branch.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描述
                    </label>
                    <textarea
                      value={newMR.description}
                      onChange={(e) => setNewMR({ ...newMR, description: e.target.value })}
                      rows={4}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请描述此次合并请求的内容..."
                    />
                  </div>

                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNewModal(false)}
                >
                  取消
                </Button>
                <Button onClick={handleCreateMR}>
                  创建合并请求
                </Button>
              </div>
            </div>
          </div>
        )}



        {/* 代码差异查看模态框 */}
        {showDiffModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[90vw] h-[80vh] flex flex-col">
              {/* 模态框头部 */}
              <div className="p-6 border-b flex justify-between items-center">
                <h3 className="text-xl font-semibold">代码差异对比</h3>
                <button
                  onClick={() => {
                    setShowDiffModal(false);
                    setDiffData(null);
                    setProjectFiles([]);
                    setSelectedFile(null);
                    setFileContent(null);
                    setCurrentViewingMrId(null);
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <Cross2Icon className="w-4 h-4" />
                </button>
              </div>

              {/* 模态框内容 */}
              <div className="flex-1 overflow-auto p-6">
                {/* 加载状态 */}
                {isLoadingFiles && (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-500">加载中...</span>
                  </div>
                )}

                {/* 有差异时显示对比 */}
                {!isLoadingFiles && diffData && diffData.length > 0 && (
                  <div className="space-y-8">
                    {diffData.map((change, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
                          <FileTextIcon className="w-4 h-4" />
                          <span>{change.new_path}</span>
                          {change.new_path !== change.old_path && (
                            <>
                              <ChevronRightIcon className="w-4 h-4" />
                              <span>{change.old_path}</span>
                            </>
                          )}
                        </div>
                        <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto bg-gray-50 p-4 rounded">
                          {change.diff}
                        </pre>
                      </div>
                    ))}
                  </div>
                )}

                {/* 无差异时显示文件列表 */}
                {!isLoadingFiles && (!diffData || diffData.length === 0) && (
                  <div className="flex flex-col h-full">
                    <div className="text-gray-500 mb-4 text-center">
                      两个分支之间没有代码差异，以下是项目中的所有文件
                    </div>

                    <div className="grid grid-cols-4 gap-4 h-full">
                      {/* 文件列表 */}
                      <div className="col-span-1 border rounded-lg overflow-auto">
                        <div className="p-3 bg-gray-50 border-b font-medium">文件列表</div>
                        <div className="p-2">
                          {projectFiles.length > 0 ? (
                            <div className="space-y-1">
                              {projectFiles.map((file) => (
                                <div
                                  key={file.id}
                                  className={`p-2 rounded cursor-pointer hover:bg-gray-100 flex items-center gap-2 ${
                                    selectedFile?.id === file.id ? 'bg-blue-50 border border-blue-200' : ''
                                  }`}
                                  onClick={() => handleFileSelect(file)}
                                >
                                  <FileTextIcon className="w-4 h-4 text-gray-400" />
                                  <span className="text-sm truncate">{file.path}</span>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-gray-500 text-center p-4">没有找到文件</div>
                          )}
                        </div>
                      </div>

                      {/* 文件内容 */}
                      <div className="col-span-3 border rounded-lg overflow-auto">
                        <div className="p-3 bg-gray-50 border-b font-medium">
                          {selectedFile ? selectedFile.path : '文件内容'}
                        </div>
                        <div className="p-4">
                          {selectedFile ? (
                            fileContent ? (
                              <pre className="text-sm font-mono whitespace-pre-wrap overflow-x-auto">
                                {fileContent}
                              </pre>
                            ) : (
                              <div className="text-gray-500 text-center p-4">无法加载文件内容</div>
                            )
                          ) : (
                            <div className="text-gray-500 text-center p-4">请从左侧选择一个文件查看内容</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
});