/**
 * CI/CD 流水线管理页面
 *
 * 主要功能：
 * 1. 项目列表展示和选择
 * 2. 流水线列表展示和状态管理
 * 3. 流水线操作（运行、停止、重试）
 * 4. 流水线配置管理（添加模板）
 *
 * 技术栈：React + MobX + GitLab API
 */

import { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { userStore } from '../store/userStore';
import {
  GitHubLogoIcon,
  ChevronRightIcon,
  ClockIcon,
  PersonIcon,
  PlusIcon,
  Cross2Icon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';

// 流水线状态映射
const getStatusDisplay = (status) => {
  const statusMap = {
    running: { text: '运行中', class: 'bg-blue-100 text-blue-800' },
    success: { text: '成功', class: 'bg-green-100 text-green-800' },
    failed: { text: '失败', class: 'bg-red-100 text-red-800' },
    pending: { text: '等待中', class: 'bg-yellow-100 text-yellow-800' }
  };
  return statusMap[status] || { text: status, class: 'bg-gray-100 text-gray-800' };
};

// GitLab API 配置
const GITLAB_API_BASE_URL = 'http://***********/api/v4';
const GITLAB_TOKEN = userStore.token;

// 流水线模板配置
const pipelineTemplates = [
  {
    id: 1,
    name: '基础构建流水线',
    stages: ['build', 'test', 'deploy'],
    template: `stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - npm install
    - npm run build

test:
  stage: test
  script:
    - npm run test

deploy:
  stage: deploy
  script:
    - echo "部署到测试环境"`
  },
  {
    id: 2,
    name: '前端开发流水线',
    stages: ['install', 'lint', 'build', 'test'],
    template: `stages:
  - install
  - lint
  - build
  - test

install:
  stage: install
  script:
    - npm install

lint:
  stage: lint
  script:
    - npm run lint

build:
  stage: build
  script:
    - npm run build

test:
  stage: test
  script:
    - npm run test`
  }
];

// GitLab API 封装
const gitlabApi = {
  // 通用请求方法
  async request(url, options = {}) {
    const response = await fetch(`${GITLAB_API_BASE_URL}${url}`, {
      headers: {
        'Private-Token': GITLAB_TOKEN,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    return await response.json();
  },

  // 获取流水线列表
  async getPipelines(projectId) {
    return this.request(`/projects/${projectId}/pipelines`);
  },

  // 获取流水线详情
  async getPipelineDetails(projectId, pipelineId) {
    return this.request(`/projects/${projectId}/pipelines/${pipelineId}`);
  },

  // 触发流水线
  async triggerPipeline(projectId, ref, variables = {}) {
    return this.request(`/projects/${projectId}/pipeline`, {
      method: 'POST',
      body: JSON.stringify({
        ref,
        variables: Object.entries(variables).map(([key, value]) => ({ key, value }))
      })
    });
  },

  // 创建流水线配置
  async createPipeline(projectId, content) {
    return this.request(`/projects/${projectId}/repository/files/.gitlab-ci.yml`, {
      method: 'PUT',
      body: JSON.stringify({
        branch: 'main',
        content: content,
        commit_message: '更新流水线配置'
      })
    });
  },

  // 取消流水线
  async cancelPipeline(projectId, pipelineId) {
    return this.request(`/projects/${projectId}/pipelines/${pipelineId}/cancel`, {
      method: 'POST'
    });
  },

  // 重试流水线
  async retryPipeline(projectId, pipelineId) {
    return this.request(`/projects/${projectId}/pipelines/${pipelineId}/retry`, {
      method: 'POST'
    });
  }
};

export const CICD = observer(() => {
  // 核心状态管理
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [projects, setProjects] = useState([]); // 项目列表
  const [branches, setBranches] = useState([]); // 分支列表
  const [pipelines, setPipelines] = useState([]); // 流水线列表
  const [loading, setLoading] = useState(false); // 加载状态
  const [error, setError] = useState(null); // 错误信息

  // 模态框状态
  const [showNewModal, setShowNewModal] = useState(false); // 运行流水线模态框
  const [showAddPipelineModal, setShowAddPipelineModal] = useState(false); // 添加流水线模态框

  // 表单状态
  const [selectedTemplate, setSelectedTemplate] = useState(null); // 选中的流水线模板
  const [selectedPipeline, setSelectedPipeline] = useState(null); // 选中的流水线
  const [submitForm, setSubmitForm] = useState({
    description: '',
    branch: 'main'
  }); // 提交表单数据

  // 操作状态
  const [stoppingPipelineId, setStoppingPipelineId] = useState(null); // 正在停止的流水线ID

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      const username = 'songxinhao';
      try {
        // 获取用户信息
        const userResponse = await gitlabApi.request(`/users?username=${username}`);
        const user = userResponse[0];

        if (user) {
          // 获取用户项目列表
          const projectsData = await gitlabApi.request(`/users/${user.id}/projects`);
          const formattedProjects = projectsData.map(project => ({
            id: project.id,
            name: project.name
          }));

          setProjects(formattedProjects);

          // 自动选择第一个项目
          if (formattedProjects.length > 0) {
            const firstProject = formattedProjects[0];
            setSelectedProject(firstProject);
            await loadProjectData(firstProject);
          }
        }
      } catch (error) {
        console.error('初始化数据失败:', error);
        showError('初始化数据失败');
      }
    };

    initializeData();
  }, []);

  // 加载项目相关数据（分支和流水线）
  const loadProjectData = useCallback(async (project) => {
    try {
      // 获取分支列表
      const branchesData = await gitlabApi.request(`/projects/${project.id}/repository/branches`);
      setBranches(branchesData.map(branch => ({
        name: branch.name,
        default: branch.default
      })));

      // 获取流水线列表
      const pipelinesData = await gitlabApi.getPipelines(project.id);
      const detailedPipelines = await Promise.all(
        pipelinesData.map(async (pipeline) => {
          const details = await gitlabApi.getPipelineDetails(project.id, pipeline.id);
          return formatPipelineData(pipeline, details, project.id);
        })
      );
      setPipelines(detailedPipelines);
    } catch (error) {
      console.error('加载项目数据失败:', error);
      showError('加载项目数据失败');
    }
  }, []);

  // 格式化流水线数据
  const formatPipelineData = (pipeline, details, projectId) => ({
    id: pipeline.id,
    projectId: projectId,
    status: pipeline.status,
    branch: pipeline.ref,
    commit: {
      id: pipeline.sha.substring(0, 7),
      message: details.commit?.message || '无提交信息',
      author: details.commit?.author_name || '未知作者'
    },
    stages: details.stages?.map(stage => ({
      name: stage.name,
      status: stage.status,
      duration: stage.duration ? `${stage.duration}秒` : '进行中'
    })) || [],
    duration: pipeline.duration ? `${pipeline.duration}秒` : '进行中',
    createdAt: new Date(pipeline.created_at).toLocaleString()
  });

  // 处理项目选择
  const handleProjectSelect = async (project) => {
    setLoading(true);
    setSelectedProject(project);

    try {
      await loadProjectData(project);
    } catch (error) {
      console.error('切换项目失败:', error);
      showError('切换项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加流水线配置
  const handleAddPipeline = async (template) => {
    if (!selectedProject) return;

    try {
      const result = await gitlabApi.createPipeline(selectedProject.id, template.template);
      if (result.error) {
        throw new Error(result.error || '更新流水线配置失败');
      }
      setShowAddPipelineModal(false);
      setSelectedTemplate(null);
      showSuccess('流水线配置已更新');
    } catch (error) {
      console.error('更新流水线失败:', error);
      showError(error.message || '更新流水线失败');
    }
  };

  // 运行流水线
  const handleRunPipeline = async () => {
    if (!selectedProject || !selectedPipeline) return;

    try {
      const result = await gitlabApi.triggerPipeline(
        selectedProject.id,
        submitForm.branch || 'main',
        {
          PIPELINE_DESCRIPTION: submitForm.description,
          PIPELINE_TEMPLATE: selectedPipeline.name
        }
      );

      if (result.id) {
        setShowNewModal(false);
        await loadProjectData(selectedProject); // 重新加载流水线数据
        showSuccess('流水线已触发');
      } else {
        throw new Error('触发流水线失败');
      }
    } catch (error) {
      console.error('触发流水线失败:', error);
      showError('触发流水线失败');
    }
  };

  // 重试流水线
  const handleRetryPipeline = async (pipeline) => {
    if (!selectedProject) return;

    try {
      await gitlabApi.retryPipeline(selectedProject.id, pipeline.id);
      await loadProjectData(selectedProject); // 重新加载流水线数据
      showSuccess('流水线已重新运行');
    } catch (error) {
      console.error('重新运行流水线失败:', error);
      showError('重新运行流水线失败');
    }
  };

  // 取消流水线
  const handleCancelPipeline = async (pipeline) => {
    if (!selectedProject) return;

    try {
      setStoppingPipelineId(pipeline.id);
      await gitlabApi.cancelPipeline(selectedProject.id, pipeline.id);

      // 等待状态更新
      await new Promise(resolve => setTimeout(resolve, 1000));
      await loadProjectData(selectedProject); // 重新加载流水线数据
      showSuccess('流水线已停止');
    } catch (error) {
      console.error('停止流水线失败:', error);
      showError('停止流水线失败');
    } finally {
      setStoppingPipelineId(null);
    }
  };

  // 错误提示
  const showError = (message) => {
    setError(message);
    setTimeout(() => setError(null), 3000);
  };

  // 成功提示（可扩展）
  const showSuccess = (message) => {
    console.log('成功:', message);
  };

  return (
    <>
      {/* Error Message */}
      {error && (
        <div className="fixed top-0 left-0 right-0 z-50 flex justify-center">
          <div className="bg-red-50 text-red-600 px-4 py-2 rounded-b-lg shadow-lg flex items-center gap-2">
            <Cross2Icon className="w-4 h-4" />
            <span>{error}</span>
          </div>
        </div>
      )}

      <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
        {/* Project List Sidebar */}
        <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          </div>
          <div className="flex-1 overflow-y-auto">
            {projects.map(project => (
              <div
                key={project.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${
                  selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                }`}
                onClick={() => handleProjectSelect(project)}
              >
                <div className="flex items-center gap-2">
                  <GitHubLogoIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="font-medium">{project.name}</div>
                    <div className="text-sm text-gray-500">代码仓库</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pipeline Content */}
        {selectedProject ? (
          <div className="flex-1 flex flex-col h-full">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
                <p className="text-gray-500">CI/CD 流水线</p>
              </div>
              <Button 
                className="flex items-center gap-1"
                onClick={() => setShowAddPipelineModal(true)}
                disabled={!selectedProject}
                variant="outline"
              >
                <PlusIcon className="w-4 h-4" />
                添加流水线
              </Button>
            </div>

            <div className="bg-white rounded-lg shadow-sm flex-1 overflow-hidden">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-500">加载中...</div>
                </div>
              ) : (
                <div>
                  {/* 表头 */}
                  <div className="grid grid-cols-4 gap-4 p-4 border-b bg-gray-50 font-medium text-gray-600">
                    <div>状态</div>
                    <div>创建人</div>
                    <div>创建时间</div>
                    <div className="text-right">操作</div>
                  </div>
                  
                  {/* 列表内容 */}
                  <div className="divide-y">
                    {pipelines
                      .filter(pipe => pipe.projectId === selectedProject.id)
                      .map(pipeline => (
                        <div key={pipeline.id} className="p-4 hover:bg-gray-50">
                          <div className="grid grid-cols-4 gap-4 items-center">
                            {/* 状态列 */}
                            <div className="flex items-center gap-2">
                              {(() => {
                                const statusInfo = getStatusDisplay(pipeline.status);
                                return (
                                  <div className={`px-3 py-1 rounded-full text-sm ${statusInfo.class}`}>
                                    {statusInfo.text}
                                  </div>
                                );
                              })()}
                              <div className="text-sm text-gray-600">
                                {pipeline.commit.message}
                              </div>
                            </div>

                            {/* 创建人列 */}
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                              <PersonIcon className="w-4 h-4" />
                              {pipeline.commit.author}
                            </div>

                            {/* 创建时间列 */}
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                              <ClockIcon className="w-4 h-4" />
                              {pipeline.createdAt}
                            </div>

                            {/* 操作列 */}
                            <div className="flex items-center justify-end gap-2">
                              <Button 
                                className="flex items-center gap-1"
                                onClick={() => handleRetryPipeline(pipeline)}
                                size="sm"
                                disabled={pipeline.status === 'running'}
                              >
                                运行
                              </Button>
                              <Button
                                className="flex items-center gap-1"
                                onClick={() => handleCancelPipeline(pipeline)}
                                size="sm"
                                variant="destructive"
                                disabled={pipeline.status !== 'running' || stoppingPipelineId === pipeline.id}
                              >
                                {stoppingPipelineId === pipeline.id ? (
                                  <div className="flex items-center">
                                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-1"></div>
                                    停止中...
                                  </div>
                                ) : (
                                  '停止'
                                )}
                              </Button>
                            </div>
                          </div>

                          {/* 阶段信息 */}
                          <div className="flex gap-4 mt-4">
                            {pipeline.stages.map((stage, index) => (
                              <div key={index} className="flex items-center">
                                {index > 0 && <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-2" />}
                                {(() => {
                                  const stageStatusInfo = getStatusDisplay(stage.status);
                                  return (
                                    <div className={`px-3 py-1 rounded-lg text-sm ${stageStatusInfo.class}`}>
                                      {stage.name}
                                      <span className="ml-2 text-xs">{stage.duration}</span>
                                    </div>
                                  );
                                })()}
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <GitHubLogoIcon className="w-12 h-12 mx-auto mb-4" />
              <p>请选择左侧的项目提交代码</p>
            </div>
          </div>
        )}

        {/* New MR Modal */}
        {showNewModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b">
                <h3 className="text-xl font-semibold">运行流水线</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      选择流水线
                    </label>
                    <div className="space-y-2">
                      {pipelineTemplates.map(template => (
                        <div
                          key={template.id}
                          className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                            selectedPipeline?.id === template.id ? 'border-blue-500 bg-blue-50' : ''
                          }`}
                          onClick={() => setSelectedPipeline(template)}
                        >
                          {template.name}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      分支
                    </label>
                    <select
                      value={submitForm.branch || 'main'}
                      onChange={(e) => setSubmitForm({ ...submitForm, branch: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg"
                    >
                      {branches.map(branch => (
                        <option key={branch.name} value={branch.name}>
                          {branch.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      描述
                    </label>
                    <textarea
                      value={submitForm.description}
                      onChange={(e) => setSubmitForm({ ...submitForm, description: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg"
                      rows={4}
                      placeholder="请输入流水线描述..."
                    />
                  </div>
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowNewModal(false)}>
                  取消
                </Button>
                <Button onClick={handleRunPipeline} disabled={!selectedPipeline}>
                  运行
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Add Pipeline Modal */}
        {showAddPipelineModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl w-[600px]">
              <div className="p-6 border-b">
                <h3 className="text-xl font-semibold">添加流水线</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {pipelineTemplates.map(template => (
                    <div
                      key={template.id}
                      className={`p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        selectedTemplate?.id === template.id ? 'border-blue-500 bg-blue-50' : ''
                      }`}
                      onClick={() => setSelectedTemplate(template)}
                    >
                      <div className="font-medium">{template.name}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        阶段：{template.stages.join(' → ')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowAddPipelineModal(false)}>
                  取消
                </Button>
                <Button
                  onClick={() => handleAddPipeline(selectedTemplate)}
                  disabled={!selectedTemplate}
                >
                  添加
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
});