import React from 'react';
import { observer } from 'mobx-react-lite';
import { Button } from '../components/ui/button';
import { userStore } from '../store/userStore';

export const UserSettings = observer(() => {
  const { user, updateSettings, setCurrentPage } = userStore;

  const handleToggleNotifications = () => {
    updateSettings({ notifications: !user.settings.notifications });
  };

  const handleThemeChange = (theme) => {
    updateSettings({ theme });
  };

  const handleLanguageChange = (language) => {
    updateSettings({ language });
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-y-auto scrollbar-none bg-gray-50">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-6">用户设置</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">个人信息</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  用户名
                </label>
                <input
                  type="text"
                  value={user.username}
                  disabled
                  className="w-full px-3 py-2 border rounded-md bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱
                </label>
                <input
                  type="email"
                  value={user.email}
                  disabled
                  className="w-full px-3 py-2 border rounded-md bg-gray-50"
                />
              </div>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-4">通知设置</h3>
            <div className="flex items-center justify-between">
              <span>接收系统通知</span>
              <Button
                variant={user.settings.notifications ? 'default' : 'outline'}
                onClick={handleToggleNotifications}
              >
                {user.settings.notifications ? '已开启' : '已关闭'}
              </Button>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-4">主题设置</h3>
            <div className="flex gap-4">
              <Button
                variant={user.settings.theme === 'light' ? 'default' : 'outline'}
                onClick={() => handleThemeChange('light')}
              >
                浅色主题
              </Button>
              <Button
                variant={user.settings.theme === 'dark' ? 'default' : 'outline'}
                onClick={() => handleThemeChange('dark')}
              >
                深色主题
              </Button>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-medium mb-4">语言设置</h3>
            <div className="flex gap-4">
              <Button
                variant={user.settings.language === 'zh-CN' ? 'default' : 'outline'}
                onClick={() => handleLanguageChange('zh-CN')}
              >
                中文
              </Button>
              <Button
                variant={user.settings.language === 'en-US' ? 'default' : 'outline'}
                onClick={() => handleLanguageChange('en-US')}
              >
                English
              </Button>
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(null)}
          >
            返回
          </Button>
        </div>
      </div>
    </div>
  );
});