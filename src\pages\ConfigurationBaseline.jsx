import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  TrashIcon,
  Pencil1Icon,
  EyeOpenIcon,
  LayersIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { projectApi, configLineApi, fileApi, employeeApi } from '../services/configurationBaselineService';

/**
 * 配置基线管理组件
 * 功能：项目配置基线的创建、编辑、删除、查看
 */
export const ConfigurationBaseline = observer(() => {
  // ==================== 状态管理 ====================

  // 项目相关状态
  const [selectedProject, setSelectedProject] = useState(null); // 当前选中的项目
  const [projectSearchQuery, setProjectSearchQuery] = useState(''); // 项目搜索关键词
  const [projects, setProjects] = useState([]); // 项目列表

  // 基线相关状态
  const [baselines, setBaselines] = useState([]); // 基线列表
  const [searchQuery, setSearchQuery] = useState(''); // 基线搜索关键词
  const [creatorFilter, setCreatorFilter] = useState(''); // 创建人筛选
  const [selectedBaseline, setSelectedBaseline] = useState(null); // 查看详情的基线
  const [editingBaseline, setEditingBaseline] = useState(null); // 正在编辑的基线
  const [deletingBaseline, setDeletingBaseline] = useState(null); // 待删除的基线

  // 弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false); // 详情弹窗
  const [showNewModal, setShowNewModal] = useState(false); // 新建弹窗
  const [showEditModal, setShowEditModal] = useState(false); // 编辑弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false); // 删除确认弹窗

  // 表单相关状态
  const [newBaseline, setNewBaseline] = useState({
    name: '',
    createdBy: '',
    description: ''
  }); // 新建基线表单数据
  const [uploadFiles, setUploadFiles] = useState([]); // 上传的文件列表
  const [formErrors, setFormErrors] = useState({}); // 表单验证错误

  // 其他状态
  const [employees, setEmployees] = useState([]); // 员工列表
  const [pagination, setPagination] = useState({
    currentPage: 0,
    totalPages: 0,
    totalElements: 0
  }); // 分页信息
  const [showError, setShowError] = useState(false); // 错误提示显示状态
  const [errorMessage, setErrorMessage] = useState(''); // 错误提示内容
  const [isCreatorDropdownOpen, setIsCreatorDropdownOpen] = useState(false); // 创建人下拉框状态
  const [isEditCreatorDropdownOpen, setIsEditCreatorDropdownOpen] = useState(false); // 编辑时创建人下拉框状态

  // DOM引用
  const creatorDropdownRef = useRef(null);
  const editCreatorDropdownRef = useRef(null);

  // ==================== 工具函数 ====================

  /**
   * 格式化日期时间字符串
   * @param {string} dateTimeString - ISO格式的日期时间字符串
   * @returns {string} 格式化后的日期时间字符串
   */
  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    return dateTimeString.replace('T', ' ');
  };

  /**
   * 格式化文件大小
   * @param {number} size - 文件大小（字节）
   * @returns {string} 格式化后的文件大小字符串
   */
  const formatFileSize = (size) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  };

  // ==================== 事件处理函数 ====================

  /**
   * 选择项目处理函数
   * @param {Object} project - 项目对象
   */
  const handleSelectProject = (project) => {
    setSelectedProject(project);
    fetchBaselines(project.id);
  };

  /**
   * 项目搜索处理函数
   * @param {Event} e - 输入事件
   */
  const handleProjectSearch = (e) => {
    const query = e.target.value;
    setProjectSearchQuery(query);
    fetchProjects(query);
  };

  /**
   * 文件选择处理函数
   * @param {Event} e - 文件选择事件
   */
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files || []);
    setUploadFiles(prev => [...prev, ...files]);
  };

  /**
   * 文件拖拽放置处理函数
   * @param {Event} e - 拖拽事件
   */
  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setUploadFiles(prev => [...prev, ...files]);
  };

  /**
   * 文件拖拽悬停处理函数
   * @param {Event} e - 拖拽事件
   */
  const handleDragOver = (e) => {
    e.preventDefault();
  };

  /**
   * 移除文件处理函数
   * @param {number} index - 文件索引
   */
  const handleRemoveFile = (index) => {
    setUploadFiles(prev => prev.filter((_, i) => i !== index));
  };

  // ==================== 副作用处理 ====================

  /**
   * 点击外部区域关闭下拉框
   */
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (creatorDropdownRef.current && !creatorDropdownRef.current.contains(event.target)) {
        setIsCreatorDropdownOpen(false);
      }
      if (editCreatorDropdownRef.current && !editCreatorDropdownRef.current.contains(event.target)) {
        setIsEditCreatorDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // ==================== API调用函数 ====================

  /**
   * 获取员工列表
   */
  const fetchEmployees = async () => {
    try {
      const data = await employeeApi.getEmployeeList();
      const employeeList = data.map(item => ({
        id: item.id,
        name: item.name
      }));
      setEmployees(employeeList);
    } catch (error) {
      console.error('获取员工列表失败:', error);
    }
  };

  /**
   * 获取项目列表
   * @param {string} searchQuery - 搜索关键词
   */
  const fetchProjects = async (searchQuery = '') => {
    try {
      const data = await projectApi.getProjectList(searchQuery);
      const projectList = data.map(item => ({
        id: item.id,
        name: item.name,
        status: item.status || 0,
        progress: item.progress || 0,
        description: item.description || '',
        createdTime: item.createdTime || '',
        createdBy: item.createdBy || '',
        updatedTime: item.updatedTime || '',
        updatedBy: item.updatedBy || ''
      }));
      setProjects(projectList);

      // 如果没有选中项目且有项目列表，自动选中第一个项目
      if (!selectedProject && projectList.length > 0) {
        const firstProject = projectList[0];
        setSelectedProject(firstProject);
        fetchBaselines(firstProject.id);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setErrorMessage('获取项目列表失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 获取基线详情
   * @param {string|number} id - 基线ID
   * @returns {Object} 格式化后的基线详情数据
   */
  const fetchBaselineDetail = async (id) => {
    try {
      const data = await configLineApi.getConfigLineDetail(id);
      return {
        id: data.id,
        name: data.name,
        version: data.version || '',
        creator: data.creator,
        creatorName: data.creatorName,
        createdTime: formatDateTime(data.createdTime),
        description: data.description || '',
        projectId: data.projectId,
        files: data.files || []
      };
    } catch (error) {
      console.error('获取基线详情失败:', error);
      throw error;
    }
  };

  /**
   * 获取基线列表
   * @param {string|number} projectId - 项目ID
   * @param {number} page - 页码，默认为0
   */
  const fetchBaselines = async (projectId, page = 0) => {
    try {
      const data = await configLineApi.getConfigLineList(projectId, page, 10, searchQuery, creatorFilter);
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        version: item.version || '',
        creatorName: item.creatorName,
        createdTime: formatDateTime(item.createdTime),
        description: item.description || '',
        projectId: item.projectId,
        files: item.files || []
      }));

      setBaselines(formattedData);
      setPagination({
        currentPage: data.number,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });
    } catch (error) {
      console.error('获取基线列表失败:', error);
    }
  };

  // ==================== 业务处理函数 ====================

  /**
   * 创建基线处理函数
   */
  const handleCreateBaseline = async () => {
    // 表单验证
    const errors = {};
    if (!newBaseline.name) errors.name = '请输入基线名称';
    if (!newBaseline.createdBy) errors.createdBy = '请选择创建人';

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // 查找创建人信息
    const creator = employees.find(emp => emp.name === newBaseline.createdBy);
    if (!creator) {
      alert('创建人信息无效');
      return;
    }

    try {
      // 构建请求数据
      const requestData = {
        configLine: {
          name: newBaseline.name,
          version: newBaseline.version || '',
          creator: creator.id,
          createdTime: new Date().toISOString(),
          description: newBaseline.description || '',
          projectId: selectedProject.id,
          creatorName: creator.name
        }
      };

      // 构建FormData
      const formData = new FormData();
      formData.append('configLine', new Blob([JSON.stringify(requestData.configLine)], {
        type: 'application/json'
      }));

      uploadFiles.forEach(file => {
        formData.append('files', file);
      });

      // 发送创建请求
      const response = await configLineApi.createConfigLine(formData);

      if (!response.success) {
        throw new Error(response.message || '创建基线失败，一个项目只能创建一个基线');
      }

      // 重置表单状态
      setUploadFiles([]);
      setNewBaseline({ name: '', createdBy: '', description: '' });
      setShowNewModal(false);

      // 刷新基线列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

      // 显示成功提示
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);

    } catch (error) {
      console.error('创建基线失败:', error);
      const errorMsg = error.response?.data?.message || error.message || '创建基线失败';
      setErrorMessage(errorMsg);
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 输入框变化处理函数（清除错误状态）
   * @param {string} field - 字段名
   * @param {any} value - 字段值
   */
  const handleInputChange = (field, value) => {
    setNewBaseline(prev => ({ ...prev, [field]: value }));

    // 清除对应字段的错误提示
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  /**
   * 保存编辑基线处理函数
   */
  const handleSaveEdit = async () => {
    if (!editingBaseline.name) {
      alert('请填写基线名称');
      return;
    }

    try {
      // 获取已存在文件的ID数组
      const existingFileIds = (editingBaseline.files || [])
        .filter(file => !(file instanceof File) && file !== null)
        .map(file => file.id)
        .filter(id => id !== undefined && id !== null);

      // 构建请求数据
      const requestData = {
        configLine: {
          id: editingBaseline.id,
          name: editingBaseline.name,
          version: editingBaseline.version || '',
          creator: editingBaseline.creator,
          createdTime: editingBaseline.createdTime.replace(' ', 'T') + '.000Z',
          description: editingBaseline.description || '',
          projectId: editingBaseline.projectId,
          creatorName: editingBaseline.creatorName,
          fileIds: existingFileIds,
          files: (editingBaseline.files || [])
            .filter(file => !(file instanceof File) && file !== null)
            .map(file => ({
              id: file.id || 0,
              name: file.name || '',
              path: file.path || '',
              type: file.type || 0,
              size: file.size || 0,
              otherId: file.otherId || 0,
              uploadTime: file.uploadTime ? file.uploadTime.replace(' ', 'T') + '.000Z' : new Date().toISOString(),
              uploaderId: file.uploaderId || 0,
              description: file.description || ''
            }))
        }
      };

      // 构建FormData
      const formData = new FormData();
      formData.append('configLine', new Blob([JSON.stringify(requestData.configLine)], {
        type: 'application/json'
      }));

      // 添加新上传的文件
      const newFiles = (editingBaseline.files || [])
        .filter(file => file instanceof File && file !== null);

      newFiles.forEach(file => {
        formData.append('files', file);
      });

      // 发送更新请求
      await configLineApi.updateConfigLine(editingBaseline.id, formData);

      // 刷新基线列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

      // 关闭编辑弹窗
      setShowEditModal(false);
      setEditingBaseline(null);

    } catch (error) {
      console.error('更新基线失败:', error);
      setErrorMessage(error.message || '更新失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 查看基线详情处理函数
   * @param {Object} baseline - 基线对象
   */
  const handleViewDetail = async (baseline) => {
    try {
      const detailData = await fetchBaselineDetail(baseline.id);
      setSelectedBaseline(detailData);
      setShowDetailModal(true);
    } catch (error) {
      console.error('获取基线详情失败:', error);
      setErrorMessage('获取基线详情失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 编辑基线处理函数
   * @param {Object} baseline - 基线对象
   */
  const handleEditClick = async (baseline) => {
    try {
      const detailData = await fetchBaselineDetail(baseline.id);
      setEditingBaseline(detailData);
      setShowEditModal(true);
    } catch (error) {
      console.error('获取基线详情失败:', error);
      alert('获取基线详情失败');
    }
  };

  /**
   * 删除基线处理函数
   * @param {Object} baseline - 基线对象
   */
  const handleDeleteClick = (baseline) => {
    setDeletingBaseline(baseline);
    setShowDeleteModal(true);
  };

  /**
   * 确认删除基线处理函数
   */
  const handleConfirmDelete = async () => {
    if (!deletingBaseline) return;

    try {
      await configLineApi.deleteConfigLine(deletingBaseline.id);

      // 刷新基线列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

      // 关闭删除弹窗
      setShowDeleteModal(false);
      setDeletingBaseline(null);

      // 显示成功提示
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);

    } catch (error) {
      console.error('删除基线失败:', error);
      setErrorMessage(error.message || '删除失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 监听选中项目变化，重新获取基线列表
   */
  useEffect(() => {
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  }, [selectedProject]);

  /**
   * 文件预览处理函数
   * @param {Object} file - 文件对象
   */
  const handlePreviewFile = async (file) => {
    try {
      const previewUrl = await fileApi.previewFile(file.name);

      // 处理URL格式问题
      let processedUrl = previewUrl;
      if (processedUrl.includes('http: //')) {
        processedUrl = processedUrl.replace('http: //', 'http://');
      }
      if (processedUrl.includes(' ')) {
        processedUrl = processedUrl.replace(/\s+/g, '');
      }

      // 在新窗口打开预览
      window.open(processedUrl, '_blank');

    } catch (error) {
      console.error('获取文件预览失败:', error);
      setErrorMessage('获取文件预览失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  /**
   * 搜索基线处理函数
   */
  const handleSearch = async () => {
    if (!selectedProject) return;

    try {
      const data = await configLineApi.getConfigLineList(
        selectedProject.id,
        0,
        10,
        searchQuery,
        creatorFilter
      );

      // 格式化数据并更新列表
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        version: item.version || '',
        creatorName: item.creatorName,
        createdTime: formatDateTime(item.createdTime),
        description: item.description || '',
        projectId: item.projectId,
        files: item.files || []
      }));

      setBaselines(formattedData);
      setPagination({
        currentPage: data.number,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });

    } catch (error) {
      console.error('搜索失败:', error);
      alert('搜索失败: ' + (error.message || '请求出错'));
    }
  };

  /**
   * 重置搜索条件处理函数
   */
  const handleReset = async () => {
    // 清空搜索条件
    setSearchQuery('');
    setCreatorFilter('');

    // 重新获取基线列表
    if (selectedProject) {
      fetchBaselines(selectedProject.id, 0);
    }
  };

  /**
   * 编辑弹窗中删除文件处理函数
   * @param {string|number} fileId - 文件ID或文件名
   */
  const handleRemoveEditingFile = (fileId) => {
    setEditingBaseline(prev => ({
      ...prev,
      files: prev.files.filter(file => {
        // 新上传的文件使用name比较，已有文件使用id比较
        if (file instanceof File) {
          return file.name !== fileId;
        }
        return file.id !== fileId;
      })
    }));
  };

  /**
   * 分页处理函数
   * @param {number} newPage - 新页码
   */
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }));
    fetchBaselines(selectedProject.id, newPage);
  };

  /**
   * 文件下载处理函数
   * @param {Object} file - 文件对象
   */
  const handleDownloadFile = async (file) => {
    try {
      const response = await fileApi.downloadFile(file.name);

      if (!response.ok) {
        throw new Error('文件下载失败');
      }

      // 创建下载链接并触发下载
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

    } catch (error) {
      console.error('文件下载失败:', error);
      setErrorMessage('文件下载失败');
      setShowError(true);
      setTimeout(() => {
        setShowError(false);
        setErrorMessage('');
      }, 3000);
    }
  };

  // ==================== 组件初始化 ====================

  /**
   * 组件挂载时初始化数据
   */
  useEffect(() => {
    fetchProjects(); // 获取项目列表
    fetchEmployees(); // 获取员工列表
  }, []);

  // ==================== 组件渲染 ====================

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-full">
        {/* 项目搜索区域 */}
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>

        {/* 项目列表区域 */}
        <div className="flex-1 overflow-y-auto">
          {projects
            .filter(project =>
              project.name.toLowerCase().includes(projectSearchQuery.toLowerCase())
            )
            .map(project => (
              <div
                key={project.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${
                  selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                }`}
                onClick={() => handleSelectProject(project)}
              >
                <div className="font-medium">{project.name}</div>
                <div className="mt-1 text-sm">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    project.status === 0 ? 'bg-gray-100 text-gray-800' :
                    project.status === 1 ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {project.status === 0 ? '未开始' :
                     project.status === 1 ? '进行中' : '已结束'}
                  </span>
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* 右侧主要内容区域 */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col h-full">
          {/* 页面标题区域 */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">配置基线管理</p>
            </div>
          </div>

          {/* 搜索和操作区域 */}
          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex items-center gap-4">
              {/* 基线搜索框 */}
              <div className="relative flex-1 max-w-md">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索基线名称、版本..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              {/* 创建人筛选下拉框 */}
              <div className="w-48">
                <select
                  value={creatorFilter}
                  onChange={(e) => setCreatorFilter(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部创建人</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 操作按钮组 */}
              <Button
                onClick={handleSearch}
                className="px-4 py-2"
                style={{ backgroundColor: '#007bff', color: 'white' }}
              >
                搜索
              </Button>
              <Button
                onClick={handleReset}
                className="px-4 py-2"
                variant="outline"
              >
                重置
              </Button>
              <Button
                className="flex items-center gap-1"
                onClick={() => setShowNewModal(true)}
                style={{ backgroundColor: '#007bff', color: 'white' }}
              >
                <PlusIcon className="w-4 h-4" />
                创建配置基线
              </Button>
            </div>
          </div>

          {/* 基线列表表格区域 */}
          <div className="flex-1 bg-white rounded-lg shadow-sm overflow-hidden flex flex-col">
            {/* 表格内容区域 */}
            <div className="flex-1 overflow-auto">
              <table className="w-full border-collapse">
                {/* 表格头部 */}
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">基线名称</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">创建人</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">基线描述</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">创建时间</th>
                    <th className="px-4 py-2 text-right text-sm font-medium text-gray-600 border-b">操作</th>
                  </tr>
                </thead>
                {/* 表格主体 */}
                <tbody className="divide-y divide-gray-200">
                  {baselines.map((baseline) => (
                    <tr key={baseline.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">{baseline.name}</td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">{baseline.creatorName}</td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">
                        {baseline.description ? (
                          <span className="line-clamp-1">{baseline.description}</span>
                        ) : (
                          <span className="text-gray-400">暂无描述</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">{baseline.createdTime}</td>
                      <td className="px-4 py-3 text-right text-sm font-medium space-x-2 border-b">
                        {/* 查看详情按钮 */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetail(baseline)}
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </Button>
                        {/* 编辑按钮 */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditClick(baseline)}
                          className="text-blue-600"
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </Button>
                        {/* 删除按钮 */}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600"
                          onClick={() => handleDeleteClick(baseline)}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 分页器区域 */}
            <div className="flex justify-end items-center p-4 border-t">
              <div className="flex items-center gap-2">
                <span>共 {pagination.totalElements} 条记录</span>
                {/* 上一页按钮 */}
                <Button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 0}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  上一页
                </Button>

                {/* 页码按钮组 */}
                {Array.from({ length: Math.max(1, pagination.totalPages) }, (_, index) => (
                  <Button
                    key={index}
                    onClick={() => handlePageChange(index)}
                    className={`px-3 py-1 border rounded ${
                      pagination.currentPage === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'
                    }`}
                    disabled={pagination.totalElements === 0 && index > 0}
                  >
                    {index + 1}
                  </Button>
                ))}

                {/* 下一页按钮 */}
                <Button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage >= pagination.totalPages - 1 || pagination.totalElements === 0}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* 未选择项目时的空状态 */
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看配置基线</p>
          </div>
        </div>
      )}

      {/* ==================== 弹窗组件区域 ==================== */}

      {/* 基线详情查看弹窗 */}
      {showDetailModal && selectedBaseline && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <div>
                <h3 className="text-xl font-semibold">查看配置基线</h3>
                <div className="text-sm text-gray-500 mt-1">
                  编号：{selectedBaseline.id}
                </div>
              </div>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 gap-6 mb-6">
                <div>
                  <div className="text-sm text-gray-500 mb-1">项目名称</div>
                  <div className="font-medium">{selectedProject?.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 mb-1">基线名称</div>
                  <div className="font-medium">{selectedBaseline.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 mb-1">创建者</div>
                  <div className="font-medium">{selectedBaseline.creatorName}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 mb-1">创建时间</div>
                  <div className="font-medium">{selectedBaseline.createdTime}</div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium mb-2">基线描述</h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    {selectedBaseline.description || '暂无描述'}
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium mb-2">文件列表</h4>
                  <div className="space-y-2">
                    {(selectedBaseline.files || []).map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="font-medium">{file.name}</span>
                          <span className="text-sm text-gray-500">({formatFileSize(file.size)})</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-gray-500">{formatDateTime(file.uploadTime)}</div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePreviewFile(file)}
                            className="text-blue-600"
                          >
                            <EyeOpenIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadFile(file)}
                            className="text-blue-600"
                          >
                            <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 创建基线弹窗 */}
      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">创建配置基线</h3>
              <button
                onClick={() => setShowNewModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={selectedProject?.name || ''}
                        disabled
                        className="flex-1 px-3 py-2 border rounded-lg bg-gray-50"
                      />
                      <Button
                        onClick={async () => {
                          try {
                            const response = await fileApi.downloadArchive();

                            if (!response.ok) {
                              throw new Error('文件打包失败');
                            }

                            const blob = await response.blob();
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = '项目文件.zip';
                            document.body.appendChild(a);
                            a.click();
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);

                          } catch (error) {
                            console.error('文件打包失败:', error);
                            setErrorMessage('文件打包失败');
                            setShowError(true);

                            setTimeout(() => {
                              setShowError(false);
                              setErrorMessage('');
                            }, 3000);
                          }
                        }}
                        className="h-[38px] px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2 whitespace-nowrap"
                      >
                        <LayersIcon className="w-4 h-4" />
                        打包输入/输出文件
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newBaseline.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.name ? 'border-red-500' : ''}`}
                      placeholder="请输入基线名称"
                    />
                    {formErrors.name && (
                      <div className="mt-1 text-red-500 text-sm">{formErrors.name}</div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={creatorDropdownRef}>
                      <div
                        onClick={() => setIsCreatorDropdownOpen(!isCreatorDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newBaseline.createdBy ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newBaseline.createdBy ? 'text-gray-900' : 'text-gray-400'}>
                          {newBaseline.createdBy || '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  handleInputChange('createdBy', employee.name);
                                  setIsCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newBaseline.createdBy === employee.name ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {formErrors.createdBy && (
                      <div className="mt-1 text-red-500 text-sm">{formErrors.createdBy}</div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <textarea
                    value={newBaseline.description}
                    onChange={(e) => setNewBaseline({ ...newBaseline, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线描述..."
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    文件上传
                  </label>
                  <label className="block cursor-pointer">
                    <div
                      className="mt-1 flex flex-col items-center justify-center w-full h-32 px-4 transition bg-white border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400"
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                    >
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg className="w-8 h-8 mb-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <p className="mb-2 text-sm text-gray-500">
                          点击或拖拽文件到这里
                        </p>
                        <input
                          type="file"
                          multiple
                          className="hidden"
                          onChange={handleFileChange}
                        />
                      </div>
                    </div>
                  </label>
                  {/* 修改文件列表显示区域 */}
                  {uploadFiles.length > 0 && (
                    <div className="mt-4">
                      <div className="max-h-[100px] overflow-y-auto pr-2">
                        <div className="space-y-2">
                          {uploadFiles.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center gap-2">
                                <FileTextIcon className="w-5 h-5 text-blue-500 flex-shrink-0" />
                                <span className="text-sm text-gray-600 truncate">{file.name}</span>
                                <span className="text-xs text-gray-400 flex-shrink-0">
                                  ({(file.size / 1024).toFixed(2)} KB)
                                </span>
                              </div>
                              <button
                                onClick={() => handleRemoveFile(index)}
                                className="p-1 hover:bg-gray-200 rounded-full flex-shrink-0"
                              >
                                <Cross2Icon className="w-4 h-4 text-gray-500" />
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateBaseline}>
                创建基线
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑基线弹窗 */}
      {showEditModal && editingBaseline && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            {/* 弹窗头部 */}
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑基线</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* 项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* 基线名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingBaseline.name}
                      onChange={(e) => setEditingBaseline({ ...editingBaseline, name: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入基线名称"
                    />
                  </div>

                  {/* 创建人 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="relative" ref={editCreatorDropdownRef}>
                      <div
                        onClick={() => setIsEditCreatorDropdownOpen(!isEditCreatorDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !editingBaseline.creatorName ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={editingBaseline.creatorName ? 'text-gray-900' : 'text-gray-400'}>
                          {editingBaseline.creatorName || '请选择创建人'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isEditCreatorDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isEditCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingBaseline({
                                    ...editingBaseline,
                                    creator: employee.id,
                                    creatorName: employee.name
                                  });
                                  setIsEditCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingBaseline.creator === employee.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <textarea
                    value={editingBaseline.description}
                    onChange={(e) => setEditingBaseline({ ...editingBaseline, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线描述..."
                  />
                </div>

                {/* 文件列表 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件列表
                  </label>
                  <div className="space-y-2">
                    {(editingBaseline.files || []).map((file) => (
                      <div key={file instanceof File ? file.name : file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="font-medium">{file.name}</span>
                          <span className="text-sm text-gray-500">
                            ({formatFileSize(file instanceof File ? file.size : file.size)})
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-gray-500">
                            {file instanceof File ? '新上传' : formatDateTime(file.uploadTime)}
                          </div>
                          <div className="flex gap-1">
                            {!(file instanceof File) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreviewFile(file)}
                                className="text-blue-600"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </Button>
                            )}
                            <button
                              onClick={() => handleRemoveEditingFile(file instanceof File ? file.name : file.id)}
                              className="p-1 hover:bg-gray-200 rounded-full"
                            >
                              <Cross2Icon className="w-4 h-4 text-gray-500" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 文件上传区域 */}
                  <div className="mt-4">
                    <label className="cursor-pointer">
                      <div
                        className="mt-1 flex flex-col items-center justify-center w-full h-32 px-4 transition bg-white border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400"
                        onDragOver={handleDragOver}
                        onDrop={(e) => {
                          e.preventDefault();
                          const files = Array.from(e.dataTransfer.files);
                          setEditingBaseline(prev => ({
                            ...prev,
                            files: [...(prev.files || []), ...files]
                          }));
                        }}
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg className="w-8 h-8 mb-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className="mb-2 text-sm text-gray-500">
                            点击或拖拽文件到这里
                          </p>
                          <input
                            type="file"
                            multiple
                            className="hidden"
                            onChange={(e) => {
                              const files = Array.from(e.target.files || []);
                              setEditingBaseline(prev => ({
                                ...prev,
                                files: [...(prev.files || []), ...files]
                              }));
                            }}
                          />
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleSaveEdit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认弹窗 */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            {/* 弹窗内容 */}
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-2">确认删除</h3>
              <p className="text-gray-600">
                确定要删除项目 &ldquo;{deletingBaseline?.name}&rdquo; 吗？此操作不可恢复。
              </p>
            </div>
            {/* 弹窗按钮区域 */}
            <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingBaseline(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示组件 */}
      {showError && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-white rounded-md shadow-md px-4 py-2 flex items-center gap-2">
          <span className="text-sm text-red-500">{errorMessage}</span>
        </div>
      )}
    </div>
  );
});