import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronRightIcon,
  ChevronLeftIcon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon,
  DownloadIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import ErrorMessage from '../components/ui/error-message';
import SuccessMessage from '../components/ui/success-message';
import { userStore } from '../store/userStore';
import { projectApi, reviewApi, fileApi, employeeApi, schemeDesignApi } from '../services/designReviewService';

// 评审类型配置
const reviewTypes = [
  { id: '0', name: '架构评审' },
  { id: '1', name: '数据库评审' },
  { id: '2', name: '接口评审' },
  { id: '3', name: '界面评审' }
];

// 项目状态配置
const projectStatuses = [
  { id: '0', name: '未开始', color: 'bg-gray-100 text-gray-800' },
  { id: '1', name: '进行中', color: 'bg-blue-100 text-blue-800' },
  { id: '2', name: '已结束', color: 'bg-green-100 text-green-800' }
];

// 评审状态配置
const reviewStatuses = [
  { id: '0', name: '待评审', color: 'bg-yellow-100 text-yellow-800' },
  { id: '1', name: '已通过', color: 'bg-green-100 text-green-800' },
  { id: '2', name: '未通过', color: 'bg-red-100 text-red-800' }
];

// API 调用函数
const fetchProjects = async () => {
  try {
    return await projectApi.getAllProjects();
  } catch (error) {
    console.error('获取项目列表失败:', error);
    return [];
  }
};

const fetchReviews = async (projectId, page = 0, size = 10) => {
  try {
    const userData = userStore.getUserData();
    const employeeId = userData?.id;
    const data = await reviewApi.getReviewList(projectId, page, size, employeeId);
    const reviews = data.content || [];
    return reviews.map(review => ({
      ...review,
      type: String(review.type),
      status: String(review.status)
    }));
  } catch (error) {
    console.error('获取评审列表失败:', error);
    return [];
  }
};

const fetchReviewDetail = async (id) => {
  try {
    return await reviewApi.getReviewDetail(id);
  } catch (error) {
    console.error('获取评审详情失败:', error);
    return null;
  }
};

const fetchEmployees = async () => {
  try {
    return await employeeApi.getAllEmployees();
  } catch (error) {
    console.error('获取员工列表失败:', error);
    return [];
  }
};

const fetchSchemeDesigns = async (projectId, searchText = '') => {
  try {
    const data = await schemeDesignApi.getSchemeDesigns(projectId, 0, 50, searchText);
    return data.content || [];
  } catch (error) {
    console.error('获取方案设计列表失败:', error);
    return [];
  }
};

// 工具函数
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  try {
    const date = new Date(dateTimeStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return dateTimeStr;
  }
};

// 设计评审主组件
export const DesignReview = observer(() => {
  // 搜索和筛选状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // 弹窗状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showReviewerSelect, setShowReviewerSelect] = useState(false);
  const [showContentSelect, setShowContentSelect] = useState(false);

  // 数据状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState('');
  const [filteredReviews, setFilteredReviews] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [schemeDesigns, setSchemeDesigns] = useState([]);

  // 表单状态
  const [selectedReview, setSelectedReview] = useState(null);
  const [editingReview, setEditingReview] = useState(null);
  const [newReview, setNewReview] = useState({
    name: '',
    type: '',
    description: '',
    content: '',
    reviewLeader: '',
    reviewDate: ''
  });
  const [selectedReviewers, setSelectedReviewers] = useState([]);
  const [selectedSchemeId, setSelectedSchemeId] = useState(null);
  const [deleteId, setDeleteId] = useState(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const pageSize = 10;

  // 下拉框状态
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);
  const [isLeaderDropdownOpen, setIsLeaderDropdownOpen] = useState(false);

  // 消息状态
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  // DOM 引用
  const leaderDropdownRef = useRef(null);
  const reviewerSelectRef = useRef(null);
  const contentSelectRef = useRef(null);
  const typeDropdownRef = useRef(null);

  // 文件处理函数
  const handlePreviewFile = async (fileName) => {
    try {
      const previewUrl = await fileApi.previewFile(fileName);
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('获取文件预览失败:', error);
      setError('获取文件预览失败，请重试');
    }
  };

  const handleDownloadFile = async (fileName) => {
    try {
      const response = await fileApi.downloadFile(fileName);
      const blob = await response.blob();

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess('文件下载成功');
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('下载文件失败:', error);
      setError('下载文件失败，请重试');
    }
  };

  // 加载项目列表
  useEffect(() => {
    const loadProjects = async () => {
      const projectList = await fetchProjects();
      setProjects(projectList);
      if (!selectedProject && projectList.length > 0) {
        setSelectedProject(projectList[0].id);
      }
    };
    loadProjects();
  }, [selectedProject]);

  // 加载评审列表
  useEffect(() => {
    const loadReviews = async () => {
      if (selectedProject) {
        try {
          const userData = userStore.getUserData();
          const employeeId = userData?.id;
          const data = await reviewApi.getReviewList(selectedProject, currentPage, pageSize, employeeId);

          const reviews = data.content || [];
          const formattedReviews = reviews.map(review => ({
            ...review,
            type: String(review.type),
            status: String(review.status)
          }));
          setFilteredReviews(formattedReviews);
          setTotalPages(data.totalPages);
          setTotalElements(data.totalElements);
        } catch (error) {
          console.error('获取评审列表失败:', error);
        }
      }
    };
    loadReviews();
  }, [selectedProject, currentPage]);

  // 修改获取员工列表的函数调用
  useEffect(() => {
    const loadEmployees = async () => {
      const employeeList = await fetchEmployees();
      setEmployees(employeeList);
    };
    loadEmployees();
  }, []);

  // 修改获取方案设计列表的函数调用
  const loadSchemeDesigns = async (projectId, searchText = '') => {
    if (projectId) {
      const designs = await fetchSchemeDesigns(projectId, searchText);
      setSchemeDesigns(designs);
    }
  };

  const handleViewDetail = async (review) => {
    try {
      const reviewDetail = await fetchReviewDetail(review.id);
      if (reviewDetail) {
        setSelectedReview({
          ...reviewDetail,
          type: String(reviewDetail.type),
          status: String(reviewDetail.status),
          reviewLeader: reviewDetail.reviewLeaderName,
          reviewers: reviewDetail.reviewersName || [],
          content: reviewDetail.name,
          description: reviewDetail.description || '',
          conclusion: reviewDetail.advice || '',
          attachments: reviewDetail.schemeDesignFiles || []
        });
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error('获取评审详情失败:', error);
      setError('获取评审详情失败');
    }
  };

  const handleCreateReview = async () => {
    try {
      // 表单验证
      const errors = {};
      if (!newReview.name) errors.name = '请输入评审名称';
      if (!newReview.type) errors.type = '请选择评审类型';
      if (!newReview.reviewLeader) errors.reviewLeader = '请选择评审组长';
      if (!newReview.reviewDate) errors.reviewDate = '请选择评审日期';
      if (!selectedReviewers.length) errors.reviewers = '请选择评审人';
      if (!selectedSchemeId) errors.content = '请选择评审内容';
      if (!selectedProject) errors.project = '请选择项目';

      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }

      const requestData = {
        name: newReview.name,
        type: parseInt(newReview.type),
        status: 0,
        reviewDate: new Date(newReview.reviewDate).toISOString(),
        reviewLeader: parseInt(newReview.reviewLeader),
        reviewersId: selectedReviewers.map(reviewer => reviewer.id),
        schemeDesignId: selectedSchemeId,
        advice: newReview.description || '',
        projectId: parseInt(selectedProject),
        personAndAdvice: {}
      };

      await reviewApi.createReview(requestData);

      // 刷新列表并关闭弹窗
      const reviews = await fetchReviews(selectedProject, currentPage, pageSize);
      setFilteredReviews(reviews);
      setShowNewModal(false);

      // 重置表单
      setNewReview({
        name: '',
        type: '',
        description: '',
        content: '',
        reviewLeader: '',
        reviewDate: ''
      });
      setSelectedReviewers([]);
      setSelectedSchemeId(null);
      setFormErrors({});
      setSuccess('创建评审成功');

    } catch (error) {
      console.error('创建评审失败:', error);
      setError('创建评审失败，请重试');
    }
  };

  const handleEdit = async (review) => {
    try {
      const reviewDetail = await fetchReviewDetail(review.id);

      if (reviewDetail) {
        const currentProject = projects.find(p => p.id === parseInt(selectedProject));
        setEditingReview({
          ...reviewDetail,
          projectName: currentProject?.name || '',
          type: String(reviewDetail.type),
          status: reviewDetail.status === '0' ? '2' : '3',
          projectFiles: reviewDetail.schemeDesignFiles || [],
          reviewLeader: reviewDetail.reviewLeaderName,
          reviewersName: reviewDetail.reviewersName || []
        });
        setShowEditModal(true);
      } else {
        alert('获取评审详情失败');
      }
    } catch (error) {
      console.error('获取评审详情失败:', error);
      setError('获取评审详情失败');
    }
  };

  const confirmReview = async (status, advice, id) => {
    try {
      const data = await reviewApi.confirmReview(id, status, advice);
      return data;
    } catch (error) {
      console.error('评审确认失败:', error);
      return null;
    }
  };

  // 保存评审编辑
  const handleSaveEdit = async (status) => {
    if (!editingReview.advice) {
      setFormErrors({ advice: '请输入审批意见!' });
      return;
    }

    try {
      await confirmReview(status, editingReview.advice, editingReview.id);

      // 刷新列表并关闭弹窗
      const reviews = await fetchReviews(selectedProject, currentPage, pageSize);
      setFilteredReviews(reviews);
      setShowEditModal(false);
      setEditingReview(null);
      setFormErrors({});
      setSuccess('评审确认成功');
    } catch (error) {
      console.error('保存评审失败:', error);
      setError('保存评审失败，请重试');
    }
  };

  // 搜索评审
  const handleSearch = async () => {
    try {
      const userData = userStore.getUserData();
      const params = {
        projectId: selectedProject,
        page: 0,
        size: pageSize,
        employeeId: userData?.id,
        ...(searchQuery && { name: searchQuery }),
        ...(selectedType && { type: selectedType }),
        ...(selectedStatus && { status: selectedStatus })
      };

      const data = await reviewApi.searchReviews(params);
      setFilteredReviews(data.content.map(review => ({
        ...review,
        type: String(review.type),
        status: String(review.status)
      })));
      setCurrentPage(0);
      setTotalPages(data.totalPages);
      setTotalElements(data.totalElements);
    } catch (error) {
      console.error('搜索失败:', error);
      setError('搜索失败，请重试');
    }
  };

  // 添加分页处理函数
  const handlePageChange = (newPage) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  // 重置搜索条件
  const handleReset = async () => {
    setSearchQuery('');
    setSelectedType('');
    setSelectedStatus('');

    // 重新加载评审列表
    try {
      const reviews = await fetchReviews(selectedProject, 0, pageSize);
      setFilteredReviews(reviews);
      setCurrentPage(0);
    } catch (error) {
      console.error('重置搜索失败:', error);
      setError('重置搜索失败，请重试');
    }
  };

  // 修改 handleDelete 函数
  const handleDelete = async (id) => {
    setDeleteId(id);
    setShowDeleteModal(true);
  };

  // 确认删除评审
  const confirmDelete = async () => {
    try {
      await reviewApi.deleteReview(deleteId);
      const reviews = await fetchReviews(selectedProject, currentPage, pageSize);
      setFilteredReviews(reviews);
      setShowDeleteModal(false);
      setSuccess('删除成功');
    } catch (error) {
      console.error('删除评审失败:', error);
      setError('删除失败，请重试');
    }
  };

  // 处理移除评审人
  const handleRemoveReviewer = (reviewerId) => {
    setSelectedReviewers(selectedReviewers.filter(r => r.id !== reviewerId));
  };

  // 修改选择方案时的处理逻辑
  const handleSelectScheme = (design) => {
    setNewReview({
      ...newReview,
      content: design.name, // 只设置评审内容为方案名称
      description: design.description || ''
    });
    setSelectedSchemeId(design.id); // 保存选中的方案ID
    setShowContentSelect(false);
  };

  // 添加点击外部区域处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (leaderDropdownRef.current && !leaderDropdownRef.current.contains(event.target)) {
        setIsLeaderDropdownOpen(false);
      }
      if (reviewerSelectRef.current && !reviewerSelectRef.current.contains(event.target) && 
          !event.target.closest('.reviewer-select-dropdown')) {
        setShowReviewerSelect(false);
      }
      if (contentSelectRef.current && !contentSelectRef.current.contains(event.target)) {
        setShowContentSelect(false);
      }
      if (typeDropdownRef.current && !typeDropdownRef.current.contains(event.target)) {
        setIsTypeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 错误和成功消息提示 */}
      {error && (
        <ErrorMessage
          message={error}
          onClose={() => setError(null)}
        />
      )}
      {success && (
        <SuccessMessage
          message={success}
          onClose={() => setSuccess(null)}
        />
      )}

      {/* 左侧项目列表 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projects.map(project => (
            <div
              key={project.id}
              className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                }`}
              onClick={() => setSelectedProject(project.id)}
            >
              <div className="font-medium mb-2">{project.name}</div>
              <div>
                <span className={`px-2 py-1 text-xs rounded-full ${projectStatuses.find(s => s.id === String(project.status))?.color}`}>
                  {projectStatuses.find(s => s.id === String(project.status))?.name || '未知状态'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 右侧主要内容区域 */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 页面标题 */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">
                {projects.find(p => p.id === selectedProject)?.name}
              </h1>
              <p className="text-gray-500">方案评审</p>
            </div>
          </div>

          {/* 搜索和筛选工具栏 */}
          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4">
              {/* 搜索框 */}
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索评审..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              {/* 评审类型筛选 */}
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">评审类型（全部）</option>
                {reviewTypes.map(type => (
                  <option key={type.id} value={type.id}>{type.name}</option>
                ))}
              </select>

              {/* 评审状态筛选 */}
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">评审状态（全部）</option>
                {reviewStatuses.map(status => (
                  <option key={status.id} value={status.id}>{status.name}</option>
                ))}
              </select>

              {/* 操作按钮组 */}
              <div className="flex gap-2">
                <Button
                  onClick={handleSearch}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  style={{ backgroundColor: '#007bff', color: 'white' }}
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={() => setShowNewModal(true)}
                >
                  <PlusIcon className="w-4 h-4" />
                  新建评审
                </Button>
              </div>
            </div>
          </div>

          {/* 评审列表表格 */}
          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <div className="divide-y">
              {/* 表格头部 */}
              <div className="p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="grid grid-cols-4 gap-8 flex-1 mr-8">
                    <div className="text-sm font-medium text-gray-500">评审名称</div>
                    <div className="text-sm font-medium text-gray-500">评审类型</div>
                    <div className="text-sm font-medium text-gray-500">评审状态</div>
                    <div className="text-sm font-medium text-gray-500">评审时间</div>
                  </div>
                  <div className="w-[120px] text-sm font-medium text-gray-500">操作</div>
                </div>
              </div>

              {/* 评审列表数据行 */}
              {filteredReviews.map(review => (
                <div key={review.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="grid grid-cols-4 gap-8 flex-1 mr-8">
                      <div className="flex items-center gap-2">
                        <FileTextIcon className="w-5 h-5 text-blue-500" />
                        <div className="text-sm font-medium">{review.name}</div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {reviewTypes.find(t => t.id === review.type)?.name}
                      </div>
                      <div>
                        <span className={`px-2 py-1 text-xs rounded-full ${reviewStatuses.find(s => s.id === review.status)?.color
                          }`}>
                          {reviewStatuses.find(s => s.id === review.status)?.name}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatDateTime(review.reviewDate)}
                      </div>
                    </div>
                    {/* 操作按钮 */}
                    <div className="flex gap-2 w-[120px] justify-end">
                      <Button
                        variant="outline"
                        onClick={() => handleViewDetail(review)}
                        title="查看详情"
                      >
                        <EyeOpenIcon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleEdit(review)}
                        title="编辑评审"
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        className="text-red-600"
                        onClick={() => handleDelete(review.id)}
                        title="删除评审"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 分页控件 */}
          <div className="flex items-center justify-end mt-4">
            <div className="flex items-center gap-2">
              <span>共 {totalElements} 条记录</span>
              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 0}
                variant="outline"
                className="flex items-center gap-1"
              >
                <ChevronLeftIcon className="w-4 h-4" />
                上一页
              </Button>
              {Array.from({ length: totalPages }, (_, i) => (
                <Button
                  key={i}
                  onClick={() => handlePageChange(i)}
                  variant={currentPage === i ? "default" : "outline"}
                  className={`w-8 h-8 p-0 ${currentPage === i ? 'bg-blue-500 text-white hover:bg-blue-600' : ''}`}
                >
                  {i + 1}
                </Button>
              ))}
              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages - 1}
                variant="outline"
                className="flex items-center gap-1"
              >
                下一页
                <ChevronRightIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      ) : (
        /* 未选择项目时的空状态 */
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看评审</p>
          </div>
        </div>
      )}

      {/* 评审详情查看弹窗 */}
      {showDetailModal && selectedReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看评审</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">项目名称</div>
                    <div className=" p-4 rounded-lg">
                      {projects.find(p => p.id === selectedProject)?.name}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审名称</div>
                    <div className=" p-4 rounded-lg">
                      {selectedReview.name}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审类型</div>
                    <div className=" p-4 rounded-lg">
                      {reviewTypes.find(t => t.id === String(selectedReview.type))?.name}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审组长</div>
                    <div className=" p-4 rounded-lg">
                      {selectedReview.reviewLeader}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审人员</div>
                    <div className=" p-4 rounded-lg">
                      {selectedReview.reviewers.join('、')}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审状态</div>
                    <div className=" p-4 rounded-lg">
                      <span className={`px-2 py-1 text-xs rounded-full ${reviewStatuses.find(s => s.id === String(selectedReview.status))?.color
                        }`}>
                        {reviewStatuses.find(s => s.id === String(selectedReview.status))?.name}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审描述</div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      {selectedReview.description || '暂无描述'}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-gray-500 mb-1">评审内容</div>
                    <div className="bg-gray-50 p-4 rounded-lg whitespace-pre-line">
                      {selectedReview.content || '暂无内容'}
                    </div>
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 mb-1">评审结论</div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    {selectedReview.conclusion || '暂无结论'}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500 mb-1">附件</div>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    {selectedReview.attachments && selectedReview.attachments.length > 0 ? (
                      selectedReview.attachments.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg mb-2 last:mb-0">
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4 text-gray-400" />
                            <span>{file.name}</span>
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => handlePreviewFile(file.name)}
                              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600 hover:text-blue-600"
                            >
                              <EyeOpenIcon className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDownloadFile(file.name)}
                              className="p-2 hover:bg-gray-100 rounded-lg text-gray-600 hover:text-blue-600"
                            >
                              <DownloadIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-gray-500 text-sm text-center py-2">暂无附件</div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 新建评审弹窗 */}
      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          {error && (
            <ErrorMessage
              message={error}
              onClose={() => setError(null)}
            />
          )}

          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">新建评审</h3>
              <button
                onClick={() => setShowNewModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newReview.name}
                      onChange={(e) => {
                        setNewReview({ ...newReview, name: e.target.value });
                        if (formErrors.name) {
                          setFormErrors({ ...formErrors, name: '' });
                        }
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.name ? 'border-red-500' : ''
                        }`}
                      placeholder="请输入评审名称"
                    />
                    {formErrors.name && (
                      <div className="text-red-500 text-sm mt-1">
                        {formErrors.name}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={typeDropdownRef}>
                      <div
                        onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newReview.type ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newReview.type ? 'text-gray-900' : 'text-gray-400'}>
                          {newReview.type ? reviewTypes.find(t => t.id === newReview.type)?.name : '请选择评审类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isTypeDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {reviewTypes.map(type => (
                              <div
                                key={type.id}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setNewReview({ ...newReview, type: type.id });
                                  setIsTypeDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newReview.type === type.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {type.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div ref={leaderDropdownRef}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审组长 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        onClick={() => setIsLeaderDropdownOpen(!isLeaderDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newReview.reviewLeader ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newReview.reviewLeader ? 'text-gray-900' : 'text-gray-400'}>
                          {newReview.reviewLeader ? employees.find(e => e.id === parseInt(newReview.reviewLeader))?.name : '请选择评审组长'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isLeaderDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isLeaderDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setNewReview({ ...newReview, reviewLeader: String(employee.id) });
                                  setFormErrors({ ...formErrors, reviewLeader: '' });
                                  setIsLeaderDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newReview.reviewLeader === String(employee.id) ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {formErrors.reviewLeader && (
                      <div className="text-red-500 text-sm mt-1">
                        {formErrors.reviewLeader}
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div
                        ref={reviewerSelectRef}
                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowReviewerSelect(true);
                        }}
                      >
                        {selectedReviewers.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedReviewers.map(reviewer => (
                              <div key={reviewer.id} className="inline-flex items-center gap-1 bg-gray-100 rounded px-2 py-1">
                                <span className="text-sm">{reviewer.name}</span>
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRemoveReviewer(reviewer.id);
                                  }}
                                  className="text-gray-400 hover:text-red-500"
                                >
                                  <Cross2Icon className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-400">请选择评审人员</span>
                        )}
                      </div>
                      {showReviewerSelect && (
                        <div
                          className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto reviewer-select-dropdown"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className="flex flex-col p-2">
                            <div className="flex justify-between items-center mb-2 pb-2 border-b">
                              <h3 className="font-medium">选择评审人员</h3>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setShowReviewerSelect(false);
                                }}
                                className="p-1 rounded-full hover:bg-gray-100"
                              >
                                <Cross2Icon className="w-4 h-4" />
                              </button>
                            </div>
                            {employees.map((employee) => (
                              <div
                                key={employee.id}
                                className="flex items-center gap-2 cursor-pointer p-2 hover:bg-gray-50"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  const reviewer = {
                                    id: employee.id,
                                    name: employee.name
                                  };
                                  if (!selectedReviewers.find(r => r.id === employee.id)) {
                                    setSelectedReviewers([...selectedReviewers, reviewer]);
                                  } else {
                                    handleRemoveReviewer(employee.id);
                                  }
                                }}
                              >
                                <input
                                  type="checkbox"
                                  checked={selectedReviewers.some(r => r.id === employee.id)}
                                  onChange={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    const reviewer = {
                                      id: employee.id,
                                      name: employee.name
                                    };
                                    if (!selectedReviewers.find(r => r.id === employee.id)) {
                                      setSelectedReviewers([...selectedReviewers, reviewer]);
                                    } else {
                                      handleRemoveReviewer(employee.id);
                                    }
                                  }}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span className="text-sm">{employee.name}</span>
                              </div>
                            ))}
                            <div className="mt-3 flex justify-end">
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  setShowReviewerSelect(false);
                                }}
                                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                              >
                                确定
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审时间 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="datetime-local"
                      value={newReview.reviewDate}
                      onChange={(e) => setNewReview({ ...newReview, reviewDate: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      step="1"
                    />
                  </div>

                  <div ref={contentSelectRef}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审内容
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={newReview.content}
                        onChange={(e) => {
                          setNewReview({ ...newReview, content: e.target.value });
                          loadSchemeDesigns(selectedProject, e.target.value);
                        }}
                        onFocus={() => {
                          setShowContentSelect(true);
                          loadSchemeDesigns(selectedProject);
                        }}
                        className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入或选择评审内容..."
                      />
                      {showContentSelect && (
                        <div className="absolute z-50 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
                          {schemeDesigns.map((design) => (
                            <div
                              key={design.id}
                              className="p-2 hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleSelectScheme(design)}
                            >
                              <div className="font-medium">{design.name}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    评审建议
                  </label>
                  <textarea
                    value={newReview.description}
                    onChange={(e) => setNewReview({ ...newReview, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入评审描述..."
                  />
                </div>

              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateReview}>
                创建评审
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑评审弹窗 */}
      {showEditModal && editingReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑评审</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={editingReview.projectName}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      方案名称
                    </label>
                    <input
                      type="text"
                      value={editingReview.name}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审类型
                    </label>
                    <select
                      value={editingReview.type}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    >
                      {reviewTypes.map(type => (
                        <option key={type.id} value={type.id}>{type.name}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审组长
                    </label>
                    <input
                      type="text"
                      value={editingReview.reviewLeader || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审人员
                    </label>
                    <input
                      type="text"
                      value={editingReview.reviewersName ? editingReview.reviewersName.join('，') : ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      评审时间
                    </label>
                    <input
                      type="text"
                      value={formatDateTime(editingReview.reviewDate)}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    附件
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    {editingReview.projectFiles && editingReview.projectFiles.length > 0 ? (
                      editingReview.projectFiles.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 mb-2 last:mb-0">
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="text-sm">{file.name}</span>
                          <button
                            onClick={() => handlePreviewFile(file.name)}
                            className="ml-auto p-2 hover:bg-gray-100 rounded-lg"
                          >
                            <EyeOpenIcon className="w-4 h-4 text-gray-600 hover:text-blue-600" />
                          </button>
                        </div>
                      ))
                    ) : (
                      <div className="text-gray-500 text-sm text-center py-2">暂无附件</div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    审批意见 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={editingReview.advice || ''}
                    onChange={(e) => {
                      setEditingReview({ ...editingReview, advice: e.target.value });
                      if (formErrors.advice) {
                        setFormErrors({ ...formErrors, advice: '' });
                      }
                    }}
                    rows={2}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.advice ? 'border-red-500' : ''
                      }`}
                    placeholder="请输入审批意见..."
                  />
                  {formErrors.advice && (
                    <div className="text-red-500 text-sm mt-1">
                      {formErrors.advice}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEditModal(false)}>
                取消
              </Button>
              <Button style={{ backgroundColor: '#007bff', color: 'white' }} onClick={() => handleSaveEdit(1)}>
                通过
              </Button>
              <Button style={{ backgroundColor: '#FF0000', color: 'white' }} onClick={() => handleSaveEdit(2)}>
                不通过
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认弹窗 */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-medium mb-2">确定要删除吗？</h3>
              <p className="text-gray-500 text-sm">删除后将无法恢复。</p>
            </div>
            <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
              <Button
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
              >
                取消
              </Button>
              <Button
                style={{ backgroundColor: '#FF0000', color: 'white' }}
                onClick={confirmDelete}
              >
                确定
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

export default DesignReview;