import React, { useState, useRef, useEffect } from 'react';
import { 
  ChatBubbleIcon, 
  BellIcon, 
  BookmarkIcon, 
  BoxIcon,
  EnterFullScreenIcon,
  MinusIcon,
  Cross2Icon
} from '@radix-ui/react-icons';
import { observer } from 'mobx-react-lite';
import { chatStore } from '../store/chatStore';
import { notificationStore } from '../store/notificationStore';
import { knowledgeStore } from '../store/knowledgeStore';
import { fileStore } from '../store/fileStore';
import { NotificationDropdown } from './NotificationDropdown';
import { KnowledgeBase } from './KnowledgeBase';

export const SystemToolbar = observer(() => {
  const [showNotifications, setShowNotifications] = useState(false);
  const notificationRef = useRef(null);

  const handleChatClick = () => {
    chatStore.isOpen = true;
    chatStore.isMinimized = false;
    chatStore.markAllMessagesRead();
  };

  const handleKnowledgeClick = () => {
    knowledgeStore.markAllDocumentsRead();
  };

  const handleFileClick = () => {
    fileStore.toggleModal();
    fileStore.markAllFilesRead();
  };

  const handleClickOutside = (event) => {
    if (notificationRef.current && !notificationRef.current.contains(event.target)) {
      setShowNotifications(false);
    }
  };

  const handleClose = () => {
    // 隐藏工具栏
    const toolbar = document.querySelector('.fixed.top-0.right-0');
    if (toolbar) {
      toolbar.style.display = 'none';
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <>
      <div className="fixed top-0 right-0 h-12 bg-white shadow-sm flex items-center px-4 gap-4 z-50">
        <div className="flex items-center gap-4">
          <button 
            className="p-2 hover:bg-gray-100 rounded-full relative"
            onClick={handleChatClick}
          >
            <ChatBubbleIcon className="w-5 h-5 text-gray-600" />
            {chatStore.unreadMessages > 0 && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">{chatStore.unreadMessages}</span>
              </div>
            )}
          </button>
          <div className="relative" ref={notificationRef}>
            <button
              className={`p-2 rounded-full ${showNotifications ? 'bg-gray-100' : 'hover:bg-gray-100'}`}
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <div className="relative">
                <BellIcon className="w-5 h-5 text-gray-600" />
                {notificationStore.unreadCount > 0 && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">{notificationStore.unreadCount}</span>
                  </div>
                )}
              </div>
            </button>
            {showNotifications && <NotificationDropdown onClose={() => setShowNotifications(false)} />}
          </div>
          <button 
            className="p-2 hover:bg-gray-100 rounded-full relative"
            onClick={handleKnowledgeClick}
          >
            <BookmarkIcon className="w-5 h-5 text-gray-600" />
            {knowledgeStore.newDocuments > 0 && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">{knowledgeStore.newDocuments}</span>
              </div>
            )}
          </button>
          <button 
            className="p-2 hover:bg-gray-100 rounded-full relative"
            onClick={handleFileClick}
          >
            <BoxIcon className="w-5 h-5 text-gray-600" />
            {fileStore.newSharedFiles > 0 && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">{fileStore.newSharedFiles}</span>
              </div>
            )}
          </button>
        </div>
        <div className="h-6 w-px bg-gray-200 mx-2"></div>
        <div className="flex items-center gap-2">
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <EnterFullScreenIcon className="w-5 h-5 text-gray-600" />
          </button>
          <button className="p-2 hover:bg-gray-100 rounded-full">
            <MinusIcon className="w-5 h-5 text-gray-600" />
          </button>
          <button 
            className="p-2 hover:bg-gray-100 rounded-full"
            onClick={handleClose}
          >
            <Cross2Icon className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>
      <KnowledgeBase />
    </>
  );
});