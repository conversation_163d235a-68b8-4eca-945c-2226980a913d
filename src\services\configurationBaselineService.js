import axios from 'axios';

import {fetchData} from './fetch'

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (searchQuery = '') => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/list?name=${encodeURIComponent(searchQuery)}`).then(res => res.json()),
};

// 配置基线相关接口
export const configLineApi = {
  // 获取配置基线列表
  getConfigLineList: (projectId, page = 0, size = 10, keyword = '', creator = '') => {
    const params = new URLSearchParams({ 
      projectId, 
      page, 
      size,
      ...(keyword && { keyword }),
      ...(creator && { creator })
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/config-lines/all?${params}`).then(res => res.json());
  },

  // 获取单个配置基线详情
  getConfigLineDetail: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/config-lines/one/${id}`, {
      headers: {
        'Accept': 'application/json'
      }
    }).then(res => res.json()),

  // 创建配置基线
  createConfigLine: (formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/config-lines/create`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json'
      },
      body: formData
    }).then(res => res.json()),

  // 更新配置基线
  updateConfigLine: (id, formData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/config-lines/update/${id}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json'
      },
      body: formData
    }).then(res => res.json()),

  // 删除配置基线
  deleteConfigLine: (id) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/config-lines/delete/${id}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json'
      }
    }).then(res => {
      if (!res.ok) {
        throw new Error(`删除失败: ${res.status}`);
      }
      return true;
    }),
};

// 文件相关接口
export const fileApi = {
  // 预览文件
  previewFile: (fileName) => {
    const params = new URLSearchParams({
      fileName,
      bucketName: 'configline'
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/preview?${params}`, {
      headers: {
        'Accept': 'text/plain'
      }
    }).then(res => res.text());
  },

  // 下载文件
  downloadFile: (fileName) => {
    const params = new URLSearchParams({
      fileName,
      bucketName: 'configline'
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/file/download?${params}`);
  },

  // 打包下载文件
  downloadArchive: () => 
    fetch(`${fetchData["PROJECT_URL"]}/api/file/archive/more?bucketIds=2&bucketIds=3`, {
      headers: {
        'Accept': 'application/json'
      }
    }),
};

// 员工相关接口
export const employeeApi = {
  // 获取员工列表
  getEmployeeList: () => 
    fetch(`${fetchData["BASE_URL"]}/api/employees/list`).then(res => res.json()),
}; 