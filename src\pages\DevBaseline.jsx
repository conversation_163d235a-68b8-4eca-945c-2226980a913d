import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import styled from 'styled-components';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  FileTextIcon,
  TrashIcon,
  Pencil1Icon,
  EyeOpenIcon,
  ChevronDownIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import * as devBaselineService from '../services/devBaselineService';

const mockBaselines = [
  {
    id: 'BL-2024-001',
    projectId: 1,
    name: '产品1.0版本基线',
    version: 'v1.0.0',
    description: '产品1.0版本的完整配置基线',
    createdBy: '张工',
    createdAt: '2024-02-20 14:30',
    updatedAt: '2024-02-20 14:30',
    status: 'locked',
    items: [
      {
        id: 1,
        name: '需求文档',
        type: 'document',
        version: 'v1.0',
        path: '/docs/requirements.pdf'
      },
      {
        id: 2,
        name: '系统设计',
        type: 'document',
        version: 'v1.0',
        path: '/docs/design.pdf'
      },
      {
        id: 3,
        name: '源代码',
        type: 'code',
        version: 'commit-abc123',
        path: 'git://repo/main'
      }
    ],
    history: [
      {
        id: 1,
        time: '2024-02-20 14:30',
        operator: '张工',
        action: 'create',
        content: '创建基线'
      },
      {
        id: 2,
        time: '2024-02-20 14:35',
        operator: '张工',
        action: 'lock',
        content: '锁定基线'
      }
    ]
  }
];

export const DevBaseline = observer(() => {
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [selectedBaseline, setSelectedBaseline] = useState(null);
  const [newBaseline, setNewBaseline] = useState({
    projectName: '',
    name: '',
    version: '',
    createdBy: '',
    description: '',
    files: []
  });

  // 添加 baselines 状态
  const [baselines, setBaselines] = useState(mockBaselines);

  // 添加编辑状态
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingBaseline, setEditingBaseline] = useState(null);

  // 添加编辑时的配置项状态和处理函数
  const [editingConfigItem, setEditingConfigItem] = useState({
    name: '',
    type: '',
    version: '',
    path: ''
  });

  const [projects, setProjects] = useState([]); // 新增项目列表状态

  // 修改文件上传状态为数组
  const [uploadFiles, setUploadFiles] = useState([]); // 替换原来的 uploadFile 状态

  // 添加员工列表状态
  const [employees, setEmployees] = useState([]);

  // 添加错误状态管理
  const [formErrors, setFormErrors] = useState({
    name: '',
    createdBy: ''
  });

  // 添加删除确认弹窗状态
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingBaseline, setDeletingBaseline] = useState(null);

  // 添加分页状态
  const [pagination, setPagination] = useState({
    currentPage: 0,
    totalPages: 0,
    totalElements: 0
  });

  // 添加预览状态
  const [previewFile, setPreviewFile] = useState(null);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  // 添加创建人筛选状态
  const [creatorFilter, setCreatorFilter] = useState('');

  // 添加错误提示状态
  const [errorMessage, setErrorMessage] = useState('');
  const [showError, setShowError] = useState(false);

  // 添加状态控制下拉框的显示/隐藏
  const [isCreatorDropdownOpen, setIsCreatorDropdownOpen] = useState(false);
  const [isEditCreatorDropdownOpen, setIsEditCreatorDropdownOpen] = useState(false);

  // 添加 ref
  const creatorDropdownRef = useRef(null);
  const editCreatorDropdownRef = useRef(null);

  // 添加点击外部区域关闭下拉框的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (creatorDropdownRef.current && !creatorDropdownRef.current.contains(event.target)) {
        setIsCreatorDropdownOpen(false);
      }
      if (editCreatorDropdownRef.current && !editCreatorDropdownRef.current.contains(event.target)) {
        setIsEditCreatorDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 添加错误提示组件
  const ErrorMessage = ({ message }) => (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white border border-red-200 rounded-lg px-4 py-3 shadow-lg z-[100]">
      <div className="w-4 h-4 rounded-full bg-red-500 flex-shrink-0" />
      <span className="text-sm text-gray-900">{message}</span>
    </div>
  );

  // 修改错误处理函数
  const showErrorMessage = (message) => {
    setErrorMessage(message);
    setShowError(true);
    setTimeout(() => {
      setShowError(false);
      setErrorMessage('');
    }, 3000); // 3秒后自动消失
  };

  // 修改获取员工列表的函数
  const fetchEmployees = async () => {
    try {
      const employeeList = await devBaselineService.fetchEmployeeList();
      setEmployees(employeeList.map(item => ({
        id: item.id,
        name: item.name
      })));
    } catch (error) {
      console.error('获取员工列表失败:', error);
    }
  };

  // 在组件加载时获取员工列表
  useEffect(() => {
    fetchEmployees();
  }, []);

  // 添加新的 useEffect 用于初始化加载项目列表
  useEffect(() => {
    fetchProjects();
  }, []); // 空依赖数组表示只在组件挂载时执行一次

  // 修改获取项目列表的函数
  const fetchProjects = async (searchQuery = '') => {
    try {
      const username = 'songxinhao';
      const projectsData = await devBaselineService.fetchProjectList(username, searchQuery);

      // 格式化项目数据
      const projectList = projectsData.map(project => ({
        id: project.id,
        name: project.name,
        path: project.path,
        status: '进行中',
        progress: 0
      }));

      setProjects(projectList);
      if (!selectedProject && projectList.length > 0) {
        setSelectedProject(projectList[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 修改项目搜索处理函数
  const handleProjectSearch = (e) => {
    const query = e.target.value;
    setProjectSearchQuery(query);
    if (query.length === 0 || query.length >= 2) { // 只在输入为空或至少2个字符时搜索
      fetchProjects(query);
    }
  };

  // 添加时间格式化函数
  const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    // 确保时间显示到秒
    const date = new Date(dateTimeString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  };

  // 修改获取基线详情的函数
  const fetchBaselineDetail = async (id) => {
    try {
      const data = await devBaselineService.fetchBaselineDetail(id);

      // 格式化数据，添加 createdBy
      const formattedData = {
        id: data.id,
        name: data.name,
        version: data.version || '',
        createdBy: data.createdBy,
        creatorName: data.creatorName,
        createdTime: formatDateTime(data.createdAt),
        updatedTime: formatDateTime(data.updatedAt),
        description: data.description || '',
        projectId: data.projectId,
        files: data.projectFiles || []
      };

      return formattedData;
    } catch (error) {
      console.error('获取基线详情失败:', error);
      throw error;
    }
  };

  // 修改查看详情的处理函数
  const handleViewDetail = async (baseline) => {
    try {
      const detailData = await fetchBaselineDetail(baseline.id);
      setSelectedBaseline(detailData);
      setShowDetailModal(true);
    } catch (error) {
      showErrorMessage('获取基线详情失败');
    }
  };


  // 添加文件拖拽处理函数
  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setNewBaseline(prev => ({
      ...prev,
      files: [...(prev.files || []), ...files]
    }));
  };


  // 修改获取基线列表的函数
  const fetchBaselines = async (projectId, page = 0) => {
    try {
      const data = await devBaselineService.fetchBaselineList(projectId, page, 10, searchQuery, creatorFilter);

      // 格式化数据
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        version: item.version || '',
        creatorName: item.creatorName,
        createdTime: formatDateTime(item.createdAt),
        description: item.description || '',
        projectId: item.projectId,
        files: item.files || []
      }));

      // 更新列表数据和分页信息
      setBaselines(formattedData);
      setPagination({
        currentPage: data.number,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });
    } catch (error) {
      console.error('获取基线列表失败:', error);
      showErrorMessage('获取基线列表失败');
    }
  };

  // 修改选择项目的处理函数
  const handleSelectProject = (project) => {
    setSelectedProject(project);
    // fetchBaselines 会通过 useEffect 自动调用
  };


  // 修改保存编辑的函数
  const handleSaveEdit = async () => {
    if (!editingBaseline.name) {
      showErrorMessage('请填写基线名称');
      return;
    }

    try {
      // 准备请求数据
      const formData = new FormData();

      // 准备 developLine 数据
      const designLine = {
        id: editingBaseline.id,
        name: editingBaseline.name,
        status: 0,
        version: editingBaseline.version || '',
        createdBy: editingBaseline.createdBy,
        description: editingBaseline.description || '',
        projectId: editingBaseline.projectId,
        creatorName: editingBaseline.creatorName,
        projectFiles: editingBaseline.files
          .filter(file => !(file instanceof File))
          .map(file => ({
            id: file.id,
            name: file.name,
            path: file.path || '',
            type: file.type || 0,
            size: file.size || 0,
            otherId: file.otherId || 0,
            uploadTime: file.uploadTime ? new Date(file.uploadTime).toISOString() : new Date().toISOString(),
            uploaderId: file.uploaderId || 0,
            description: file.description || '',
            module: file.module || ''
          }))
      };

      formData.append('designLine', new Blob([JSON.stringify(designLine)], {
        type: 'application/json'
      }));

      // 添加新上传的文件
      const newFiles = editingBaseline.files.filter(file => file instanceof File);
      newFiles.forEach(file => {
        formData.append('files', file);
      });

      await devBaselineService.updateBaseline(editingBaseline.id, formData);

      // 更新成功后刷新列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

      // 关闭编辑弹窗
      setShowEditModal(false);
      setEditingBaseline(null);

    } catch (error) {
      console.error('更新基线失败:', error);
      showErrorMessage('更新失败: ' + (error.message || '请求出错'));
    }
  };

  // 修改编辑按钮点击处理函数
  const handleEditClick = async (baseline) => {
    try {
      const detailData = await fetchBaselineDetail(baseline.id);
      setEditingBaseline(detailData);
      setShowEditModal(true);
    } catch (error) {
      console.error('获取基线详情失败:', error);
      showErrorMessage('获取基线详情失败');
    }
  };

  // 修改删除按钮点击处理函数
  const handleDelete = (baseline) => {
    setDeletingBaseline(baseline);
    setShowDeleteModal(true);
  };

  // 修改确认删除的处理函数
  const handleConfirmDelete = async () => {
    if (!deletingBaseline) return;

    try {
      await devBaselineService.deleteBaseline(deletingBaseline.id);

      // 删除成功后刷新列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

      // 关闭删除确认弹窗
      setShowDeleteModal(false);
      setDeletingBaseline(null);

    } catch (error) {
      console.error('删除基线失败:', error);
      showErrorMessage('删除失败: ' + (error.message || '请求出错'));
    }
  };

  // 修改组件加载时的 effect，只监听 selectedProject 变化
  useEffect(() => {
    if (selectedProject) {
      fetchBaselines(selectedProject.id);
    }
  }, [selectedProject]); // 只在 selectedProject 改变时触发

  // 添加文件大小格式化函数
  const formatFileSize = (size) => {
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  // 修改文件预览处理函数
  const handlePreviewFile = async (file) => {
    try {
      const previewUrl = await devBaselineService.previewFile(file.name, 'developline');
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('获取文件预览失败:', error);
      showErrorMessage('获取文件预览失败');
    }
  };

  // 修改文件下载处理函数
  const handleDownloadFile = async (fileName) => {
    try {
      const blob = await devBaselineService.downloadFile(fileName, 'developline');
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('文件下载失败:', error);
      showErrorMessage('文件下载失败');
    }
  };

  // 修改搜索按钮的处理函数
  const handleSearch = async () => {
    if (!selectedProject) {
      showErrorMessage('请先选择项目');
      return;
    }

    try {
      const data = await devBaselineService.searchBaselines(
        selectedProject.id,
        0,
        10,
        searchQuery.trim(),
        creatorFilter
      );

      // 格式化数据
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        version: item.version || '',
        creatorName: item.creatorName,
        createdTime: formatDateTime(item.createdAt),
        description: item.description || '',
        projectId: item.projectId,
        files: item.files || []
      }));

      // 更新列表数据和分页信息
      setBaselines(formattedData);
      setPagination({
        currentPage: data.number,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });

    } catch (error) {
      console.error('搜索失败:', error);
      showErrorMessage('搜索失败: ' + (error.message || '请求出错'));
    }
  };

  // 修改重置按钮的处理函数
  const handleReset = async () => {
    // 清空搜索框和创建人筛选
    setSearchQuery('');
    setCreatorFilter('');

    if (!selectedProject) {
      return;
    }

    try {
      const data = await devBaselineService.searchBaselines(selectedProject.id, 0, 10);

      // 格式化数据
      const formattedData = data.content.map(item => ({
        id: item.id,
        name: item.name,
        version: item.version || '',
        creatorName: item.creatorName,
        createdTime: formatDateTime(item.createdAt),
        description: item.description || '',
        projectId: item.projectId,
        files: item.files || []
      }));

      // 更新列表数据和分页信息
      setBaselines(formattedData);
      setPagination({
        currentPage: data.number,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });

    } catch (error) {
      console.error('重置搜索失败:', error);
      showErrorMessage('重置搜索失败: ' + (error.message || '请求出错'));
    }
  };

  // 修改编辑弹窗中删除文件的处理函数
  const handleRemoveEditingFile = (fileId) => {
    setEditingBaseline(prev => ({
      ...prev,
      files: prev.files.filter(file => {
        // 如果是新上传的文件，使用 name 来比较
        if (file instanceof File) {
          return file.name !== fileId;
        }
        // 如果是已有的文件，使用 id 来比较
        return file.id !== fileId;
      })
    }));
  };

  // 添加处理页码变化的函数
  const handlePageChange = (newPage) => {
    if (selectedProject) {
      setPagination(prev => ({ ...prev, currentPage: newPage }));
      fetchBaselines(selectedProject.id, newPage);
    }
  };

  // 添加一个验证函数
  const validateForm = () => {
    const errors = {};
    if (!newBaseline.name) {
      errors.name = '请输入基线名称';
    }
    if (!newBaseline.createdBy) {
      errors.createdBy = '请选择创建人';
    }
    return errors;
  };

  // 修改确定按钮的处理函数
  const handleConfirmCreate = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      // 准备请求数据
      const formData = new FormData();

      // 添加开发基线数据
      const developLine = {
        name: newBaseline.name,
        version: newBaseline.version,
        createdBy: newBaseline.createdBy,
        description: newBaseline.description,
        projectId: selectedProject.id
      };

      formData.append('developLine', new Blob([JSON.stringify(developLine)], {
        type: 'application/json'
      }));

      // 添加文件数据
      if (newBaseline.files && newBaseline.files.length > 0) {
        newBaseline.files.forEach(file => {
          formData.append('files', file);
        });
      }

      await devBaselineService.createBaseline(formData);

      // 关闭弹窗
      setShowNewModal(false);

      // 重置表单
      setNewBaseline({
        projectName: '',
        name: '',
        version: '',
        createdBy: '',
        description: '',
        files: []
      });

      // 刷新基线列表
      if (selectedProject) {
        fetchBaselines(selectedProject.id);
      }

    } catch (error) {
      console.error('创建开发基线失败:', error);
      showErrorMessage('创建开发基线失败，请重试');
    }
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* Project List Sidebar */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-full">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {projects
            .filter(project =>
              project.name.toLowerCase().includes(projectSearchQuery.toLowerCase())
            )
            .map(project => (
              <div
                key={project.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
                  }`}
                onClick={() => handleSelectProject(project)}
              >
                <div className="font-medium">{project.name}</div>
              </div>
            ))}
        </div>
      </div>

      {/* Configuration Baseline Content */}
      {selectedProject ? (
        <div className="flex-1 flex flex-col h-full">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">开发基线管理</p>
            </div>

          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex items-center gap-4">

              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索基线名称、版本..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>

              {/* 创建人筛选 */}
              <div className="w-48">
                <select
                  value={creatorFilter}
                  onChange={(e) => setCreatorFilter(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">全部创建人</option>
                  {employees.map(employee => (
                    <option key={employee.id} value={employee.id}>
                      {employee.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* 搜索按钮 */}
              <Button
                onClick={handleSearch}
                className="px-4 py-2"
                style={{ backgroundColor: '#007bff', color: 'white' }}
              >
                搜索
              </Button>
              <Button
                onClick={handleReset}
                className="px-4 py-2"
                variant="outline"
              >
                重置
              </Button>
              <Button
                className="flex items-center gap-1"
                onClick={() => setShowNewModal(true)}
                style={{ backgroundColor: '#007bff', color: 'white' }}
              >
                <PlusIcon className="w-4 h-4" />
                创建开发基线
              </Button>
            </div>
          </div>

          <div className="flex-1 bg-white rounded-lg shadow-sm overflow-hidden flex flex-col">
            <div className="p-6 border-b">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">配置基线列表</h2>
              </div>
            </div>

            <div className="flex-1 overflow-auto">
              <table className="w-full border-collapse">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">基线名称</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">创建人</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">基线描述</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">基线版本</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b">创建时间</th>
                    <th className="px-4 py-2 text-right text-sm font-medium text-gray-600 border-b">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {baselines.map((baseline) => (
                    <tr key={baseline.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">{baseline.name}</td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">{baseline.creatorName}</td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">
                        {baseline.description ? (
                          <span className="line-clamp-1">{baseline.description}</span>
                        ) : (
                          <span className="text-gray-400">暂无描述</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">{baseline.version}</td>
                      <td className="px-4 py-3 text-sm text-gray-500 border-b">{baseline.createdTime}</td>
                      <td className="px-4 py-3 text-right text-sm font-medium space-x-2 border-b">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetail(baseline)}
                        >
                          <EyeOpenIcon className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditClick(baseline)}
                        >
                          <Pencil1Icon className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600"
                          onClick={() => handleDelete(baseline)}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 添加分页器 */}
            <div className="flex justify-end items-center p-4 border-t">
              <div className="flex items-center gap-2">
                <span>共 {pagination.totalElements} 条记录</span>
                <Button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={pagination.currentPage === 0}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  上一页
                </Button>

                {/* 显示所有页码 */}
                {Array.from({ length: pagination.totalPages }, (_, index) => (
                  <Button
                    key={index}
                    onClick={() => handlePageChange(index)}
                    className={`px-3 py-1 border rounded ${pagination.currentPage === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                  >
                    {index + 1}
                  </Button>
                ))}

                <Button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={pagination.currentPage >= pagination.totalPages - 1}
                  className="px-3 py-1 border rounded hover:bg-gray-200"
                >
                  下一页
                </Button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看配置基线</p>
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {showDetailModal && selectedBaseline && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看开发基线</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      项目名称
                    </label>
                    <div className="text-sm">{selectedProject?.name}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      基线名称
                    </label>
                    <div className="text-sm">{selectedBaseline.name}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      创建人
                    </label>
                    <div className="text-sm">{selectedBaseline.creatorName}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      创建时间
                    </label>
                    <div className="text-sm">{selectedBaseline.createdTime}</div>
                  </div>
                </div>

                {/* 基线版本 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线版本
                  </label>
                  <input
                    type="text"
                    value={selectedBaseline.version}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>

                {/* 基线描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    基线描述
                  </label>
                  <div className="bg-gray-50 p-4 rounded-lg text-sm">
                    {selectedBaseline.description || '暂无描述'}
                  </div>
                </div>

                {/* 文件列表 */}
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">
                    文件列表
                  </label>
                  <div className="space-y-2">
                    {(selectedBaseline.files || []).map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="font-medium text-sm">{file.name}</span>
                          
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-gray-500">{formatDateTime(file.uploadTime)}</div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePreviewFile(file)}
                            className="text-blue-600"
                          >
                            <EyeOpenIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDownloadFile(file.name)}
                            className="text-blue-600"
                          >
                            <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* New Baseline Modal */}
      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">创建开发基线</h3>
              <button
                onClick={() => setShowNewModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">

                {/* 项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* 基线名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newBaseline.name}
                      onChange={(e) => {
                        setNewBaseline(prev => ({ ...prev, name: e.target.value }));
                        if (formErrors.name) {
                          setFormErrors(prev => ({ ...prev, name: '' }));
                        }
                      }}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${formErrors.name ? 'border-red-500' : ''
                        }`}
                      placeholder="请输入基线名称"
                    />
                    {formErrors.name && (
                      <div className="mt-1 text-sm text-red-500">
                        {formErrors.name}
                      </div>
                    )}
                  </div>

                  {/* 创建人 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={creatorDropdownRef}>
                      <div
                        onClick={() => setIsCreatorDropdownOpen(!isCreatorDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          formErrors.createdBy 
                            ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <span className={newBaseline.createdBy ? 'text-gray-900' : 'text-gray-500'}>
                          {newBaseline.createdBy 
                            ? employees.find(emp => emp.id === newBaseline.createdBy)?.name 
                            : '请选择创建人'}
                        </span>
                        <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${isCreatorDropdownOpen ? 'transform rotate-180' : ''}`} />
                      </div>
                      
                      {isCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setNewBaseline(prev => ({
                                    ...prev,
                                    createdBy: employee.id
                                  }));
                                  if (formErrors.createdBy) {
                                    setFormErrors(prev => ({
                                      ...prev,
                                      createdBy: ''
                                    }));
                                  }
                                  setIsCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newBaseline.createdBy === employee.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {formErrors.createdBy && (
                      <div className="mt-1 text-sm text-red-500">
                        {formErrors.createdBy}
                      </div>
                    )}
                  </div>
                </div>

                {/* 基线版本 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线版本
                  </label>
                  <input
                    type="text"
                    value={newBaseline.version}
                    onChange={(e) => setNewBaseline(prev => ({ ...prev, version: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线版本，例如：v1.0.0"
                  />
                </div>

                {/* 基线描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线描述
                  </label>
                  <textarea
                    value={newBaseline.description}
                    onChange={(e) => setNewBaseline(prev => ({ ...prev, description: e.target.value }))}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线描述..."
                  />
                </div>

              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleConfirmCreate}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {showEditModal && editingBaseline && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑基线</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* 项目名称 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称
                  </label>
                  <input
                    type="text"
                    value={selectedProject?.name || ''}
                    disabled
                    className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* 基线名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingBaseline.name}
                      onChange={(e) => setEditingBaseline({ ...editingBaseline, name: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入基线名称"
                    />
                  </div>

                  {/* 创建人 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建人
                    </label>
                    <div className="relative" ref={editCreatorDropdownRef}>
                      <div
                        onClick={() => setIsEditCreatorDropdownOpen(!isEditCreatorDropdownOpen)}
                        className="w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between border-gray-300 hover:border-gray-400"
                      >
                        <span className={editingBaseline.createdBy ? 'text-gray-900' : 'text-gray-500'}>
                          {editingBaseline.createdBy 
                            ? employees.find(emp => emp.id === editingBaseline.createdBy)?.name 
                            : '请选择创建人'}
                        </span>
                        <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${isEditCreatorDropdownOpen ? 'transform rotate-180' : ''}`} />
                      </div>
                      
                      {isEditCreatorDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {employees.map(employee => (
                              <div
                                key={employee.id}
                                onClick={() => {
                                  setEditingBaseline({
                                    ...editingBaseline,
                                    createdBy: employee.id,
                                    creatorName: employee.name
                                  });
                                  setIsEditCreatorDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  editingBaseline.createdBy === employee.id ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {employee.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                  {/* 基线版本 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      基线版本
                    </label>
                    <input
                      type="text"
                      value={editingBaseline.version}
                      onChange={(e) => setEditingBaseline({ ...editingBaseline, version: e.target.value })}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入基线版本，例如：v1.0.0"
                    />
                  </div>
                {/* 描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    基线描述
                  </label>
                  <textarea
                    value={editingBaseline.description}
                    onChange={(e) => setEditingBaseline({ ...editingBaseline, description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入基线描述..."
                  />
                </div>

                {/* 文件列表 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文件列表
                  </label>
                  <div className="space-y-2">
                    {(editingBaseline.files || []).map((file) => (
                      <div key={file instanceof File ? file.name : file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <FileTextIcon className="w-4 h-4 text-gray-400" />
                          <span className="font-medium">{file.name}</span>
                          <span className="text-sm text-gray-500">
                            ({formatFileSize(file instanceof File ? file.size : file.size)})
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="text-sm text-gray-500">
                            {file instanceof File ? '新上传' : file.uploadTime}
                          </div>
                          <div className="flex gap-1">
                            {!(file instanceof File) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handlePreviewFile(file)}
                                className="text-blue-600"
                              >
                                <EyeOpenIcon className="w-4 h-4" />
                              </Button>
                            )}
                            <button
                              onClick={() => handleRemoveEditingFile(file instanceof File ? file.name : file.id)}
                              className="p-1 hover:bg-gray-200 rounded-full"
                            >
                              <Cross2Icon className="w-4 h-4 text-gray-500" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* 文件上传区域 */}
                  <div className="mt-4">
                    <label htmlFor="edit-file-upload" className="block cursor-pointer">
                      <div
                        className="mt-1 flex flex-col items-center justify-center w-full h-32 px-4 transition bg-white border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400"
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <svg className="w-8 h-8 mb-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          <p className="mb-2 text-sm text-gray-500">
                            点击或拖拽文件到这里
                          </p>
                        </div>
                      </div>
                    </label>
                    <input
                      id="edit-file-upload"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => {
                        const files = Array.from(e.target.files || []);
                        setEditingBaseline(prev => ({
                          ...prev,
                          files: [...prev.files, ...files]
                        }));
                        e.target.value = ''; // 清空 input 值，允许重复上传相同文件
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleSaveEdit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-2">确认删除</h3>
              <p className="text-gray-600">
                确定要删除基线 "{deletingBaseline?.name}" 吗？此操作不可恢复。
              </p>
            </div>
            <div className="p-4 bg-gray-50 flex justify-end gap-2 rounded-b-lg">
              <Button
                variant="outline"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeletingBaseline(null);
                }}
              >
                取消
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                style={{ backgroundColor: '#FF0000', color: '#FFFFFF' }}
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreviewModal && previewFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[70]">
          <div className="bg-white rounded-lg shadow-xl w-[90vw] h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">{previewFile.name}</h3>
                <p className="text-sm text-gray-500">
                  {formatFileSize(previewFile.size)}
                </p>
              </div>
              <button
                onClick={() => {
                  setShowPreviewModal(false);
                  setPreviewFile(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="flex-1 overflow-auto p-4">
              {previewFile.type?.startsWith('image/') ? (
                <img
                  src={previewFile.previewUrl}
                  alt={previewFile.name}
                  className="max-w-full h-auto mx-auto"
                />
              ) : previewFile.type?.startsWith('text/') || previewFile.type?.includes('javascript') || previewFile.type?.includes('json') ? (
                <pre className="whitespace-pre-wrap font-mono text-sm p-4 bg-gray-50 rounded-lg">
                  {previewFile.previewUrl}
                </pre>
              ) : previewFile.type?.includes('pdf') ? (
                <iframe
                  src={previewFile.previewUrl}
                  title={previewFile.name}
                  className="w-full h-full border-0"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <p>该文件类型暂不支持预览，请下载后查看</p>
                </div>
              )}
            </div>
            <div className="p-4 border-t flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowPreviewModal(false);
                  setPreviewFile(null);
                }}
              >
                关闭
              </Button>
              <Button
                onClick={() => window.open(previewFile.previewUrl, '_blank')}
              >
                下载
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 错误提示 */}
      {showError && <ErrorMessage message={errorMessage} />}
    </div>
  );
});