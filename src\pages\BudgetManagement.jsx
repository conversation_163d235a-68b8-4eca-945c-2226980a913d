import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  TrashIcon,
  Pencil1Icon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';

const mockProjects = [
  { id: 1, name: '企业门户网站重构', status: '进行中', progress: 75 },
  { id: 2, name: 'APP用户体验优化', status: '规划中', progress: 30 },
  { id: 3, name: '数据中台建设', status: '进行中', progress: 45 },
  { id: 4, name: '智能客服系统', status: '已完成', progress: 100 },
  { id: 5, name: '安全运维平台', status: '进行中', progress: 60 }
];

const budgetCategories = [
  { 
    id: 'labor', 
    name: '人工成本',
    unit: '人天',
    items: [
      { id: 'dev', name: '开发人员' },
      { id: 'test', name: '测试人员' },
      { id: 'pm', name: '项目经理' },
      { id: 'design', name: '设计人员' }
    ]
  },
  { 
    id: 'equipment', 
    name: '设备成本',
    items: [
      { id: 'server', name: '服务器' },
      { id: 'computer', name: '开发电脑' },
      { id: 'network', name: '网络设备' }
    ]
  }
];

const mockBudgets = [
  {
    id: 'BUD-001',
    projectId: 1,
    createdAt: '2024-02-20',
    createdBy: '张三',
    status: 'draft',
    items: [
      { category: 'labor', itemId: 'dev', quantity: 100 },
      { category: 'labor', itemId: 'test', quantity: 30 },
      { category: 'equipment', itemId: 'server', quantity: 2 }
    ]
  }
];

export const BudgetManagement = observer(() => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewBudgetModal, setShowNewBudgetModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [newBudget, setNewBudget] = useState({
    items: budgetCategories.map(category => ({
      category: category.id,
      details: category.items.map(item => ({
        itemId: item.id,
        quantity: 0
      }))
    })),
    materials: []
  });

  const handleCreateBudget = () => {
    if (!selectedProject) {
      alert('请选择项目');
      return;
    }
    setShowNewBudgetModal(false);
    setNewBudget({
      items: budgetCategories.map(category => ({
        category: category.id,
        details: category.items.map(item => ({
          itemId: item.id,
          quantity: 0
        }))
      })),
      materials: []
    });
  };

  const filteredProjects = mockProjects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex-1 p-6 pt-16 overflow-y-auto bg-gray-50">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">预算成本</h1>
          <p className="text-gray-500">管理项目预算成本</p>
        </div>
        <Button 
          className="flex items-center gap-1"
          onClick={() => setShowNewBudgetModal(true)}
        >
          <PlusIcon className="w-4 h-4" />
          创建预算
        </Button>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="搜索项目..."
            className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {filteredProjects.map(project => {
          const projectBudgets = mockBudgets.filter(budget => budget.projectId === project.id);
          
          return (
            <div
              key={project.id}
              className="bg-white p-4 rounded-lg shadow-sm"
            >
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="font-medium">{project.name}</h3>
                  <div className="text-sm text-gray-500">状态: {project.status}</div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-48">
                    <div className="flex justify-between text-sm mb-1">
                      <span>进度</span>
                      <span>{project.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      setSelectedProject(project);
                      setShowNewBudgetModal(true);
                    }}
                  >
                    创建预算
                  </Button>
                </div>
              </div>

              {projectBudgets.length > 0 && (
                <div className="mt-4 space-y-2">
                  <div className="text-sm font-medium text-gray-700">预算列表</div>
                  {projectBudgets.map(budget => (
                    <div key={budget.id} className="p-3 bg-gray-50 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <div className="font-medium">预算编号：{budget.id}</div>
                          <div className="text-sm text-gray-500">
                            创建时间：{budget.createdAt} | 创建人：{budget.createdBy}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Pencil1Icon className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-1">
                        {budget.items.map((item, index) => {
                          const category = budgetCategories.find(c => c.id === item.category);
                          const budgetItem = category?.items.find(i => i.id === item.itemId);
                          return (
                            <div key={index} className="flex items-center justify-between text-sm text-gray-600">
                              <span>{category?.name} - {budgetItem?.name}</span>
                              <span>{item.quantity} {category?.unit || '个'}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* New Budget Modal */}
      {showNewBudgetModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px] max-h-[80vh] overflow-y-auto">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold">创建项目成本预算</h3>
              <button
                onClick={() => {
                  setShowNewBudgetModal(false);
                  setSelectedProject(null);
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    选择项目 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={selectedProject?.id || ''}
                    onChange={(e) => {
                      const project = mockProjects.find(p => p.id === parseInt(e.target.value));
                      setSelectedProject(project);
                    }}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择项目</option>
                    {mockProjects.map(project => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                </div>

                {budgetCategories.map(category => (
                  <div key={category.id} className="space-y-2">
                    <h5 className="font-medium">{category.name}</h5>
                    <div className="space-y-2">
                      {category.items.map(item => (
                        <div key={item.id} className="flex items-center gap-4">
                          <div className="flex-1">
                            <label className="text-sm font-medium text-gray-700">
                              {item.name}
                            </label>
                          </div>
                          <div className="w-32 flex items-center gap-2">
                            <input
                              type="number"
                              min="0"
                              value={newBudget.items
                                .find(i => i.category === category.id)
                                ?.details.find(d => d.itemId === item.id)
                                ?.quantity || 0}
                              onChange={(e) => {
                                const value = parseInt(e.target.value) || 0;
                                const newItems = [...newBudget.items];
                                const categoryItem = newItems.find(i => i.category === category.id);
                                const detail = categoryItem.details.find(d => d.itemId === item.id);
                                detail.quantity = value;
                                setNewBudget({ ...newBudget, items: newItems });
                              }}
                              className="w-full px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <span className="text-gray-500 w-12 text-sm">
                              {category.unit || '个'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                {/* Custom Material Costs */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h5 className="font-medium">材料成本</h5>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newMaterials = [...(newBudget.materials || [])];
                        newMaterials.push({ name: '', quantity: 0, unit: '个' });
                        setNewBudget({ ...newBudget, materials: newMaterials });
                      }}
                    >
                      <PlusIcon className="w-4 h-4 mr-1" />
                      添加材料
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {newBudget.materials?.map((material, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <input
                          type="text"
                          value={material.name}
                          onChange={(e) => {
                            const newMaterials = [...(newBudget.materials || [])];
                            newMaterials[index].name = e.target.value;
                            setNewBudget({ ...newBudget, materials: newMaterials });
                          }}
                          placeholder="材料名称"
                          className="flex-1 px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="number"
                          min="0"
                          value={material.quantity}
                          onChange={(e) => {
                            const newMaterials = [...(newBudget.materials || [])];
                            newMaterials[index].quantity = parseInt(e.target.value) || 0;
                            setNewBudget({ ...newBudget, materials: newMaterials });
                          }}
                          className="w-20 px-3 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <input
                          type="text"
                          value={material.unit}
                          onChange={(e) => {
                            const newMaterials = [...(newBudget.materials || [])];
                            newMaterials[index].unit = e.target.value;
                            setNewBudget({ ...newBudget, materials: newMaterials });
                          }}
                          placeholder="单位"
                          className="w-16 px-2 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600"
                          onClick={() => {
                            const newMaterials = [...(newBudget.materials || [])];
                            newMaterials.splice(index, 1);
                            setNewBudget({ ...newBudget, materials: newMaterials });
                          }}
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-4 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowNewBudgetModal(false);
                  setSelectedProject(null);
                }}
              >
                取消
              </Button>
              <Button onClick={handleCreateBudget}>
                创建预算
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});