import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  Cross2Icon,
  ChevronRightIcon,
  FileTextIcon,
  Pencil1Icon,
  TrashIcon,
  EyeOpenIcon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { requirementsApi, projectApi } from '../services/requirementsBreakdownService';

// 配置选项常量
const categoryOptions = [
  { value: 'user', label: '用户需求' },
  { value: 'project', label: '项目需求' },
  { value: 'functional', label: '功能需求' },
  { value: 'quality', label: '质量需求' }
];

const groupOptions = [
  { value: 'core', label: '核心功能' },
  { value: 'business', label: '业务功能' },
  { value: 'system', label: '系统功能' },
  { value: 'platform', label: '平台功能' }
];

const priorityOptions = [
  { value: 'low', label: '低' },
  { value: 'medium', label: '中' },
  { value: 'high', label: '高' }
];

// 数据映射工具函数
const createMappingUtils = () => {
  const categoryMap = { 'user': 0, 'project': 1, 'functional': 2, 'quality': 3 };
  const groupMap = { 'core': 0, 'business': 1, 'system': 2, 'platform': 3 };
  const priorityMap = { 'low': 0, 'medium': 1, 'high': 2 };

  const reverseCategoryMap = { 0: 'user', 1: 'project', 2: 'functional', 3: 'quality' };
  const reverseGroupMap = { 0: 'core', 1: 'business', 2: 'system', 3: 'platform' };
  const reversePriorityMap = { 0: 'low', 1: 'medium', 2: 'high' };

  return {
    categoryMap, groupMap, priorityMap,
    reverseCategoryMap, reverseGroupMap, reversePriorityMap
  };
};

export const RequirementsBreakdown = observer(() => {
  // 项目相关状态
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectSearchQuery, setProjectSearchQuery] = useState('');

  // 需求列表相关状态
  const [requirements, setRequirements] = useState([]);
  const [expandedRequirements, setExpandedRequirements] = useState(new Set());
  const [childRequirements, setChildRequirements] = useState({});

  // 搜索和过滤状态
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');

  // 分页状态
  const [page, setPage] = useState(0);
  const [size] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);

  // 模态框状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // 当前操作的需求
  const [selectedRequirement, setSelectedRequirement] = useState(null);
  const [editingRequirement, setEditingRequirement] = useState(null);
  const [deletingRequirement, setDeletingRequirement] = useState(null);

  // 新建需求表单状态
  const [newRequirement, setNewRequirement] = useState({
    name: '',
    category: '',
    group: '',
    priority: '',
    description: '',
    input: '',
    process: '',
    output: '',
    parentId: null
  });

  // 表单验证错误
  const [errors, setErrors] = useState({});

  // 获取映射工具
  const mappingUtils = createMappingUtils();

  // 简化的下拉框状态管理
  const [isPriorityDropdownOpen, setIsPriorityDropdownOpen] = useState(false);
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const [isGroupDropdownOpen, setIsGroupDropdownOpen] = useState(false);
  const priorityDropdownRef = useRef(null);
  const categoryDropdownRef = useRef(null);
  const groupDropdownRef = useRef(null);

  // 通用数据格式化函数
  const formatRequirementData = (data) => {
    if (!data) return [];
    const items = Array.isArray(data) ? data : [data];
    return items.map(item => ({
      ...item,
      category: mappingUtils.reverseCategoryMap[item.category] || '',
      group: mappingUtils.reverseGroupMap[item.group] || '',
      priority: mappingUtils.reversePriorityMap[item.priority] || ''
    }));
  };

  // 通用API请求处理函数
  const handleApiRequest = async (apiCall, onSuccess, onError) => {
    try {
      const result = await apiCall();
      onSuccess(result);
    } catch (error) {
      console.error('API请求失败:', error);
      onError && onError(error);
    }
  };

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (priorityDropdownRef.current && !priorityDropdownRef.current.contains(event.target)) {
        setIsPriorityDropdownOpen(false);
      }
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target)) {
        setIsCategoryDropdownOpen(false);
      }
      if (groupDropdownRef.current && !groupDropdownRef.current.contains(event.target)) {
        setIsGroupDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 获取项目列表
  const fetchProjects = async (searchName = '') => {
    await handleApiRequest(
      () => projectApi.getProjectList(searchName),
      (data) => {
        setProjects(data || []);
        if (!selectedProject && data?.length > 0) {
          setSelectedProject(data[0]);
        }
      },
      () => setProjects([])
    );
  };

  // 项目搜索处理
  const handleProjectSearch = (e) => {
    const searchValue = e.target.value;
    setProjectSearchQuery(searchValue);
    fetchProjects(searchValue);
  };

  // 获取需求数据并格式化
  const fetchAndFormatRequirements = async (projectId, pageNum = 0) => {
    const pageData = await requirementsApi.getRootRequirements(projectId, pageNum, size);
    const total = pageData.total || pageData.totalElements || pageData.content?.length || 0;
    setTotalElements(total);
    setTotalPages(pageData.totalPages || Math.ceil(total / size));

    const reqData = pageData.content || pageData.records || pageData.data || [];
    const formattedData = formatRequirementData(reqData);
    setRequirements(formattedData);
    setPage(pageNum);
    collapseAllExceptRootNodes();
  };

  // 初始化数据
  useEffect(() => {
    const initializeData = async () => {
      await handleApiRequest(
        () => projectApi.getProjectList(),
        async (data) => {
          if (data?.length > 0) {
            setProjects(data);
            const firstProject = data[0];
            setSelectedProject(firstProject);
            await fetchAndFormatRequirements(firstProject.id, 0);
          }
        }
      );
    };

    initializeData();
  }, [size]); // 添加size依赖

  // 搜索需求
  const fetchRequirements = async (projectId, pageNum) => {
    const searchParams = {
      name: searchQuery,
      category: selectedCategory ? mappingUtils.categoryMap[selectedCategory] : '',
      group: selectedGroup ? mappingUtils.groupMap[selectedGroup] : '',
      priority: selectedPriority ? mappingUtils.priorityMap[selectedPriority] : ''
    };

    await handleApiRequest(
      () => requirementsApi.searchRequirements(projectId, pageNum, size, searchParams),
      (pageData) => {
        const total = pageData.total || pageData.totalElements || requirements.length;
        setTotalElements(total);
        setTotalPages(Math.ceil(total / size));

        const data = pageData.content || pageData.records || pageData.data || [];
        const formattedData = formatRequirementData(data);
        setRequirements(formattedData);
        collapseAllExceptRootNodes();
      },
      () => {
        setRequirements([]);
        setTotalElements(0);
      }
    );
  };

  // 选择项目
  const handleProjectSelect = async (project) => {
    setSelectedProject(project);
    await fetchAndFormatRequirements(project.id, 0);
  };

  // 查看需求详情
  const handleViewDetail = async (requirement) => {
    await handleApiRequest(
      async () => {
        const data = await requirementsApi.getRequirementDetail(requirement.id);
        let parentName = '';
        if (data.parentId > 0) {
          const parentData = await requirementsApi.getRequirementDetail(data.parentId);
          parentName = parentData.name;
        }
        return { ...data, parentName };
      },
      (data) => {
        const formattedData = formatRequirementData([data])[0];
        formattedData.parentName = data.parentName;
        setSelectedRequirement(formattedData);
        setShowDetailModal(true);
      },
      () => alert('获取需求详情失败，请重试')
    );
  };

  // 表单验证
  const validateRequirement = (requirement) => {
    const newErrors = {};
    if (!requirement.name) newErrors.name = '请填写需求名称';
    if (!requirement.category) newErrors.category = '请选择需求类型';
    if (!requirement.group) newErrors.group = '请选择功能分组';
    if (!requirement.priority) newErrors.priority = '请选择优先级';
    return newErrors;
  };

  // 重置新建需求表单
  const resetNewRequirement = () => {
    setNewRequirement({
      name: '',
      category: '',
      group: '',
      priority: '',
      description: '',
      input: '',
      process: '',
      output: '',
      parentId: null,
      parentName: ''
    });
  };

  // 创建需求
  const handleCreateRequirement = async () => {
    const newErrors = validateRequirement(newRequirement);
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    const requirementData = {
      name: newRequirement.name,
      category: mappingUtils.categoryMap[newRequirement.category],
      group: mappingUtils.groupMap[newRequirement.group],
      priority: mappingUtils.priorityMap[newRequirement.priority],
      description: newRequirement.description || '',
      input: newRequirement.input || '',
      process: newRequirement.process || '',
      output: newRequirement.output || '',
      projectId: selectedProject.id,
      parentId: newRequirement.parentId || 0
    };

    await handleApiRequest(
      () => requirementsApi.createRequirement(requirementData),
      async () => {
        // 如果是子需求，更新子需求列表
        if (newRequirement.parentId) {
          const childData = await requirementsApi.getChildRequirements(newRequirement.parentId);
          const formattedChildData = formatRequirementData(childData);
          setChildRequirements({
            ...childRequirements,
            [newRequirement.parentId]: formattedChildData
          });
        }

        // 刷新需求列表
        await fetchAndFormatRequirements(selectedProject.id, 0);
        setShowNewModal(false);
        resetNewRequirement();
      },
      () => alert('创建需求失败，请重试')
    );
  };

  // 打开新建需求模态框
  const openNewModal = () => {
    setErrors({});
    resetNewRequirement();
    setShowNewModal(true);
  };

  // 添加子需求
  const handleAddChild = async (requirement) => {
    await handleApiRequest(
      () => requirementsApi.getRequirementDetail(requirement.id),
      (data) => {
        setNewRequirement({
          name: '',
          category: mappingUtils.reverseCategoryMap[data.category] || '',
          group: mappingUtils.reverseGroupMap[data.group] || '',
          priority: '',
          description: '',
          input: '',
          process: '',
          output: '',
          parentId: requirement.id,
          parentName: requirement.name
        });
        setErrors({});
        setShowNewModal(true);
      },
      () => alert('获取父需求详情失败，请重试')
    );
  };

  // 编辑需求
  const handleEditRequirement = async (requirement) => {
    await handleApiRequest(
      async () => {
        const data = await requirementsApi.getRequirementDetail(requirement.id);
        let parentName = '';
        if (data.parentId > 0) {
          const parentData = await requirementsApi.getRequirementDetail(data.parentId);
          parentName = parentData.name;
        }
        return { ...data, parentName };
      },
      (data) => {
        const formattedData = formatRequirementData([data])[0];
        formattedData.parentName = data.parentName;
        setErrors({});
        setEditingRequirement(formattedData);
        setShowEditModal(true);
      },
      () => alert('获取需求详情失败，请重试')
    );
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    const newErrors = validateRequirement(editingRequirement);
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    const updateData = {
      name: editingRequirement.name,
      category: mappingUtils.categoryMap[editingRequirement.category],
      group: mappingUtils.groupMap[editingRequirement.group],
      priority: mappingUtils.priorityMap[editingRequirement.priority],
      description: editingRequirement.description || '',
      input: editingRequirement.input || '',
      process: editingRequirement.process || '',
      output: editingRequirement.output || '',
      projectId: selectedProject.id,
      parentId: editingRequirement.parentId || 0
    };

    await handleApiRequest(
      () => requirementsApi.updateRequirement(editingRequirement.id, updateData),
      async () => {
        // 更新父级子需求列表
        if (editingRequirement.parentId) {
          const childData = await requirementsApi.getChildRequirements(editingRequirement.parentId);
          const formattedChildData = formatRequirementData(childData);
          setChildRequirements({
            ...childRequirements,
            [editingRequirement.parentId]: formattedChildData
          });
        }

        // 更新当前需求的子需求列表
        if (expandedRequirements.has(editingRequirement.id)) {
          const childData = await requirementsApi.getChildRequirements(editingRequirement.id);
          const formattedChildData = formatRequirementData(childData);
          setChildRequirements({
            ...childRequirements,
            [editingRequirement.id]: formattedChildData
          });
        }

        // 刷新需求列表
        await fetchAndFormatRequirements(selectedProject.id, 0);
        setShowEditModal(false);
        setEditingRequirement(null);
      },
      () => alert('更新需求失败，请重试')
    );
  };

  // 删除需求
  const handleDeleteRequirement = (requirement) => {
    setDeletingRequirement(requirement);
    setShowDeleteModal(true);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    const parentId = deletingRequirement.parentId;

    await handleApiRequest(
      () => requirementsApi.deleteRequirement(deletingRequirement.id),
      async () => {
        // 更新父级子需求列表
        if (parentId) {
          const childData = await requirementsApi.getChildRequirements(parentId);
          const formattedChildData = formatRequirementData(childData);
          setChildRequirements({
            ...childRequirements,
            [parentId]: formattedChildData
          });
        }

        // 刷新需求列表
        await fetchAndFormatRequirements(selectedProject.id, 0);
        setShowDeleteModal(false);
        setDeletingRequirement(null);
      },
      () => alert('删除需求失败，请重试')
    );
  };

  // 重置搜索条件
  const handleReset = async () => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedGroup('');
    setSelectedPriority('');
    await fetchAndFormatRequirements(selectedProject.id, 0);
  };

  // 分页处理
  const handlePageChange = (newPage) => {
    if (newPage < 0 || newPage >= totalPages) return;
    fetchRequirements(selectedProject.id, newPage);
    collapseAllExceptRootNodes();
  };

  // 折叠所有非根节点
  const collapseAllExceptRootNodes = () => {
    setExpandedRequirements(new Set());
  };

  const projectListJsx = (
    <div className="flex-1 overflow-y-auto">
      {projects.map(project => (
        <div
          key={project.id}
          className={`p-4 cursor-pointer hover:bg-gray-50 ${selectedProject?.id === project.id ? 'bg-blue-50 border-blue-200 border' : ''
            }`}
          onClick={() => handleProjectSelect(project)}
        >
          <div className="font-medium">{project.name}</div>
          <div className="mt-2 text-sm">
            <span className={`px-2 py-1 rounded-full text-xs ${project.status === 0 ? 'bg-gray-100 text-gray-600' :
              project.status === 1 ? 'bg-blue-100 text-blue-600' :
                'bg-green-100 text-green-600'
              }`}>
              {project.status === 0 ? '未开始' :
                project.status === 1 ? '进行中' :
                  '已结束'}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  const handleRequirementClick = async (requirement) => {
    const newExpanded = new Set(expandedRequirements);

    if (newExpanded.has(requirement.id)) {
      newExpanded.delete(requirement.id);
      setExpandedRequirements(newExpanded);
      return;
    }

    try {
      const data = await requirementsApi.getChildRequirements(requirement.id);

      const reverseCategoryMap = { 0: 'user', 1: 'project', 2: 'functional', 3: 'quality' };
      const reverseGroupMap = { 0: 'core', 1: 'business', 2: 'system', 3: 'platform' };
      const reversePriorityMap = { 0: 'low', 1: 'medium', 2: 'high' };

      const formattedData = data.map(item => ({
        ...item,
        category: reverseCategoryMap[item.category] || '',
        group: reverseGroupMap[item.group] || '',
        priority: reversePriorityMap[item.priority] || ''
      }));

      setChildRequirements({
        ...childRequirements,
        [requirement.id]: formattedData
      });

      newExpanded.add(requirement.id);
      setExpandedRequirements(newExpanded);
    } catch (error) {
      console.error('获取子需求失败:', error);
    }
  };



  const renderRequirement = (requirement, level = 0) => {
    return (
      <div key={requirement.id}>
        <div
          className={`p-4 hover:bg-gray-50 cursor-pointer`}
          style={{ paddingLeft: `${level * 2 + 1}rem` }}
          onClick={() => handleRequirementClick(requirement)}
        >
          <div className="flex items-center justify-between">
            <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
              <div className="flex items-center gap-2">
                <ChevronRightIcon
                  className={`w-4 h-4 transition-transform ${expandedRequirements.has(requirement.id) ? 'transform rotate-90' : ''}`}
                />
                <FileTextIcon className="w-5 h-5 text-blue-500" />
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{requirement.name}</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                {categoryOptions.find(c => c.value === requirement.category)?.label}
              </div>
              <div className="text-sm text-gray-600">
                {groupOptions.find(g => g.value === requirement.group)?.label}
              </div>
              <div>
                <div className={`px-3 py-1 rounded-full text-sm inline-block text-sm ${requirement.priority === 'high' ? 'bg-red-100 text-red-800' :
                  requirement.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                  {priorityOptions.find(p => p.value === requirement.priority)?.label}优先级
                </div>
              </div>
              <div className="text-sm text-gray-600 truncate max-w-[200px]">
                {requirement.description || '-'}
              </div>
            </div>
            <div className="flex gap-2 w-[200px] justify-end">
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewDetail(requirement);
                }}
              >
                <EyeOpenIcon className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditRequirement(requirement);
                }}
              >
                <Pencil1Icon className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddChild(requirement);
                }}
              >
                <PlusIcon className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                className="text-red-600"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteRequirement(requirement);
                }}
              >
                <TrashIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {expandedRequirements.has(requirement.id) && childRequirements[requirement.id]?.map(child =>
          renderRequirement(child, level + 1)
        )}
      </div>
    );
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold mb-4">项目列表</h2>
          <div className="relative">
            <input
              type="text"
              value={projectSearchQuery}
              onChange={handleProjectSearch}
              placeholder="搜索项目..."
              className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
          </div>
        </div>
        {projectListJsx}
      </div>

      {selectedProject ? (
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold mb-2">{selectedProject.name}</h1>
              <p className="text-gray-500">需求管理</p>
            </div>

          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex flex-wrap gap-4">
              <div className="relative w-[200px]">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="搜索需求..."
                  className="w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="absolute left-2.5 top-2.5 w-4 h-4 text-gray-400" />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">需求类型（全部）</option>
                {categoryOptions.map(category => (
                  <option key={category.value} value={category.value}>{category.label}</option>
                ))}
              </select>
              <select
                value={selectedGroup}
                onChange={(e) => setSelectedGroup(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">功能分组（全部）</option>
                {groupOptions.map(group => (
                  <option key={group.value} value={group.value}>{group.label}</option>
                ))}
              </select>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="w-[180px] px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">优先级（全部）</option>
                {priorityOptions.map(priority => (
                  <option key={priority.value} value={priority.value}>{priority.label}</option>
                ))}
              </select>
              <div className="flex gap-2">
                <Button
                  onClick={() => {
                    fetchRequirements(selectedProject.id, page);
                    // 搜索后折叠所有非根节点
                    collapseAllExceptRootNodes();
                  }}
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                  style={{ backgroundColor: '#007bff', color: 'white' }}
                >
                  <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                  搜索
                </Button>
                <Button
                  onClick={handleReset}
                  variant="outline"
                  className="w-[100px] whitespace-nowrap flex items-center justify-center"
                >
                  重置
                </Button>
                <Button
                  className="flex items-center gap-1"
                  onClick={openNewModal}
                >
                  <PlusIcon className="w-4 h-4" />
                  添加需求
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
            <div className="divide-y">
              <div className="p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="grid grid-cols-5 gap-8 flex-1 mr-8">
                    <div className="text-sm font-medium text-gray-500">需求名称</div>
                    <div className="text-sm font-medium text-gray-500">需求类型</div>
                    <div className="text-sm font-medium text-gray-500">功能分组</div>
                    <div className="text-sm font-medium text-gray-500">优先级</div>
                    <div className="text-sm font-medium text-gray-500">需求描述</div>
                  </div>
                  <div className="w-[200px] text-sm font-medium text-gray-500">操作</div>
                </div>
              </div>

              {requirements.map(requirement => renderRequirement(requirement))}
            </div>
          </div>

          <div className="flex items-center justify-end mt-4">
            <div className="flex items-center gap-2">
              <span>共 {totalElements} 条记录</span>
              <Button
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 0}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                上一页
              </Button>

              {Array.from({ length: totalPages }, (_, index) => (
                <Button
                  key={index}
                  onClick={() => handlePageChange(index)}
                  className={`px-3 py-1 border rounded ${page === index ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`}
                >
                  {index + 1}
                </Button>
              ))}

              <Button
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages - 1}
                className="px-3 py-1 border rounded hover:bg-gray-200"
              >
                下一页
              </Button>
            </div>
          </div>

        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileTextIcon className="w-12 h-12 mx-auto mb-4" />
            <p>请选择左侧的项目查看需求</p>
          </div>
        </div>
      )}

      {showDetailModal && selectedRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">查看需求</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <div className="text-sm text-gray-500 mb-1">项目名称</div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    {selectedProject?.name || '未选择项目'}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">优先级</div>
                    <div className=" p-3 rounded-lg">
                      {priorityOptions.find(p => p.value === selectedRequirement.priority)?.label || '-'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">需求名称</div>
                    <div className=" p-3 rounded-lg">
                      {selectedRequirement.name}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">需求类型</div>
                    <div className=" p-3 rounded-lg">
                      {categoryOptions.find(c => c.value === selectedRequirement.category)?.label || '-'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">功能分组</div>
                    <div className=" p-3 rounded-lg">
                      {groupOptions.find(g => g.value === selectedRequirement.group)?.label || '-'}
                    </div>
                  </div>
                </div>



                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">需求描述</div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {selectedRequirement.description || '-'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">处理过程</div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {selectedRequirement.process || '-'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-500 mb-1">需求输入</div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {selectedRequirement.input || '-'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-1">需求输出</div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {selectedRequirement.output || '-'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {showNewModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">
                {newRequirement.parentId ? '添加子需求' : '添加需求'}
              </h3>
              <button
                onClick={() => setShowNewModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {newRequirement.parentId ? (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        项目名称
                      </label>
                      <input
                        type="text"
                        value={selectedProject?.name || ''}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        父需求
                      </label>
                      <input
                        type="text"
                        value={newRequirement.parentName || ''}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={newRequirement.name}
                      onChange={(e) => setNewRequirement({ ...newRequirement, name: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求名称..."
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      优先级 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={priorityDropdownRef}>
                      <div
                        onClick={() => setIsPriorityDropdownOpen(!isPriorityDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newRequirement.priority ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newRequirement.priority ? 'text-gray-900' : 'text-gray-400'}>
                          {newRequirement.priority ? priorityOptions.find(p => p.value === newRequirement.priority)?.label : '请选择优先级'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isPriorityDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isPriorityDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {priorityOptions.map(priority => (
                              <div
                                key={priority.value}
                                onClick={() => {
                                  setNewRequirement({ ...newRequirement, priority: priority.value });
                                  setErrors({ ...errors, priority: false });
                                  setIsPriorityDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newRequirement.priority === priority.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {priority.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.priority && <p className="text-red-500 text-sm">{errors.priority}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求类型 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={categoryDropdownRef}>
                      <div
                        onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newRequirement.category ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newRequirement.category ? 'text-gray-900' : 'text-gray-400'}>
                          {newRequirement.category ? categoryOptions.find(c => c.value === newRequirement.category)?.label : '请选择需求类型'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isCategoryDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isCategoryDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {categoryOptions.map(category => (
                              <div
                                key={category.value}
                                onClick={() => {
                                  setNewRequirement({ ...newRequirement, category: category.value });
                                  setErrors({ ...errors, category: false });
                                  setIsCategoryDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newRequirement.category === category.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {category.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.category && <p className="text-red-500 text-sm">{errors.category}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      功能分组 <span className="text-red-500">*</span>
                    </label>
                    <div className="relative" ref={groupDropdownRef}>
                      <div
                        onClick={() => setIsGroupDropdownOpen(!isGroupDropdownOpen)}
                        className={`w-full px-3 py-2 border rounded-lg bg-white cursor-pointer flex items-center justify-between ${
                          !newRequirement.group ? 'border-gray-300' : 'border-gray-300 hover:border-blue-500'
                        }`}
                      >
                        <span className={newRequirement.group ? 'text-gray-900' : 'text-gray-400'}>
                          {newRequirement.group ? groupOptions.find(g => g.value === newRequirement.group)?.label : '请选择功能分组'}
                        </span>
                        <svg 
                          className={`h-5 w-5 text-gray-400 transform transition-transform ${isGroupDropdownOpen ? 'rotate-180' : ''}`} 
                          viewBox="0 0 20 20" 
                          fill="currentColor"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                      </div>
                      
                      {isGroupDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
                          <div className="py-1 max-h-60 overflow-auto">
                            {groupOptions.map(group => (
                              <div
                                key={group.value}
                                onClick={() => {
                                  setNewRequirement({ ...newRequirement, group: group.value });
                                  setErrors({ ...errors, group: false });
                                  setIsGroupDropdownOpen(false);
                                }}
                                className={`px-3 py-2 cursor-pointer hover:bg-blue-50 ${
                                  newRequirement.group === group.value ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                                }`}
                              >
                                {group.label}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    {errors.group && <p className="text-red-500 text-sm">{errors.group}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求描述
                    </label>
                    <textarea
                      value={newRequirement.description}
                      onChange={(e) => setNewRequirement({ ...newRequirement, description: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.description ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求描述..."
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      处理过程
                    </label>
                    <textarea
                      value={newRequirement.process}
                      onChange={(e) => setNewRequirement({ ...newRequirement, process: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.process ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的处理过程..."
                    />
                    {errors.process && <p className="text-red-500 text-sm">{errors.process}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入描述
                    </label>
                    <textarea
                      value={newRequirement.input}
                      onChange={(e) => setNewRequirement({ ...newRequirement, input: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.input ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的输入内容..."
                    />
                    {errors.input && <p className="text-red-500 text-sm">{errors.input}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输出描述
                    </label>
                    <textarea
                      value={newRequirement.output}
                      onChange={(e) => setNewRequirement({ ...newRequirement, output: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.output ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的输出内容..."
                    />
                    {errors.output && <p className="text-red-500 text-sm">{errors.output}</p>}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowNewModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleCreateRequirement}>
                创建
              </Button>
            </div>
          </div>
        </div>
      )}

      {showEditModal && editingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">编辑需求</h3>
              <button
                onClick={() => setShowEditModal(false)}
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className={`${editingRequirement.parentId > 0 ? 'grid grid-cols-2 gap-4' : ''}`}>
                  <div className={`${editingRequirement.parentId > 0 ? '' : 'w-full'}`}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      项目名称
                    </label>
                    <input
                      type="text"
                      value={selectedProject?.name || ''}
                      disabled
                      className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                    />
                  </div>
                  {editingRequirement.parentId > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        父需求
                      </label>
                      <input
                        type="text"
                        value={editingRequirement.parentName || ''}
                        disabled
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-gray-500"
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={editingRequirement.name}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, name: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      优先级 <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={editingRequirement.priority}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, priority: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.priority ? 'border-red-500' : 'border-gray-300'}`}
                    >
                      <option value="">请选择优先级</option>
                      {priorityOptions.map(priority => (
                        <option key={priority.value} value={priority.value}>{priority.label}</option>
                      ))}
                    </select>
                    {errors.priority && <p className="text-red-500 text-sm">{errors.priority}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求类型 <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={editingRequirement.category}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, category: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.category ? 'border-red-500' : 'border-gray-300'}`}
                    >
                      <option value="">请选择需求类型</option>
                      {categoryOptions.map(category => (
                        <option key={category.value} value={category.value}>{category.label}</option>
                      ))}
                    </select>
                    {errors.category && <p className="text-red-500 text-sm">{errors.category}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      功能分组 <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={editingRequirement.group}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, group: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.group ? 'border-red-500' : 'border-gray-300'}`}
                    >
                      <option value="">请选择功能分组</option>
                      {groupOptions.map(group => (
                        <option key={group.value} value={group.value}>{group.label}</option>
                      ))}
                    </select>
                    {errors.group && <p className="text-red-500 text-sm">{errors.group}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      需求描述
                    </label>
                    <textarea
                      value={editingRequirement.description}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, description: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.description ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求描述..."
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      处理过程
                    </label>
                    <textarea
                      value={editingRequirement.process}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, process: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.process ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的处理过程..."
                    />
                    {errors.process && <p className="text-red-500 text-sm">{errors.process}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输入描述
                    </label>
                    <textarea
                      value={editingRequirement.input}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, input: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.input ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的输入内容..."
                    />
                    {errors.input && <p className="text-red-500 text-sm">{errors.input}</p>}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输出描述
                    </label>
                    <textarea
                      value={editingRequirement.output}
                      onChange={(e) => setEditingRequirement({ ...editingRequirement, output: e.target.value })}
                      rows={2}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${errors.output ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="请输入需求的输出内容..."
                    />
                    {errors.output && <p className="text-red-500 text-sm">{errors.output}</p>}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleSaveEdit}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {showDeleteModal && deletingRequirement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">确定要删除需求"{deletingRequirement.name}"吗？</h3>
              <p className="text-gray-500 text-sm mb-6">此操作不可恢复。</p>
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                  className="w-24"
                >
                  取消
                </Button>
                <Button
                  onClick={handleConfirmDelete}
                  className="w-24 bg-red-500 hover:bg-red-600 text-white"
                >
                  删除
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});