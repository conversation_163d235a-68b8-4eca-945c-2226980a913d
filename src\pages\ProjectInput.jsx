import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import {
  UploadIcon,
  TrashIcon,
  FileTextIcon,
  PlusIcon,
  Cross2Icon,
  MagnifyingGlassIcon,
  Pencil1Icon
} from '@radix-ui/react-icons';
import { Button } from '../components/ui/button';
import { useNavigate, useLocation } from 'react-router-dom';
import { projectApi, clientApi } from '../services/projectInputService';

// 项目类型配置
const projectTypes = [
  { id: 0, name: '软件开发' },
  { id: 1, name: '市场调研' },
  { id: 2, name: '产品设计' },
  { id: 3, name: '服务项目' }
];

// 优先级配置
const priorities = [
  { id: 'high', name: '高', color: 'bg-red-100 text-red-800' },
  { id: 'medium', name: '中', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'low', name: '低', color: 'bg-green-100 text-green-800' }
];

// 团队成员配置（模拟数据）
const teamMembers = [
  { id: 1, name: '张三', position: '项目经理' },
  { id: 2, name: '李四', position: '技术主管' },
  { id: 3, name: '王五', position: '开发工程师' },
  { id: 4, name: '赵六', position: '测试工程师' }
];

// 项目状态映射
const statusMap = {
  0: { name: '未开始', color: 'bg-gray-100 text-gray-800' },
  1: { name: '进行中', color: 'bg-blue-100 text-blue-800' },
  2: { name: '结项', color: 'bg-green-100 text-green-800' }
};

// 项目类型映射
const typeMap = {
  0: '软件开发',
  1: '市场调研',
  2: '产品设计',
  3: '服务项目'
};

// 优先级样式映射
const priorityStyleMap = {
  0: 'bg-red-100 text-red-800',    // 高优先级
  1: 'bg-yellow-100 text-yellow-800', // 中优先级
  2: 'bg-green-100 text-green-800'    // 低优先级
};

// 优先级文本映射
const priorityTextMap = {
  0: '高',
  1: '中',
  2: '低'
};

// 错误提示组件
const ErrorMessage = ({ message, onClose }) => (
  <div className="fixed top-4 left-1/2 -translate-x-1/2 flex items-center bg-white py-2 px-3 shadow-md rounded z-[100]">
    <div className="text-red-500 mr-3">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-7v2h2v-2h-2zm0-8v6h2V7h-2z" fill="currentColor" />
      </svg>
    </div>
    <div className="text-red-500">{message}</div>
    <button onClick={onClose} className="ml-4 text-gray-400 hover:text-gray-600">
      <Cross2Icon className="w-4 h-4" />
    </button>
  </div>
);

/**
 * 项目输入管理页面组件
 * 功能：项目列表展示、客户管理、项目CRUD操作、搜索筛选等
 */
export const ProjectInput = observer(() => {
  const navigate = useNavigate();
  const location = useLocation();

  // 客户相关状态
  const [selectedClient, setSelectedClient] = useState(null); // 当前选中的客户
  const [clients, setClients] = useState([]); // 客户列表
  const [clientSearchQuery, setClientSearchQuery] = useState(''); // 客户搜索关键词
  const [clientLoading, setClientLoading] = useState(false); // 客户数据加载状态
  const [clientError, setClientError] = useState(null); // 客户数据错误信息
  const [isEditMode, setIsEditMode] = useState(false); // 客户编辑模式

  // 项目相关状态
  const [projects, setProjects] = useState([]); // 项目列表
  const [projectLoading, setProjectLoading] = useState(false); // 项目数据加载状态
  const [projectError, setProjectError] = useState(null); // 项目数据错误信息
  const [searchKeyword, setSearchKeyword] = useState(''); // 项目搜索关键词
  const [selectedStatus, setSelectedStatus] = useState('1'); // 选中的项目状态

  // 模态框状态
  const [showAddModal, setShowAddModal] = useState(false); // 新建项目模态框
  const [showClientModal, setShowClientModal] = useState(false); // 客户模态框
  const [showDetailModal, setShowDetailModal] = useState(false); // 项目详情模态框
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false); // 删除确认对话框

  // 表单数据状态
  const [formData, setFormData] = useState({
    id: null,
    name: '',
    type: '',
    description: '',
    createTime: new Date().toISOString().split('T')[0],
    priority: 'medium',
    manager: '',
    attachments: []
  });

  const [clientFormData, setClientFormData] = useState({
    id: null,
    company: '',
    contact: '',
    phone: '',
    createTime: new Date().toISOString().split('T')[0],
    email: '',
    address: '',
    description: ''
  });

  // 错误状态
  const [errors, setErrors] = useState({}); // 项目表单错误
  const [clientFormErrors, setClientFormErrors] = useState({}); // 客户表单错误
  const [errorMessage, setErrorMessage] = useState(''); // 全局错误提示

  // 其他状态
  const [projectDetail, setProjectDetail] = useState(null); // 项目详情数据
  const [detailLoading, setDetailLoading] = useState(false); // 详情加载状态
  const [projectToDelete, setProjectToDelete] = useState(null); // 待删除的项目
  const [searchTimeout, setSearchTimeout] = useState(null); // 搜索防抖定时器

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);

  // 组件初始化：设置默认客户和获取数据
  useEffect(() => {
    const initializeData = async () => {
      // 获取路由状态中的数据
      const state = location.state || {};
      const { projectData, previousClient } = state;

      // 设置初始客户状态
      const initialClient = projectData?.client || previousClient || { id: 'all', name: '所有客户' };
      setSelectedClient(initialClient);

      // 构建查询参数
      const params = new URLSearchParams({
        page: 0,
        size: pageSize,
        status: selectedStatus
      });

      // 添加客户ID条件（如果不是"所有客户"）
      if (initialClient?.id !== 'all' && initialClient?.id) {
        params.append('clientId', initialClient.id);
      }

      // 并行获取项目列表和客户列表
      await Promise.all([
        fetchProjectsWithParams(params),
        fetchClients()
      ]);
    };

    initializeData();
  }, [pageSize, selectedStatus]); // 依赖项包含可能变化的值

  // 获取项目列表数据
  const fetchProjectsWithParams = async (params) => {
    setProjectLoading(true);
    try {
      const data = await projectApi.getProjectList(params);
      setProjects(data.content);
      setTotalPages(data.totalPages);
      setTotalRecords(data.totalElements);
    } catch (err) {
      setProjectError(err.message);
      setErrorMessage(err.message);
    } finally {
      setProjectLoading(false);
    }
  };

  // 获取客户列表数据
  const fetchClients = async () => {
    setClientLoading(true);
    setClientError(null);
    try {
      const data = await clientApi.getClientList();
      // 格式化客户数据
      const formattedData = data.map(client => ({
        id: client.id,
        name: client.company,
        contact: client.contact,
        phone: client.phone,
        email: client.email,
        address: client.address,
        description: client.description,
        createTime: client.createTime ? client.createTime.split('T')[0] : new Date().toISOString().split('T')[0],
        projectCount: 0
      }));
      setClients(formattedData);
    } catch (err) {
      setClientError(err.message);
      console.error('获取客户数据错误:', err);
    } finally {
      setClientLoading(false);
    }
  };

  // 修改选择客户的处理函数
  const handleSelectClient = (client) => {
    setSelectedClient(client);
    setCurrentPage(1); // 重置页码为1

    const params = new URLSearchParams({
      page: 0,
      size: pageSize
    });

    // 添加状态条件，仅当选择了状态时
    if (selectedStatus !== '') {
      params.append('status', selectedStatus);
    }

    if (client.id !== 'all') {
      params.append('clientId', client.id);
    }

    fetchProjectsWithParams(params);
  };

  // 修改页码变化处理函数
  const handlePageChange = (page) => {
    setCurrentPage(page);
    const params = new URLSearchParams({
      page: page - 1,
      size: pageSize
    });
    
    // 使用当前选择的状态，如果没有选择则不添加状态条件
    if (selectedStatus !== '') {
      params.append('status', selectedStatus);
    }
    
    // 添加客户ID条件（如果不是"所有客户"）
    if (selectedClient?.id !== 'all') {
      params.append('clientId', selectedClient.id);
    }
    
    fetchProjectsWithParams(params);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }));
  };

  const removeFile = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) newErrors.name = '请输入项目名称';
    if (!formData.type) newErrors.type = '请选择项目类型';
    if (!formData.priority) newErrors.priority = '请选择优先级';
    if (!formData.manager) newErrors.manager = '请选择项目经理';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const requestData = {
        id: formData.id || 0,
        name: formData.name,
        type: parseInt(formData.type),
        description: formData.description,
        priority: getPriorityNumber(formData.priority),
        managerId: parseInt(formData.manager),
        clientId: selectedClient.id,
        managerName: getManagerName(formData.manager)
      };

      if (formData.id) {
        await projectApi.updateProject(formData.id, requestData);
      } else {
        await projectApi.createProject(requestData);
      }

      // 刷新项目列表
      const params = new URLSearchParams({
        page: 0,
        size: pageSize,
        status: '1'
      });

      if (selectedClient?.id !== 'all') {
        params.append('clientId', selectedClient.id);
      }

      await fetchProjectsWithParams(params);

      setShowAddModal(false);
      setFormData({
        id: null,
        name: '',
        type: '',
        description: '',
        priority: 'medium',
        manager: '',
        attachments: []
      });
    } catch (err) {
      console.error('保存项目错误:', err);
      setErrorMessage(err.message);
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 工具函数：优先级转换
  const getPriorityNumber = (priority) => {
    const priorityMap = { 'high': 0, 'medium': 1, 'low': 2 };
    return priorityMap[priority] || 1;
  };

  const getPriorityText = (priority) => {
    const priorityMap = { 0: 'high', 1: 'medium', 2: 'low' };
    return priorityMap[priority] || 'medium';
  };

  // 工具函数：获取经理名称
  const getManagerName = (managerId) => {
    const manager = teamMembers.find(m => m.id === parseInt(managerId));
    return manager ? manager.name : '';
  };

  // 添加一个重置客户表单的函数
  const resetClientForm = () => {
    setClientFormData({
      id: null,
      company: '',
      contact: '',
      phone: '',
      createTime: new Date().toISOString().split('T')[0],
      email: '',
      address: '',
      description: ''
    });
    setClientFormErrors({});
  };

  // 修改处理函数
  const handleAddClient = () => {
    resetClientForm(); // 使用重置函数
    setShowClientModal(true);
  };

  const handleEditClient = (e, client) => {
    e.stopPropagation();

    // 格式化电话号码，只保留数字
    const formattedPhone = client.phone?.replace(/[^\d]/g, '') || '';

    // 只设置表单数据，不进行验证
    setClientFormData({
      id: client.id,
      company: client.name,
      contact: client.contact,
      phone: formattedPhone,
      email: client.email,
      address: client.address,
      description: client.description,
      createTime: client.createTime
    });

    // 清除之前的错误状态
    setClientFormErrors({});

    setShowClientModal(true);
  };

  const handleDeleteClient = (e, client) => {
    e.stopPropagation(); // 阻止事件冒泡
    setShowDeleteConfirm(true);
    setProjectToDelete(client);
  };

  const confirmDelete = async () => {
    if (!projectToDelete) return;

    try {
      await projectApi.deleteProject(projectToDelete.id);

      const params = new URLSearchParams({
        page: 0,
        size: pageSize
      });

      // 使用当前选择的状态，如果没有选择则不添加状态条件
      if (selectedStatus !== '') {
        params.append('status', selectedStatus);
      }

      if (selectedClient?.id !== 'all') {
        params.append('clientId', selectedClient.id);
      }

      await fetchProjectsWithParams(params);
      setShowDeleteConfirm(false);
      setProjectToDelete(null);
      setTimeout(() => setErrorMessage(''), 3000);
    } catch (err) {
      console.error('删除项目错误:', err);
      setErrorMessage(err.message);
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  const handleSaveClient = async () => {
    setClientFormErrors({});

    const newErrors = {};
    if (!clientFormData.company?.trim()) {
      newErrors.company = '请输入公司名称';
    }
    if (!clientFormData.contact?.trim()) {
      newErrors.contact = '请输入联系人';
    }

    const phoneRegex = /^1[3456789]\d{9}$/;
    if (!clientFormData.phone?.trim()) {
      newErrors.phone = '请输入联系电话';
    } else if (!phoneRegex.test(clientFormData.phone)) {
      newErrors.phone = '请输入正确的手机号码格式';
    }

    if (Object.keys(newErrors).length > 0) {
      setClientFormErrors(newErrors);
      return;
    }

    try {
      const requestData = {
        id: clientFormData.id || 0,
        company: clientFormData.company.trim(),
        contact: clientFormData.contact.trim(),
        phone: clientFormData.phone.trim(),
        email: clientFormData.email?.trim() || '',
        address: clientFormData.address?.trim() || '',
        description: clientFormData.description?.trim() || ''
      };

      if (clientFormData.id) {
        await clientApi.updateClient(clientFormData.id, requestData);
      } else {
        await clientApi.createClient(requestData);
      }

      await fetchClients();
      setShowClientModal(false);
    } catch (err) {
      console.error('保存客户错误:', err);
      setErrorMessage(err.message);
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 修改搜索处理函数
  const handleSearchChange = (value) => {
    setClientSearchQuery(value);
    setCurrentPage(1);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeoutId = setTimeout(async () => {
      setClientLoading(true);
      setClientError(null);
      try {
        const data = await clientApi.searchClients(value);
        const formattedData = data.map(client => ({
          id: client.id,
          name: client.company,
          contact: client.contact,
          phone: client.phone,
          email: client.email,
          address: client.address,
          description: client.description,
          projectCount: 0
        }));
        setClients(formattedData);

        if (formattedData.length > 0) {
          const firstClient = formattedData[0];
          setSelectedClient(firstClient);
          fetchProjectsWithParams(new URLSearchParams({
            page: 0,
            size: pageSize,
            status: '1',
            clientId: firstClient.id
          }));
        } else {
          setSelectedClient(null);
          setProjects([]);
        }
      } catch (err) {
        setClientError(err.message);
        console.error('搜索客户错误:', err);
      } finally {
        setClientLoading(false);
      }
    }, 300);

    setSearchTimeout(timeoutId);
  };

  // 获取项目详情
  const fetchProjectDetail = async (projectId) => {
    setDetailLoading(true);
    try {
      const data = await projectApi.getProjectDetail(projectId);
      setProjectDetail(data);
      setShowDetailModal(true);
    } catch (err) {
      console.error('获取项目详情错误:', err);
      setErrorMessage(err.message);
      setTimeout(() => setErrorMessage(''), 3000);
    } finally {
      setDetailLoading(false);
    }
  };

  // 修改删除项目的处理函数
  const handleDeleteProject = (e, project) => {
    e.stopPropagation();
    setProjectToDelete(project);
    setShowDeleteConfirm(true);
  };

  // 修改编辑项目的函数
  const handleEditProject = async (e, project) => {
    e.stopPropagation();

    try {
      const projectDetail = await projectApi.getProjectDetail(project.id);
      navigate('/project-management/add', {
        state: {
          editMode: true,
          projectData: {
            ...projectDetail,
            priority: getPriorityText(projectDetail.priority),
            clientId: projectDetail.clientId,
            clientName: projectDetail.clientName,
            client: selectedClient
          },
          previousClient: selectedClient
        }
      });
    } catch (err) {
      console.error('获取项目详情错误:', err);
      setErrorMessage('获取项目详情失败');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };



  // 修改搜索函数
  const handleSearch = () => {
    // 重置页码
    setCurrentPage(1);

    // 构建查询参数
    const params = new URLSearchParams({
      page: 0,
      size: pageSize
    });

    // 添加项目名称搜索条件
    if (searchKeyword.trim()) {
      params.append('name', searchKeyword.trim());
    }

    // 添加状态搜索条件
    if (selectedStatus !== '') {
      params.append('status', selectedStatus);
    }

    // 添加客户ID条件（如果不是"所有客户"）
    if (selectedClient?.id !== 'all') {
      params.append('clientId', selectedClient?.id);
    }

    // 发起搜索请求
    setProjectLoading(true);
    fetchProjectsWithParams(params);
  };

  // 修改重置函数
  const handleReset = () => {
    // 重置搜索条件
    setSearchKeyword('');
    setSelectedStatus('1');  // 设置为"进行中"状态
    setCurrentPage(1);

    // 构建查询参数
    const params = new URLSearchParams({
      page: 0,
      size: pageSize,
      status: '1'  // 重置后默认使用"进行中"状态
    });

    // 添加客户ID条件（如果不是"所有客户"）
    if (selectedClient?.id !== 'all') {
      params.append('clientId', selectedClient?.id);
    }

    // 发起搜索请求
    setProjectLoading(true);
    fetchProjectsWithParams(params);
  };

  // 修改跳转到新增/编辑页面的函数
  const handleAddProject = () => {
    navigate('/project-management/add', {
      state: {
        client: selectedClient,
        previousClient: selectedClient  // 保存当前选择的客户
      }
    });
  };

  return (
    <div className="flex-1 p-6 pt-16 overflow-hidden bg-gray-50 flex h-screen">
      {/* 添加错误提示组件 */}
      {errorMessage && (
        <ErrorMessage
          message={errorMessage}
          onClose={() => setErrorMessage('')}
        />
      )}

      {/* 客户列表侧边栏 */}
      <div className="w-64 bg-white rounded-lg shadow-sm mr-6 flex flex-col h-[calc(100vh-6rem)]">
        <div className="p-4 border-b">
          <div className="flex gap-2">
            {/* 搜索部分 */}
            <div className="flex-1 flex gap-2">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="搜索客户..."
                  value={clientSearchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
              </div>
            </div>

            {/* 编辑按钮部分 */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => setIsEditMode(!isEditMode)}
                className="p-1.5 hover:bg-gray-100 rounded-lg"
              >
                <Pencil1Icon className="w-4 h-4 text-gray-500" />
              </button>
              {isEditMode && (
                <button
                  onClick={handleAddClient}
                  className="p-1.5 hover:bg-gray-100 rounded-lg"
                >
                  <PlusIcon className="w-4 h-4 text-gray-500" />
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto">
          {clientLoading ? (
            <div className="p-4 text-center text-gray-500">
              加载中...
            </div>
          ) : clientError ? (
            <div className="p-4 text-center text-red-500">
              {clientError}
            </div>
          ) : clients.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              暂无客户数据
            </div>
          ) : (
            <>
              {/* 添加"所有客户"选项 */}
              <div
                className={`p-2 cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between ${selectedClient?.id === 'all' ? 'bg-blue-50' : ''
                  }`}
                onClick={() => {
                  handleSelectClient({ id: 'all', name: '所有客户' });
                }}
              >
                <span className="truncate">所有客户</span>
              </div>

              {/* 原有的客户列表 */}
              {clients.map(client => (
                <div
                  key={client.id}
                  className={`p-2 cursor-pointer hover:bg-gray-100 rounded flex items-center justify-between ${selectedClient?.id === client.id ? 'bg-blue-50' : ''
                    }`}
                  onClick={() => handleSelectClient(client)}
                >
                  <span className="truncate">{client.name}</span>

                  {isEditMode && (
                    <div className="flex gap-2">
                      <button
                        className="p-1 hover:bg-gray-200 rounded-full"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditClient(e, client);
                        }}
                      >
                        <Pencil1Icon className="w-4 h-4" />
                      </button>
                      <button
                        className="p-1 hover:bg-gray-200 rounded-full text-red-600"
                        onClick={(e) => handleDeleteClient(e, client)}
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </>
          )}
        </div>
      </div>

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col h-[calc(100vh-6rem)]">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">项目列表</h2>

          </div>

          {/* 搜索区域 */}
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex gap-4 items-center">
              {/* 修改搜索框宽度为固定宽度 */}
              <div className="w-64">
                <input
                  type="text"
                  placeholder="搜索项目名称..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">所有状态</option>
                  {Object.entries(statusMap).map(([key, value]) => (
                    <option key={key} value={key}>
                      {value.name}
                    </option>
                  ))}
                </select>
              </div>
              <Button
                onClick={handleSearch}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                <MagnifyingGlassIcon className="w-4 h-4 mr-2" />
                搜索
              </Button>
              <Button
                variant="outline"
                onClick={handleReset}
              >
                重置
              </Button>

              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white"
                onClick={handleAddProject}
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                新建项目
              </Button>

              {/* 添加一个占位的空白区域，让按钮靠左对齐 */}
              <div className="flex-1"></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm flex-1 overflow-auto">
          {/* 修改表头部分 */}
          <div className="grid grid-cols-7 gap-4 p-4 bg-gray-50 text-sm font-medium text-gray-500">
            <div className="col-span-2">项目名称</div>
            <div>项目状态</div>
            <div>项目类型</div>
            <div>项目经理</div>
            <div>优先级</div>
            <div className="text-right">操作</div>
          </div>

          {/* 项目列表内容 */}
          <div className="divide-y">
            {projectLoading ? (
              <div className="p-4 text-center text-gray-500">加载中...</div>
            ) : projectError ? (
              <div className="p-4 text-center text-red-500">{projectError}</div>
            ) : projects.length === 0 ? (
              <div className="p-4 text-center text-gray-500">暂无项目数据</div>
            ) : (
              projects.map(project => (
                <div
                  key={project.id}
                  className="grid grid-cols-7 gap-4 p-4 hover:bg-gray-50 items-center text-sm"
                >
                  <div className="col-span-2">
                    <div className="font-medium text-gray-900">{project.name}</div>
                    <div className="text-gray-500 text-xs mt-1 line-clamp-1">
                      {project.description}
                    </div>
                  </div>
                  <div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusMap[project.status]?.color}`}>
                      {statusMap[project.status]?.name || '未知状态'}
                    </span>
                  </div>
                  <div className="text-gray-600">{typeMap[project.type]}</div>
                  <div className="text-gray-600">{project.managerName}</div>
                  <div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityStyleMap[project.priority]}`}>
                      {priorityTextMap[project.priority]}优先级
                    </span>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => fetchProjectDetail(project.id)}
                      disabled={detailLoading}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      查看
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => handleEditProject(e, project)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      修改
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => handleDeleteProject(e, project)}
                      className="text-red-600 hover:text-red-700"
                    >
                      删除
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 修改分页器样式 */}
        {!projectLoading && !projectError && projects.length > 0 && (
          <div className="mt-4 flex justify-end pr-4">
            <div className="flex items-center text-sm">
              <span className="mr-4 text-gray-500">
                共 {totalRecords} 条记录
              </span>
              <div className="flex items-center">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 ${currentPage === 1
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-600 hover:text-blue-600'
                    }`}
                >
                  上一页
                </button>

                {/* 页码显示部分 */}
                <div className="flex mx-2">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`w-8 h-8 mx-1 flex items-center justify-center rounded ${pageNum === currentPage
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-50 text-gray-600 hover:text-blue-600'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 ${currentPage === totalPages
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-600 hover:text-blue-600'
                    }`}
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 新建项目模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[800px] max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">
                {formData.name ? '编辑项目' : '新建项目'}
              </h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>

            {/* 原有的表单内容 */}
            <div className="p-6">
              <div className="space-y-6">
                {/* 客户信息（只读） */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    客户名称
                  </label>
                  <input
                    type="text"
                    value={selectedClient?.name || ''}
                    disabled
                    className="w-full px-3 py-2 bg-gray-50 border rounded-lg text-gray-500"
                  />
                </div>

                {/* 原有的所有表单字段 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目类型 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择项目类型</option>
                    {projectTypes.map(type => (
                      <option key={type.id} value={type.id}>{type.name}</option>
                    ))}
                  </select>
                  {errors.type && (
                    <p className="mt-1 text-sm text-red-500">{errors.type}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目描述
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    maxLength={1000}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入项目描述（最多1000字符）"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    还可输入 {1000 - formData.description.length} 个字符
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级 <span className="text-red-500">*</span>
                  </label>
                  <div className="flex gap-4">
                    {priorities.map(priority => (
                      <label
                        key={priority.id}
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        <input
                          type="radio"
                          name="priority"
                          value={priority.id}
                          checked={formData.priority === priority.id}
                          onChange={(e) => handleInputChange('priority', e.target.value)}
                          className="w-4 h-4 text-blue-600"
                        />
                        <span className={`px-2 py-1 rounded-full text-sm ${priority.color}`}>
                          {priority.name}优先级
                        </span>
                      </label>
                    ))}
                  </div>
                  {errors.priority && (
                    <p className="mt-1 text-sm text-red-500">{errors.priority}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目经理 <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.manager}
                    onChange={(e) => handleInputChange('manager', e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">请选择项目经理</option>
                    {teamMembers.map(member => (
                      <option key={member.id} value={member.id}>
                        {member.name} ({member.position})
                      </option>
                    ))}
                  </select>
                  {errors.manager && (
                    <p className="mt-1 text-sm text-red-500">{errors.manager}</p>
                  )}
                </div>
                {/* Files */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    项目文件
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8">
                    <div className="text-center">
                      <UploadIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-4 flex text-sm leading-6 text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className="relative cursor-pointer rounded-md font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 hover:text-blue-500"
                        >
                          <span>上传文件</span>
                          <input
                            id="file-upload"
                            name="file-upload"
                            type="file"
                            className="sr-only"
                            multiple
                            onChange={handleFileUpload}
                          />
                        </label>
                        <p className="pl-1">或拖拽文件到此处</p>
                      </div>
                      <p className="text-xs leading-5 text-gray-600">
                        支持任意文件格式
                      </p>
                    </div>
                  </div>
                </div>
                {formData.attachments.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      已上传文件
                    </h4>
                    <div className="space-y-2">
                      {formData.attachments.map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div className="flex items-center gap-2">
                            <FileTextIcon className="w-4 h-4" />
                            <span>{file.name}</span>
                            <span className="text-sm text-gray-500">
                              ({(file.size / 1024 / 1024).toFixed(2)} MB)
                            </span>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600"
                            onClick={() => removeFile(index)}
                          >
                            <TrashIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleSubmit}>
                {formData.name ? '保存修改' : '创建项目'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 添加客户模态框 */}
      {showClientModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[500px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">
                {clientFormData.id ? '编辑客户' : '新增客户'}
              </h3>
              <button
                onClick={() => {
                  setShowClientModal(false);
                  resetClientForm(); // 使用重置函数
                }}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* 如果是编辑模式，显示创建时间 */}
                {clientFormData.id && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      创建时间
                    </label>
                    <input
                      type="text"
                      value={clientFormData.createTime || ''}
                      disabled
                      className="w-full px-3 py-2 bg-gray-50 border rounded-lg text-gray-500"
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    公司名称 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={clientFormData.company}
                    onChange={(e) => {
                      setClientFormData(prev => ({ ...prev, company: e.target.value }));
                      if (clientFormErrors.company) {
                        setClientFormErrors(prev => ({ ...prev, company: null }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${clientFormErrors.company ? 'border-red-500' : ''
                      }`}
                  />
                  {clientFormErrors.company && (
                    <p className="mt-1 text-sm text-red-500">{clientFormErrors.company}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    联系人 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={clientFormData.contact}
                    onChange={(e) => {
                      setClientFormData(prev => ({ ...prev, contact: e.target.value }));
                      if (clientFormErrors.contact) {
                        setClientFormErrors(prev => ({ ...prev, contact: null }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${clientFormErrors.contact ? 'border-red-500' : ''
                      }`}
                  />
                  {clientFormErrors.contact && (
                    <p className="mt-1 text-sm text-red-500">{clientFormErrors.contact}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    联系电话 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={clientFormData.phone}
                    maxLength={11}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^\d]/g, ''); // 只允许输入数字
                      setClientFormData(prev => ({ ...prev, phone: value }));
                      if (clientFormErrors.phone) {
                        setClientFormErrors(prev => ({ ...prev, phone: null }));
                      }
                    }}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${clientFormErrors.phone ? 'border-red-500' : ''
                      }`}
                    placeholder="请输入11位手机号码"
                  />
                  {clientFormErrors.phone && (
                    <p className="mt-1 text-sm text-red-500">{clientFormErrors.phone}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    电子邮箱
                  </label>
                  <input
                    type="email"
                    value={clientFormData.email}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    地址
                  </label>
                  <input
                    type="text"
                    value={clientFormData.address}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <textarea
                    value={clientFormData.description}
                    onChange={(e) => setClientFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    maxLength={500}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    还可输入 {500 - (clientFormData.description?.length || 0)} 个字符
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowClientModal(false);
                  resetClientForm(); // 使用重置函数
                }}
              >
                取消
              </Button>
              <Button onClick={handleSaveClient}>
                保存
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 项目详情模态框 */}
      {showDetailModal && projectDetail && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[600px]">
            <div className="p-6 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold">项目详情</h3>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <Cross2Icon className="w-4 h-4" />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">



                {/* 项目名称和项目负责人在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">客户名称</label>
                    <div className="mt-1">{projectDetail.clientName}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">项目名称</label>
                    <div className="mt-1">{projectDetail.name}</div>
                  </div>

                </div>

                {/* 创建时间和结束时间在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">项目负责人</label>
                    <div className="mt-1">{projectDetail.managerName}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">项目类型</label>
                    <div className="mt-1">{typeMap[projectDetail.type]}</div>
                  </div>


                </div>

                {/* 项目类型单独一行 */}
                <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="text-sm font-medium text-gray-500">创建时间</label>
                    <div className="mt-1">
                      {projectDetail.createTime ? projectDetail.createTime.split('T')[0] : '-'}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">结束时间</label>
                    <div className="mt-1">
                      {projectDetail.endTime ? projectDetail.endTime.split('T')[0] : '-'}
                    </div>
                  </div>
                </div>
                {/* 优先级和项目状态在同一行 */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">优先级</label>
                    <div className="mt-1">
                      <span className={`px-3 py-1 rounded-full text-sm ${priorityStyleMap[projectDetail.priority]}`}>
                        {priorityTextMap[projectDetail.priority]}优先级
                      </span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">项目状态</label>
                    <div className="mt-1">
                      <span className={`px-3 py-1 rounded-full text-sm ${statusMap[projectDetail.status]?.color}`}>
                        {statusMap[projectDetail.status]?.name || '未知状态'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 项目描述单独一行 */}
                <div>
                  <label className="text-sm font-medium text-gray-500">项目描述</label>
                  <div className="mt-1 p-3 bg-gray-50 rounded-lg min-h-[100px]">{projectDetail.description}</div>
                </div>
              </div>
            </div>
            <div className="p-6 border-t bg-gray-50 flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowDetailModal(false)}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 添加确认删除对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-[400px]">
            <div className="p-6">
              <h3 className="text-lg font-medium mb-4">确认删除</h3>
              <p className="text-gray-600">
                确定要删除项目&ldquo;{projectToDelete?.name}&rdquo;吗？此操作不可恢复。
              </p>
              <div className="mt-6 flex justify-center gap-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 bg-white text-gray-700 border rounded-lg hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={confirmDelete}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
                >
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});