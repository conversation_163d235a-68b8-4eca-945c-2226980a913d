// API接口管理文件
import { userStore } from '../store/userStore';

import { fetchData } from './fetch';

// 获取用户信息
export const getUserBySSHKey = async (sshKey) => {
  const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/user`, {
    headers: {
      'Private-Token': userStore.getUserData()?.accessToken || '',
      'SSH-Key': sshKey
    }
  });
  return response.json();
};

// 获取用户项目列表
export const getUserProjects = async (userId) => {
  const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/users/${userId}/projects`, {
    headers: {
      'Private-Token': userStore.getUserData()?.accessToken || '',
    }
  });
  return response.json();
};

// 获取项目分支列表
export const getProjectBranches = async (projectId) => {
  const response = await fetch(`${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/branches`, {
    headers: {
      'Private-Token': userStore.getUserData()?.accessToken || '',
    }
  });
  return response.json();
};

// 获取项目文件列表
export const getProjectFiles = async (projectId, branch, path = '') => {
  const response = await fetch(
    `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/tree?ref=${branch}${path ? `&path=${path}` : ''}`,
    {
      headers: {
        'Private-Token': userStore.getUserData()?.accessToken || '',
      }
    }
  );
  return response.json();
};

// 获取文件内容
export const getFileContent = async (projectId, filePath, branch) => {
  const encodedPath = encodeURIComponent(filePath.replace(/^\//, ''));
  const response = await fetch(
    `${fetchData["GITLAB_URL"]}/api/v4/projects/${projectId}/repository/files/${encodedPath}/raw?ref=${branch}`,
    {
      headers: {
        'Private-Token': userStore.getUserData()?.accessToken || '',
      }
    }
  );
  return response.text();
};

// 获取文件评审记录
export const getFileReview = async (projectId, branchName, fileName) => {
  const response = await fetch(
    `${fetchData["BASE_URL"]}/api/code-reviews/one?projectId=${projectId}&branchName=${branchName}&fileName=${fileName}`,
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
  return response.json();
};

// 创建评审记录
export const createFileReview = async (reviewData) => {
  const response = await fetch(`${fetchData["BASE_URL"]}/api/code-reviews/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(reviewData)
  });
  return response.json();
};

// 更新评审记录
export const updateFileReview = async (reviewId, reviewData) => {
  const response = await fetch(`${fetchData["BASE_URL"]}/api/code-reviews/update/${reviewId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(reviewData)
  });
  return response.json();
};

// 获取项目评审列表
export const getProjectReviews = async (projectId, branchName, fileName = '') => {
  const url = `${fetchData["BASE_URL"]}/api/code-reviews/all?projectId=${projectId}&branchName=${branchName}${fileName ? `&fileName=${fileName}` : ''}`;
  
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error('获取评审数据失败');
  }

  const data = await response.json();
  console.log('API返回的原始数据:', data);
  
  // 确保返回的是数组格式
  return Array.isArray(data) ? data : (data.content || []);
}; 