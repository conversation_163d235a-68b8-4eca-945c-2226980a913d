import React, { useState, useEffect, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import { useLocation } from 'react-router-dom';
import {
  Layout,
  Card,
  Button,
  Input,
  Modal,
  Form,
  message,
  Space,
  Typography,
  Row,
  Col,
  Descriptions,
  Empty,
  Spin,
  Select
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UserOutlined,
  DownOutlined,
  RightOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { PageContainer } from '../components/PageContainer';
import {  getAllOrganizations,createDepartment,checkDepartmentEmployees,deleteDepartment,updateDepartment } from '../services/organizationService';

const { Content, Sider } = Layout;
const { Search } = Input;
const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 组织架构管理页面组件
 * 功能：展示组织架构树形结构，支持添加、删除、编辑组织节点
 */
export const OrganizationStructure = observer(() => {
  // 路由信息
  const location = useLocation();

  // 添加调试日志
  console.log('OrganizationStructure: 组件渲染，当前路径:', location.pathname);

  // 如果不在组织架构页面，直接返回null，不渲染任何内容
  if (location.pathname !== '/hr-management/organization') {
    console.log('OrganizationStructure: 不在组织架构页面，不渲染组件');
    return null;
  }

  // 主要数据状态
  const [orgData, setOrgData] = useState(null); // 组织架构数据
  const [loading, setLoading] = useState(true); // 加载状态
  const [error, setError] = useState(null); // 错误信息
  const [selectedNode, setSelectedNode] = useState(null); // 当前选中的节点
  const [expandedNodes, setExpandedNodes] = useState(new Set()); // 展开的节点集合

  // 界面控制状态
  const [editMode, setEditMode] = useState(false); // 编辑模式
  const [searchQuery, setSearchQuery] = useState(''); // 搜索关键词

  // 弹窗状态
  const [showPositionDialog, setShowPositionDialog] = useState(false); // 岗位编辑弹窗
  const [showAddDepartmentModal, setShowAddDepartmentModal] = useState(false); // 添加组织弹窗
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false); // 删除确认弹窗

  // 表单数据
  const [newDepartment, setNewDepartment] = useState({
    name: '',
    position: '',
    duty: '',
    description: '',
    children: []
  }); // 新增组织表单数据

  const [selectedParentId, setSelectedParentId] = useState(null); // 选中的上级组织ID

  const [positionForm, setPositionForm] = useState({
    name: '',
    description: '',
    duty: '',
    permissions: []
  }); // 岗位编辑表单数据

  // 辅助状态
  const [deletingNodeId, setDeletingNodeId] = useState(null); // 待删除的节点ID
  const [formErrors, setFormErrors] = useState({ name: '', position: '' }); // 表单验证错误

  // 使用Ant Design的message替代自定义消息状态
  const [messageApi, contextHolder] = message.useMessage();

  /**
   * 切换节点展开/收起状态
   */
  const toggleNode = (nodeId) => {
    const newExpandedNodes = new Set(expandedNodes);
    if (newExpandedNodes.has(nodeId)) {
      newExpandedNodes.delete(nodeId);
    } else {
      newExpandedNodes.add(nodeId);
    }
    setExpandedNodes(newExpandedNodes);
  };

  /**
   * 递归获取所有节点ID
   */
  const getAllNodeIds = useCallback((node) => {
    let ids = [node.id];
    if (node.children?.length > 0) {
      node.children.forEach(child => {
        ids = [...ids, ...getAllNodeIds(child)];
      });
    }
    return ids;
  }, []);

  /**
   * 查找公司根节点或第一个有效节点
   */
  const findCompanyNode = useCallback((node) => {
    // 首先尝试查找特定的公司节点
    if (node.name === "成都XX有限公司") return node;

    // 如果没找到，返回第一个非"组织架构"的节点
    if (node.name && node.name !== "组织架构") {
      return node;
    }

    // 递归查找子节点
    if (node.children) {
      for (const child of node.children) {
        const found = findCompanyNode(child);
        if (found) return found;
      }
    }
    return null;
  }, []);

  /**
   * 获取组织架构数据
   */
  const fetchOrgData = useCallback(async () => {
    try {
      setLoading(true);
      console.log('OrganizationStructure: 开始获取组织数据...');
      const data = await getAllOrganizations();
      console.log('OrganizationStructure: 获取到的原始数据:', data);

      // 处理数据结构，确保children字段存在
      const processNode = (node) => ({
        ...node,
        children: (node.children || []).map(processNode)
      });
      const processedData = processNode(data);
      console.log('处理后的数据:', processedData);
      setOrgData(processedData);

      // 递归获取所有节点ID（内部函数）
      const getAllNodeIds = (node) => {
        let ids = [node.id];
        if (node.children?.length > 0) {
          node.children.forEach(child => {
            ids = [...ids, ...getAllNodeIds(child)];
          });
        }
        return ids;
      };

      // 展开所有节点
      const allIds = getAllNodeIds(processedData);
      setExpandedNodes(new Set(allIds));
      console.log('展开的节点IDs:', allIds);

      // 查找公司根节点或第一个有效节点（内部函数）
      const findCompanyNode = (node) => {
        // 首先尝试查找特定的公司节点
        if (node.name === "成都XX有限公司") return node;

        // 如果没找到，返回第一个非"组织架构"的节点
        if (node.name && node.name !== "组织架构") {
          return node;
        }

        // 递归查找子节点
        if (node.children) {
          for (const child of node.children) {
            const found = findCompanyNode(child);
            if (found) return found;
          }
        }
        return null;
      };

      // 默认选中公司节点
      const companyNode = findCompanyNode(processedData);
      console.log('找到的默认选中节点:', companyNode);
      if (companyNode) {
        setSelectedNode(companyNode);
        console.log('已设置默认选中节点:', companyNode.name);
      } else {
        console.log('未找到默认选中节点');
      }

      setError(null);
    } catch (err) {
      setError(err.message);
      console.error('获取组织数据出错:', err);
    } finally {
      setLoading(false);
    }
  }, []); // 无依赖，确保只创建一次

  /**
   * 显示成功消息
   */
  const showSuccess = (msg) => {
    setTimeout(() => {
      messageApi.success(msg);
    }, 0);
  };

  /**
   * 显示错误消息
   */
  const showError = (msg) => {
    setTimeout(() => {
      messageApi.error(msg);
    }, 0);
  };
  // 组件初始化时获取数据
  useEffect(() => {
    // 只在当前路由是组织架构页面时才执行初始化
    if (location.pathname !== '/hr-management/organization') {
      return;
    }

    console.log('OrganizationStructure: useEffect 触发，开始初始化');
    fetchOrgData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]); // 依赖路径变化

  // 加载状态渲染
  if (loading) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <Spin size="large" tip="加载组织数据中..." />
        </div>
      </PageContainer>
    );
  }

  // 错误状态渲染
  if (error) {
    return (
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
          <Empty
            description={error}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={fetchOrgData}>
              重试
            </Button>
          </Empty>
        </div>
      </PageContainer>
    );
  }

  // 数据为空时不渲染
  if (!orgData) return null;

  /**
   * 渲染组织架构树节点
   */
  const renderOrgNode = (node, level = 0) => {
    if (!node) return null;

    const isExpanded = expandedNodes.has(node.id);
    const isSelected = selectedNode?.id === node.id;
    const hasChildren = node.children?.length > 0;
    const isUndeletable = node.name === "组织架构" || level === 0;

    return (
      <div key={node.id} className="ml-6">
        <div
          className={`flex items-center p-2 rounded-lg cursor-pointer ${
            isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'
          }`}
          onClick={() => setSelectedNode(node)}
        >
          {/* 展开/收起按钮 */}
          {hasChildren && (
            <button
              className="p-1 hover:bg-gray-200 rounded-full mr-1"
              onClick={(e) => {
                e.stopPropagation();
                toggleNode(node.id);
              }}
            >
              {isExpanded ? (
                <DownOutlined style={{ fontSize: '14px' }} />
              ) : (
                <RightOutlined style={{ fontSize: '14px' }} />
              )}
            </button>
          )}

          {/* 节点信息 */}
          <div className="flex-1">
            <div className="font-medium">{node.name}</div>
            <div className="text-sm text-gray-500">{node.position}</div>
          </div>

          {/* 编辑模式下的操作按钮 */}
          {editMode && !isUndeletable && (
            <button
              className="p-1 hover:bg-gray-200 rounded-full text-red-600"
              onClick={(e) => handleDeleteDepartment(node.id, e)}
            >
              <DeleteOutlined style={{ fontSize: '14px' }} />
            </button>
          )}
        </div>

        {/* 子节点 */}
        {isExpanded && hasChildren && (
          <div className="border-l border-gray-200 ml-3">
            {node.children.map(child => renderOrgNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  /**
   * 验证表单数据
   */
  const validateForm = () => {
    const newErrors = { name: '', position: '' };
    let hasError = false;

    if (!newDepartment.name) {
      newErrors.name = '请输入组织名称!';
      hasError = true;
    }
    if (!newDepartment.position) {
      newErrors.position = '请输入职责!';
      hasError = true;
    }

    setFormErrors(newErrors);
    return !hasError;
  };

  /**
   * 检查组织名称是否已存在
   */
  const checkDepartmentNameExists = (node, name) => {
    if (node.name === name) return true;
    if (node.children?.length > 0) {
      return node.children.some(child => checkDepartmentNameExists(child, name));
    }
    return false;
  };

  /**
   * 递归获取所有组织的扁平化列表
   */
  const getAllOrganizationOptions = (node, options = []) => {
    if (!node) return options;

    // 添加当前节点（排除根节点"组织架构"）
    if (node.name && node.name !== "组织架构") {
      options.push({
        id: node.id,
        name: node.name,
        level: 0 // 可以后续添加层级显示
      });
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        getAllOrganizationOptions(child, options);
      });
    }

    return options;
  };

  /**
   * 处理添加组织
   */
  const handleAddDepartment = async () => {
    // 表单验证
    if (!validateForm()) return;

    try {
      // 使用当前已有的 orgData 进行名称检查，避免重复请求
      if (checkDepartmentNameExists(orgData, newDepartment.name)) {
        showError('组织已存在');
        return;
      }

      // 准备请求数据
      const requestData = {
        ...newDepartment,
        duty: newDepartment.position,
        parentId: selectedParentId || 0,
        children: []
      };

      await createDepartment(requestData);
      await fetchOrgData();

      // 重置表单并关闭弹窗
      setNewDepartment({
        name: '',
        position: '',
        duty: '',
        description: '',
        children: []
      });
      setSelectedParentId(null);
      setShowAddDepartmentModal(false);
      showSuccess('添加成功');

    } catch (err) {
      console.error('添加组织出错:', err);
      showError(err.message);
    }
  };

  /**
   * 处理删除组织
   */
  const handleDeleteDepartment = async (nodeId, e) => {
    e.stopPropagation();

    try {
      // 检查组织下是否有员工
      const employeesData = await checkDepartmentEmployees(nodeId);

      if (employeesData?.content?.length > 0) {
        showError("该组织下有人员不可删除");
        return;
      }

      // 显示删除确认对话框
      setShowDeleteConfirmDialog(true);
      setDeletingNodeId(nodeId);
    } catch (err) {
      console.error('检查组织员工出错:', err);
      showError(err.message);
    }
  };

  /**
   * 确认删除组织
   */
  const handleConfirmDelete = async () => {
    try {
      await deleteDepartment(deletingNodeId);
      await fetchOrgData();

      // 清除相关状态
      if (selectedNode?.id === deletingNodeId) {
        setSelectedNode(null);
      }

      const newExpandedNodes = new Set(expandedNodes);
      newExpandedNodes.delete(deletingNodeId);
      setExpandedNodes(newExpandedNodes);

      setShowDeleteConfirmDialog(false);
      setDeletingNodeId(null);
      showSuccess('删除成功');

    } catch (err) {
      console.error('删除组织出错:', err);
      showError(err.message);
    }
  };

  /**
   * 打开岗位编辑对话框
   */
  const handleOpenPositionDialog = (node) => {
    setSelectedNode(node);
    setPositionForm({
      name: node.name,
      description: node.description || '',
      duty: node.duty || '',
      permissions: node.permissions || []
    });
    setShowPositionDialog(true);
  };

  /**
   * 保存岗位信息
   */
  const handleSavePosition = async () => {
    if (!positionForm.name || !positionForm.duty) {
      showError('请填写岗位名称和职责');
      return;
    }

    try {
      const updateData = {
        id: selectedNode.id,
        name: positionForm.name,
        position: selectedNode.position,
        description: positionForm.description,
        duty: positionForm.duty,
        parentId: selectedNode.parentId,
        permissions: positionForm.permissions
      };

      await updateDepartment(selectedNode.id, updateData);
      await fetchOrgData();

      setSelectedNode({ ...selectedNode, ...updateData });
      setShowPositionDialog(false);
      showSuccess('更新成功');

    } catch (err) {
      console.error('更新岗位信息出错:', err);
      showError(err.message);
    }
  };

  /**
   * 显示添加组织弹窗
   */
  const handleShowAddDepartmentModal = async () => {
    try {
      // 重置表单数据
      setNewDepartment({
        name: '',
        position: '',
        duty: '',
        description: '',
        children: []
      });
      setFormErrors({ name: '', position: '' });

      // 设置默认的上级组织为当前选中的节点
      setSelectedParentId(selectedNode?.id || null);

      setShowAddDepartmentModal(true);
    } catch (err) {
      console.error('获取上级组织数据出错:', err);
      showError(err.message);
    }
  };



  return (
    <PageContainer>
      {contextHolder}

      <Layout style={{ height: '100%', background: '#f5f5f5' }}>
        {/* 左侧：组织架构树 */}
        <Sider width="50%" style={{ background: '#fff', marginRight: 16 }}>
          <Card
            title={
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={4} style={{ margin: 0 }}>组织架构</Title>
                </Col>
                <Col>
                  <Space>
                    <Button
                      type={editMode ? "primary" : "default"}
                      icon={<EditOutlined />}
                      onClick={() => setEditMode(!editMode)}
                    >
                      {editMode ? '完成' : '编辑'}
                    </Button>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={handleShowAddDepartmentModal}
                    >
                      添加组织
                    </Button>
                  </Space>
                </Col>
              </Row>
            }
            style={{ height: '100%' }}
            styles={{ body: { height: 'calc(100% - 57px)', padding: '16px' } }}
          >
            {/* 搜索框 */}
            <Search
              placeholder="搜索组织..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{ marginBottom: 16 }}
              prefix={<SearchOutlined />}
            />

            {/* 组织树 */}
            <div style={{ height: 'calc(100% - 48px)', overflowY: 'auto' }}>
              {renderOrgNode(orgData)}
            </div>
          </Card>
        </Sider>

        {/* 右侧：详情面板 */}
        <Content>
          <Card style={{ height: '100%' }} styles={{ body: { height: '100%', overflowY: 'auto' } }}>
            {selectedNode ? (
              <>
                {/* 节点基本信息 */}
                <Row justify="space-between" align="top" style={{ marginBottom: 24 }}>
                  <Col>
                    <Title level={3} style={{ marginBottom: 8 }}>{selectedNode.name}</Title>
                    <Text type="secondary">{selectedNode.position}</Text>
                  </Col>
                  {editMode && (
                    <Col>
                      <Button
                        type="default"
                        icon={<EditOutlined />}
                        onClick={() => handleOpenPositionDialog(selectedNode)}
                      >
                        编辑岗位
                      </Button>
                    </Col>
                  )}
                </Row>

                {/* 详细信息 */}
                <Descriptions column={1} bordered>
                  <Descriptions.Item label="岗位描述">
                    {selectedNode.description || '暂无描述'}
                  </Descriptions.Item>
                  <Descriptions.Item label="职责">
                    {selectedNode.duty || '暂无职责说明'}
                  </Descriptions.Item>
                  <Descriptions.Item label="权限范围">
                    <Row gutter={[16, 16]}>
                      {['人事管理', '预算管理', '项目管理', '报表查看'].map((permission, index) => {
                        const descriptions = [
                          '可查看和编辑组织员工信息',
                          '可审批组织内的预算申请',
                          '可创建和管理组织项目',
                          '可查看组织相关的所有报表'
                        ];
                        return (
                          <Col span={12} key={permission}>
                            <Card size="small">
                              <Text strong>{permission}</Text>
                              <br />
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {descriptions[index]}
                              </Text>
                            </Card>
                          </Col>
                        );
                      })}
                    </Row>
                  </Descriptions.Item>
                </Descriptions>
              </>
            ) : (
              /* 未选中状态 */
              <Empty
                image={<UserOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
                description="请选择左侧的组织或岗位查看详细信息"
              />
            )}
          </Card>
        </Content>
      </Layout>

      {/* 岗位编辑对话框 */}
      <Modal
        title="编辑岗位信息"
        open={showPositionDialog}
        centered
        onCancel={() => setShowPositionDialog(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowPositionDialog(false)}>
            取消
          </Button>,
          <Button key="save" type="primary" onClick={handleSavePosition}>
            保存
          </Button>
        ]}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item label="岗位名称" required>
            <Input
              value={positionForm.name}
              onChange={(e) => setPositionForm({ ...positionForm, name: e.target.value })}
              placeholder="请输入岗位名称"
            />
          </Form.Item>

          <Form.Item label="岗位描述">
            <TextArea
              value={positionForm.description}
              onChange={(e) => setPositionForm({ ...positionForm, description: e.target.value })}
              rows={3}
              placeholder="请输入岗位描述"
            />
          </Form.Item>

          <Form.Item label="职责" required>
            <TextArea
              value={positionForm.duty}
              onChange={(e) => setPositionForm({ ...positionForm, duty: e.target.value })}
              rows={3}
              placeholder="请输入职责"
            />
          </Form.Item>

          <Form.Item label="权限范围">
            <Space direction="vertical">
              {['人事管理', '预算管理', '项目管理'].map(permission => (
                <label key={permission} style={{ display: 'flex', alignItems: 'center' }}>
                  <input
                    type="checkbox"
                    checked={positionForm.permissions.includes(permission)}
                    onChange={(e) => {
                      const newPermissions = e.target.checked
                        ? [...positionForm.permissions, permission]
                        : positionForm.permissions.filter(p => p !== permission);
                      setPositionForm({ ...positionForm, permissions: newPermissions });
                    }}
                    style={{ marginRight: 8 }}
                  />
                  {permission}
                </label>
              ))}
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加组织对话框 */}
      <Modal
        title="添加组织"
        open={showAddDepartmentModal}
        centered
        onCancel={() => {
          setShowAddDepartmentModal(false);
          setSelectedParentId(null);
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setShowAddDepartmentModal(false);
            setSelectedParentId(null);
          }}>
            取消
          </Button>,
          <Button key="add" type="primary" onClick={handleAddDepartment}>
            添加
          </Button>
        ]}
        width={600}
      >
        <Form layout="vertical">
          {/* 上级组织选择 */}
          <Form.Item label="上级组织">
            <Select
              value={selectedParentId}
              onChange={setSelectedParentId}
              placeholder="请选择上级组织"
              allowClear
              showSearch
              filterOption={(input, option) =>
                option?.children?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {orgData && getAllOrganizationOptions(orgData).map(org => (
                <Select.Option key={org.id} value={org.id}>
                  {org.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* 组织名称 */}
          <Form.Item
            label="组织名称"
            required
            validateStatus={formErrors.name ? 'error' : ''}
            help={formErrors.name}
          >
            <Input
              value={newDepartment.name}
              onChange={(e) => {
                setNewDepartment({ ...newDepartment, name: e.target.value });
                if (e.target.value) {
                  setFormErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              placeholder="请输入组织名称"
            />
          </Form.Item>

          {/* 职责 */}
          <Form.Item
            label="职责"
            required
            validateStatus={formErrors.position ? 'error' : ''}
            help={formErrors.position}
          >
            <Input
              value={newDepartment.position}
              onChange={(e) => {
                setNewDepartment({ ...newDepartment, position: e.target.value });
                if (e.target.value) {
                  setFormErrors(prev => ({ ...prev, position: '' }));
                }
              }}
              placeholder="请输入职责"
            />
          </Form.Item>

          {/* 组织描述 */}
          <Form.Item label="组织描述">
            <TextArea
              value={newDepartment.description}
              onChange={(e) => setNewDepartment({ ...newDepartment, description: e.target.value })}
              rows={4}
              placeholder="请输入组织描述..."
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除确认对话框 */}
      <Modal
        title="确认删除"
        open={showDeleteConfirmDialog}
        centered
        onCancel={() => {
          setShowDeleteConfirmDialog(false);
          setDeletingNodeId(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setShowDeleteConfirmDialog(false);
              setDeletingNodeId(null);
            }}
          >
            取消
          </Button>,
          <Button
            key="delete"
            type="primary"
            danger
            onClick={handleConfirmDelete}
          >
            确认删除
          </Button>
        ]}
        width={300}
      >
        <p>确定要删除该数据吗？</p>
      </Modal>
    </PageContainer>
  );
});