import axios from 'axios';

import { fetchData } from './fetch';

// 项目相关接口
export const projectApi = {
  // 获取项目列表（分页）
  getProjectList: (params) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/by-client/page?${new URLSearchParams(params)}`).then(res => res.json()),

  // 获取单个项目详情
  getProjectDetail: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/one/${projectId}`).then(res => res.json()),

  // 创建项目
  createProject: (projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 更新项目
  updateProject: (projectId, projectData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/update/${projectId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    }).then(res => res.json()),

  // 删除项目
  deleteProject: (projectId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/projects/delete/${projectId}`, {
      method: 'GET'
    }).then(res => res.json()),
};

// 客户相关接口
export const clientApi = {
  // 获取客户列表
  getClientList: () => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/list`).then(res => res.json()),

  // 搜索客户
  searchClients: (keyword) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/search?keyword=${encodeURIComponent(keyword)}`).then(res => res.json()),

  // 创建客户
  createClient: (clientData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    }).then(res => res.json()),

  // 更新客户
  updateClient: (clientId, clientData) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/update/${clientId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(clientData)
    }).then(res => res.json()),

  // 删除客户
  deleteClient: (clientId) => 
    fetch(`${fetchData["STAFF_URL"]}/api/clients/delete/${clientId}`, {
      method: 'GET'
    }).then(res => res.json()),
}; 