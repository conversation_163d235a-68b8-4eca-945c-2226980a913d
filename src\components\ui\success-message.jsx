import React from 'react';
import { Cross2Icon } from '@radix-ui/react-icons';

const SuccessMessage = ({ message, onClose }) => {
  if (!message) return null;
  
  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg flex items-center shadow-lg">
        <span>{message}</span>
        <button
          onClick={onClose}
          className="ml-4 text-green-700 hover:text-green-900"
        >
          <Cross2Icon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default SuccessMessage; 