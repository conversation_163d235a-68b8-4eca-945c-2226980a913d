import axios from 'axios';

import { fetchData } from './fetch';


// 需求管理相关接口
export const requirementsApi = {
  // 获取根需求列表
  getRootRequirements: (projectId, page, size) => {
    const params = new URLSearchParams({ projectId, page, size });
    return fetch(`${fetchData["PROJECT_URL"]}/api/requirements/root?${params}`).then(res => res.json());
  },

  // 获取子需求列表
  getChildRequirements: (parentId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/requirements/byParentId/${parentId}`).then(res => res.json()),

  // 获取单个需求详情
  getRequirementDetail: (requirementId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/requirements/one/${requirementId}`).then(res => res.json()),

  // 创建需求
  createRequirement: (requirementData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/requirements/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requirementData)
    }).then(res => res.json()),

  // 更新需求
  updateRequirement: (requirementId, requirementData) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/requirements/update/${requirementId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requirementData)
    }).then(res => res.json()),

  // 删除需求
  deleteRequirement: (requirementId) => 
    fetch(`${fetchData["PROJECT_URL"]}/api/requirements/delete/${requirementId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(res => res.json()),

  // 搜索需求
  searchRequirements: (projectId, page, size, searchParams) => {
    const params = new URLSearchParams({
      projectId,
      page,
      size,
      ...searchParams
    });
    return fetch(`${fetchData["PROJECT_URL"]}/api/requirements/all?${params}`).then(res => res.json());
  }
};

// 项目相关接口
export const projectApi = {
  // 获取项目列表
  getProjectList: (searchName = '') => {
    const params = new URLSearchParams({
      name: searchName
    });
    return fetch(`${fetchData["STAFF_URL"]}/api/projects/list?${params}`).then(res => res.json());
  }
}; 